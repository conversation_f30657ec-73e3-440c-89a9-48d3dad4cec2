__pycache__/
*.py[cod]
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
*.manifest
pip-log.txt
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
*.mo
*.log
local_settings.py
db.sqlite3
instance/
.pybuilder/
profile_default/
.pdm.toml
.pdm-python
celerybeat-schedule
.env
.venv
env/
venv/
ENV/
env.bak/
.spyderproject
.mypy_cache/
.dmypy.json
.vscode/


#file

flofin/management/commands/t.py
flofin/management/commands/t2.py
/testt
