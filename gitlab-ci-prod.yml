# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

stages:
  - deploy

variables:
  PROD_BRANCH: main

.deploy_template: &deploy_template
  stage: deploy
  image: alpine:latest
  tags:
    - rcvr-production-runner
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H "$SERVER_HOST" >> ~/.ssh/known_hosts

# Deploy to Server 2
deploy_to_server:
  <<: *deploy_template
  variables:
    SSH_KEY: "$SSH_PRIVATE_KEY_2"
    SSH_USER: root
    REPO_DIR: /home/<USER>/flofin
    PM2_SERVICE: 0
    SERVER_HOST: **************
  script:
    - ssh "$SSH_USER@$SERVER_HOST" "
        cd '$REPO_DIR' &&
        git switch '$PROD_BRANCH' &&
        git pull origin '$PROD_BRANCH' &&
        sudo systemctl restart flofin-staging-backend"
