repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: check-yaml
        args: ["--unsafe"]
      - id: check-toml
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: mixed-line-ending
      - id: detect-aws-credentials
        args: ["--allow-missing-credentials"]
      - id: detect-private-key
      - id: check-added-large-files
        args: ["--maxkb=750"]
      - id: debug-statements
  - repo: https://github.com/ambv/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3
        args: ["--line-length", "170"]
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
        language_version: python3.10
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        args:
          [
            "--max-line-length",
            "170",
            "--extend-ignore",
            "E203",
            "--extend-ignore",
            "W605",
            "--extend-ignore",
            "E722",
            "--ignore",
            "E203,W605,E722,W503,E266,E262",
            "--exclude",
            ".git,__pycache__,docs/source/conf.py,old,build,dist,data/models.py,src/settings.py",
          ]
        exclude: "^.venv/"
  - repo: https://github.com/myint/autoflake
    rev: v1.4
    hooks:
      - id: autoflake
        args:
          [
            "--in-place",
            "--remove-unused-variables",
            "--remove-all-unused-imports",
            "--ignore-init-module-imports",
            "--exclude=tests/*",
          ]
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.7.1
    hooks:
      - id: prettier
        types_or: [markdown, yaml]
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: "v1.7.1"
    hooks:
      - id: mypy
        additional_dependencies:
          [
            types-PyYAML==6.0.12.11,
            "types-requests",
            "sqlmodel",
            "types-Markdown",
            types-tzlocal,
            requests,
          ]
        args: ["--check-untyped-defs", "--ignore-missing-imports"]
        exclude: |
          (?x)(
            ^data.*|
            ^flofin.*|
          )
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.4
    hooks:
      - id: codespell
        additional_dependencies:
          - tomli
        args: ["-L", "Verifi,USAG,ois,verifi", "-x", "src/settings.py"]
