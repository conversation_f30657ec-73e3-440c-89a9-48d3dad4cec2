from django.db import models
from django.utils import timezone

from data.models import ClientLoginInformation, ClientsInformation
from user.models import Users


class CardBinLookup(models.Model):
    card_bin = models.IntegerField(primary_key=True)
    card_issuer_name = models.CharField(max_length=255, blank=True, null=True)
    prepaid_card = models.IntegerField(blank=True, null=True)
    issuer_country = models.CharField(max_length=255, blank=True, null=True)
    card_brand = models.CharField(max_length=255, blank=True, null=True)
    card_type = models.CharField(max_length=255, blank=True, null=True)
    card_subtype = models.CharField(max_length=255, blank=True, null=True)
    end_col = models.IntegerField(blank=True, null=True, default=0)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "FloFin_card_bin_lookup"


class OfferFlow(models.Model):
    flow_id = models.AutoField(primary_key=True)
    flow_brand_name = models.CharField(max_length=255, blank=True, null=True)

    client_crm_setting = models.CharField(max_length=255, blank=True, null=True)
    client = models.ForeignKey(
        ClientsInformation, models.DO_NOTHING, blank=True, null=True
    )
    client_login = models.ForeignKey(
        ClientLoginInformation, models.DO_NOTHING, blank=True, null=True
    )

    flow_descriptor1 = models.CharField(max_length=255, blank=True, null=True)
    flow_descriptor2 = models.CharField(max_length=255, blank=True, null=True)
    flow_descriptor3 = models.CharField(max_length=255, blank=True, null=True)
    flow_descriptor4 = models.CharField(max_length=255, blank=True, null=True)
    flow_descriptor5 = models.CharField(max_length=255, blank=True, null=True)
    first_product_match = models.CharField(max_length=230, blank=True, null=True)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)
    status = models.CharField(max_length=255, blank=True, null=True, default="Active")

    class Meta:
        db_table = "FloFin_offer_flow"


class OfferFlowSteps(models.Model):
    offer_flow_step_id = models.AutoField(primary_key=True)

    flow = models.ForeignKey(OfferFlow, models.DO_NOTHING, blank=True, null=True)
    flow_step_number = models.IntegerField(blank=True, null=True)
    continuity_enabled = models.IntegerField(blank=True, null=True, default=1)
    initial_delay_hours = models.IntegerField(blank=True, null=True)
    rebill_delay_hours = models.IntegerField(blank=True, null=True)
    cont_cycle_1_delay_hours = models.IntegerField(blank=True, null=True)
    cont_cycle_frequency = models.IntegerField(blank=True, null=True)
    rebill_price = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    cont_cycle_1_price = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    cont_cycle_2_price = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    cont_cycle_3_price = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    cont_cycle_4_price = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    cont_cycle_5_price = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    cont_cycle_6plus_price = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    initial_product_cost = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    continuity_product_cost = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    status = models.CharField(max_length=255, blank=True, null=True, default="Active")
    type = models.CharField(max_length=255, blank=True)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "FloFin_offer_flow_steps"


class OfferProductDetail(models.Model):
    id = models.AutoField(primary_key=True)

    offer_flow_step = models.ForeignKey(
        OfferFlowSteps, models.DO_NOTHING, blank=True, null=True
    )
    cycle_number = models.IntegerField(blank=True, null=True)
    cycle_descriptor = models.CharField(max_length=255, blank=True, null=True)
    step_price = models.CharField(max_length=255, blank=True, null=True)
    price_valid_start = models.DateTimeField(blank=True, null=True)
    status = models.IntegerField()
    product_match = models.CharField(max_length=230, blank=True, null=True)
    backlogged_cycle_flag = models.IntegerField(blank=True, null=True)
    unique_product_match = models.CharField(
        unique=True, max_length=230, blank=True, null=True
    )
    prior_update_count = models.IntegerField(null=False, default=-1)
    historical_unique = models.CharField(max_length=50, blank=True, null=True)
    product_cost = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    shipping_cost = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    status = models.CharField(max_length=255, blank=True, null=True, default="Active")

    cpa_cost = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True, default=0
    )
    misc_cost = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True, default=0
    )

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "FloFin_offer_product_detail"


class OrderImport(models.Model):
    id = models.AutoField(primary_key=True)
    client_login = models.ForeignKey(
        ClientLoginInformation, blank=True, null=True, on_delete=models.CASCADE
    )

    order_id = models.CharField(max_length=255, blank=True, null=True)
    sub_order_id = models.CharField(unique=True, max_length=45, blank=True, null=True)
    transaction_date = models.DateTimeField(blank=True, null=True)
    import_product_identifier_1 = models.CharField(
        max_length=255, blank=True, null=True
    )
    import_product_identifier_2 = models.CharField(
        max_length=255, blank=True, null=True
    )
    import_product_identifier_3 = models.CharField(
        max_length=255, blank=True, null=True
    )
    product_match = models.CharField(max_length=230, blank=True, null=True)
    is_test = models.IntegerField(blank=True, null=True)
    pre_tax_order_total = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    price_point = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    order_status = models.IntegerField(blank=True, null=True)
    status_response = models.CharField(max_length=255, blank=True, null=True)
    vendorid1 = models.CharField(max_length=255, blank=True, null=True)
    vendorid2 = models.CharField(max_length=255, blank=True, null=True)
    vendorid3 = models.CharField(max_length=255, blank=True, null=True)
    vendorid4 = models.CharField(max_length=255, blank=True, null=True)
    vendorid5 = models.CharField(max_length=255, blank=True, null=True)
    campaign = models.CharField(max_length=255, blank=True, null=True)
    is_scrubbed = models.IntegerField(blank=True, null=True)
    payment_network = models.CharField(max_length=255, blank=True, null=True)
    customer_id = models.CharField(max_length=255, blank=True, null=True)
    cc_bin = models.IntegerField(blank=True, null=True)
    card_last4 = models.IntegerField(blank=True, null=True)
    merchantId = models.CharField(max_length=255, blank=True, null=True)  # merchantId
    merchant_descriptor = models.CharField(
        max_length=255, blank=True, null=True
    )  # merchantDescriptor
    mid = models.CharField(max_length=255, blank=True, null=True)  # midNumber
    pr_global_grouping_field1 = models.CharField(
        max_length=255, blank=True, null=True
    )  # campaignCategoryName
    card_type_flag1 = models.CharField(
        max_length=255, blank=True, null=True
    )  # paySource
    cc_is_prepaid = models.IntegerField(blank=True, null=True, default=0)  # is_prepaid
    currency = models.CharField(max_length=3, blank=True, null=True)
    is_3ds_verified = models.IntegerField(
        blank=True, null=True
    )  # Field name made lowercase. # 3DTxnResult
    transaction_type = models.CharField(
        max_length=255, blank=True, null=True
    )  # txnType

    offer_product = models.ForeignKey(
        OfferProductDetail,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    cycle_num_lookup = models.IntegerField(blank=True, null=True)
    ancestor_id = models.IntegerField(blank=True, null=True)
    ancestor_date = models.DateTimeField(blank=True, null=True)

    trans_att_num = models.IntegerField(blank=True, null=True)

    score = models.DecimalField(max_digits=10, decimal_places=3, blank=True, null=True)
    retention = models.IntegerField(blank=True, null=True)
    retention_gt = models.IntegerField(blank=True, null=True)

    card_infor = models.ForeignKey(
        CardBinLookup, models.DO_NOTHING, blank=True, null=True
    )

    ancestor_vendorid1 = models.CharField(max_length=255, blank=True, null=True)
    ancestor_vendorid2 = models.CharField(max_length=255, blank=True, null=True)

    blacklisted = models.IntegerField(
        default=0
    )  # 0: not blacklisted, 1: user blacklisted, 2: crm blacklisted
    refunded = models.IntegerField(
        default=0
    )  # 0: not refunded, 1: refunded, 2: crm refunded

    crm_type = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "FloFin_order_import"

    @property
    def trans_date_formatted(self):
        return self.transaction_date.strftime("%Y-%m-%d")


class OrderImportTemp(models.Model):
    id = models.AutoField(primary_key=True)
    client_login = models.ForeignKey(
        ClientLoginInformation, blank=True, null=True, on_delete=models.CASCADE
    )
    is_test = models.IntegerField(blank=True, null=True)
    cycle_num_lookup = models.IntegerField(blank=True, null=True)
    flow_step = models.ForeignKey(
        OfferFlowSteps, models.DO_NOTHING, blank=True, null=True
    )
    flow_brand = models.ForeignKey(OfferFlow, models.DO_NOTHING, blank=True, null=True)
    card_brand = models.CharField(max_length=255, blank=True, null=True)
    card_type = models.CharField(max_length=255, blank=True, null=True)
    transaction_date = models.DateTimeField(blank=True, null=True)
    order_status = models.IntegerField(blank=True, null=True)
    payment_network = models.CharField(max_length=255, blank=True, null=True)
    merchantId = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = "FloFin_order_import_temp"

    @property
    def trans_date_formatted(self):
        return self.transaction_date.strftime("%Y-%m-%d")


class OrderUpdate(models.Model):
    id = models.AutoField(primary_key=True)
    client_login = models.ForeignKey(
        ClientLoginInformation, blank=True, null=True, on_delete=models.CASCADE
    )

    order_id = models.CharField(max_length=255, blank=True, null=True)
    sub_order_id = models.CharField(max_length=45, blank=True, null=True)
    parent_id = models.CharField(max_length=255, blank=True, null=True)
    update_date = models.DateTimeField(blank=True, null=True)
    order_update_type = models.IntegerField(blank=True, null=True)
    update_reason = models.CharField(max_length=250, blank=True, null=True)
    revenue_update = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    import_product_identifier_1 = models.CharField(
        max_length=255, blank=True, null=True
    )
    product_match = models.CharField(max_length=230, blank=True, null=True)
    customer_id = models.CharField(max_length=255, blank=True, null=True)
    update_status = models.IntegerField(blank=True, null=True)
    status_response = models.CharField(max_length=255, blank=True, null=True)
    is_3ds_verified = models.IntegerField(blank=True, null=True)
    c_parent_orderid = models.CharField(max_length=255, blank=True, null=True)
    c_parent_suborderid = models.CharField(max_length=255, blank=True, null=True)
    order_import = models.ForeignKey(
        OrderImport, models.DO_NOTHING, blank=True, null=True
    )
    g_updated = models.CharField(max_length=255, blank=True, null=True)
    misc_cost_1 = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )

    crm_type = models.CharField(
        max_length=255, blank=True
    )  # Type of update from CRM (e.g., "KONNEKTIVE", "STICKY", etc.)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "FloFin_order_update"


############################################################################################################


class HomeCLVReport(models.Model):
    id = models.AutoField(primary_key=True)
    clientID = models.CharField(max_length=255, null=True, blank=True)
    client = models.CharField(max_length=255, null=True, blank=True)

    transaction_date = models.DateTimeField(blank=True, null=True)
    ancestor_date = models.DateTimeField(blank=True, null=True)
    count_order = models.IntegerField(blank=True, null=True)

    # flow_brand_name = models.CharField(max_length=255, blank=True, null=True)
    # flow_step_number = models.IntegerField(null=True, blank=True)
    cycle_num = models.IntegerField(null=True, blank=True)

    flow_brand = models.ForeignKey(OfferFlow, models.DO_NOTHING, blank=True, null=True)
    flow_step = models.ForeignKey(
        OfferFlowSteps, models.DO_NOTHING, blank=True, null=True
    )

    network = models.CharField(max_length=255, blank=True, null=True)  # vendorid1
    pub = models.CharField(
        max_length=255, blank=True, null=True
    )  # VendorID1 + VendorID2
    merchantId = models.CharField(max_length=255, null=True, blank=True)

    post_tax_order_total = models.FloatField(null=True)
    total_sales = models.IntegerField(blank=True, null=True)
    total_unique_sale = models.IntegerField(blank=True, null=True)

    price_point = models.FloatField(null=True)
    card_brand = models.CharField(max_length=255, null=True, blank=True)
    card_type = models.CharField(max_length=255, blank=True, null=True)
    card_issuer = models.CharField(max_length=255, blank=True, null=True)

    total_foreign_card = models.IntegerField(blank=True, null=True)
    total_3ds = models.IntegerField(blank=True, null=True)
    transactions = models.IntegerField(null=True)
    transactions_amount = models.FloatField(null=True)

    refunds = models.IntegerField(null=True)
    refunds_amount = models.FloatField(null=True)
    chargebacks = models.IntegerField(null=True)
    chargebacks_amount = models.FloatField(null=True)
    voids = models.IntegerField(null=True)
    voids_amount = models.FloatField(null=True)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "FloFin_Report_Home_CLV"


class AlertThreshold(models.Model):
    id = models.AutoField(primary_key=True)

    name = models.CharField(max_length=255, blank=True, null=True)
    dimension = models.CharField(
        max_length=255,
        choices=(
            ("client", "Client"),
            ("network", "Network"),
            ("affiliate", "Affiliate"),
            ("flow_brand_name", "Flow Title"),
            ("card_brand", "Card Brand"),
            ("card_type", "Card Type"),
            ("merchantId", "MID"),
            ("All", "All"),
        ),
        blank=True,
        null=True,
    )
    metric = models.CharField(
        max_length=255,
        choices=(
            ("chargeback_rate", "Chargeback Rate"),
            ("refund_rate", "Refund Rate"),
            ("void_rate", "Void Rate"),
            ("foreign_card_rate", "Foreign Transaction Rate"),
            ("repeat_transaction_rate", "Repeat Transaction Rate"),
            ("initial_approval_rate", "Initial Approval Rate"),
        ),
        blank=True,
        null=True,
    )
    value = models.FloatField(blank=True, null=True)
    current_value = models.FloatField(blank=True, null=True)

    client = models.ForeignKey(
        ClientsInformation, models.DO_NOTHING, blank=True, null=True
    )
    dimension_value = models.CharField(max_length=255, blank=True, null=True)

    is_active = models.BooleanField(default=True)
    trigger_frequency = models.CharField(
        max_length=255,
        choices=(
            ("Hourly", "Hourly"),
            ("daily", "Daily"),
            ("weekly", "Weekly"),
            ("monthly", "Monthly"),
            ("quarterly", "Quarterly"),
            ("yearly", "Yearly"),
        ),
        blank=True,
        null=True,
    )
    condition = models.CharField(
        max_length=255,
        choices=((">", ">"), ("<", "<"), (">=", ">="), ("<=", "<=")),
        blank=True,
        null=True,
    )
    lst_email = models.JSONField(blank=True, null=True)
    user = models.ForeignKey(Users, models.DO_NOTHING, blank=True, null=True)
    last_triggered_at = models.DateTimeField(blank=True, null=True, default=None)
    next_trigger_at = models.DateTimeField(blank=True, null=True, default=None)
    error_message = models.TextField(blank=True, null=True, default=None)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "FloFin_alert_threshold"
