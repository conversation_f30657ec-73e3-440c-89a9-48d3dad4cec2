from celery import shared_task
from flofin.source.utils import run_data_flow
from flofin.models import OfferFlow
import logging
from data.models import ClientLoginInformation
from flofin.models import HomeCLVReport, OfferFlowSteps, OfferProductDetail, OrderImport
import datetime
import time 
import os
logger = logging.getLogger(__name__)

@shared_task(name='run_data_flow')
def run_data_flow_task(client_login_id:str, start_date:str, end_date:str, lst_flow_id: list[int] = None):
    try:
        # logger.info("✅ Running data flow task...")
        print("✅ Running data flow task...")
        print(f"➡️  client_login_id: {client_login_id}")
        print(f"➡️  start_date: {start_date}")
        print(f"➡️  end_date: {end_date}")
        print(f"➡️  lst_flow_id: {lst_flow_id}")
        os.makedir(./té)
        if not client_login_id or not start_date or not end_date:
            raise Exception("Missing required parameters")
        
        client_login = ClientLoginInformation.objects.get(id=client_login_id)
        print(f"➡️  client_login: {client_login}")
        
        lst_flow = None

        if lst_flow_id:
            lst_flow = OfferFlow.objects.filter(flow_id__in=lst_flow_id)
            print(f"➡️  lst_flow: {lst_flow} - {lst_flow.count()}")
        time.sleep(100)
        # run_data_flow(client_login, start_date, end_date, lst_flow)
    except Exception as e:
        logger.exception("❌ ERROR in task: %s", str(e))


@shared_task
def process_delete_sale_flow_task(flow_id, client):
    try:
        print(
            f"Start processing delete sale flow {flow_id} by client {client} at {datetime.now()}"
        )
        
        HomeCLVReport.objects.filter(flow_brand_id=flow_id).delete()
        flow_steps = OfferFlowSteps.objects.filter(flow_id=flow_id)
        if flow_steps.exists():
            flow_steps.update(status="Deleted")
            flow_products = OfferProductDetail.objects.filter(
                offer_flow_step__in=flow_steps
            )
            flow_order_imports = OrderImport.objects.filter(offer_product__in=flow_products)
            if flow_order_imports.exists():
                flow_order_imports.update(offer_product=None)
                
        print(
            f"Finished processing delete sale flow {flow_id} by client {client} at {datetime.now()}"
        )
    except Exception as e:
        logger.exception("❌ ERROR in task: %s", str(e))
    

