from rest_framework import serializers


class DateRangeSerializer(serializers.Serializer):
    start_date = serializers.CharField()
    end_date = serializers.CharField()


class NumberRangeSerializer(serializers.Serializer):
    start_number = serializers.IntegerField()
    end_number = serializers.IntegerField()


class TotalChargebacksSerializer(serializers.Serializer):
    volume = serializers.IntegerField()
    value = serializers.FloatField()
    currency = serializers.CharField()
    original_sale_date = DateRangeSerializer(many=False)
    original_sale_range_date = DateRangeSerializer(many=False)


class TotalRefundsAndVoidSerializer(serializers.Serializer):
    volume = serializers.IntegerField()
    value = serializers.FloatField()
    currency = serializers.CharField()
    transaction_date = DateRangeSerializer(many=False)
    transaction_range_date = DateRangeSerializer(many=False)


class TotalTransactionsSerializer(serializers.Serializer):
    volume = serializers.IntegerField()
    value = serializers.FloatField()
    currency = serializers.Char<PERSON>ield()
    cycle_number = NumberRangeSerializer(many=False)
    cycle_range_number = NumberRangeSerializer(many=False)


class TransactionOverviewSerializer(serializers.Serializer):
    total_chargebacks = TotalChargebacksSerializer(many=False)
    total_refunds_and_voids = TotalRefundsAndVoidSerializer(many=False)
    total_transactions = TotalTransactionsSerializer(many=False)


class itemSerializer(serializers.Serializer):
    name = serializers.CharField()
    count = serializers.IntegerField()


class BreakdownByIssuerSerializer(serializers.Serializer):
    name = serializers.CharField()
    items = itemSerializer(many=True)


class getAnalysisBreakdownByIssuerDataResponseSerializer(serializers.Serializer):
    general_overview = TransactionOverviewSerializer(many=False)
    breakdown_by_issuer = BreakdownByIssuerSerializer(many=True)


class getAnalysisBreakdownByIssuerResponseSerializer(serializers.Serializer):
    message = serializers.CharField(
        default="Get Marketing Source With Most Chargeback - Breakdown By Issuer successfully."
    )
    data = getAnalysisBreakdownByIssuerDataResponseSerializer(many=False)


class GetDataUnsuccessfulResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get data unsuccessfully.")
    exception = serializers.CharField(default="")


class inValidMethodResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Invalid request method.")


class getAnalysisChartViewResponseItemSerializer(serializers.Serializer):
    date = serializers.DateField(format="%d/%m/%Y")
    value = serializers.IntegerField()
    volume = serializers.IntegerField()


class ChartViewSerializer(serializers.Serializer):
    name = serializers.CharField()
    iemts = getAnalysisChartViewResponseItemSerializer(many=True)


class getAnalysisChartViewDataResponseSerializer(serializers.Serializer):
    general_overview = TransactionOverviewSerializer(many=False)
    chart_view = ChartViewSerializer(many=True)


class getAnalysisChartViewResponseSerializer(serializers.Serializer):
    message = serializers.CharField(
        default="Get Marketing Source With Most Chargeback - Breakdown By Issuer successfully."
    )
    data = getAnalysisChartViewDataResponseSerializer(many=False)


class ChartLineItemsSerializer(serializers.Serializer):
    number = serializers.IntegerField()
    percent = serializers.FloatField()


class getPricePointResponseDataItemSerializer(serializers.Serializer):
    name = serializers.CharField()
    items = ChartLineItemsSerializer(many=True)


class getPricePointResponseDataSerializer(serializers.Serializer):
    price_point = getPricePointResponseDataItemSerializer(many=True)


class getPricePointResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get price point successfully.")
    data = getPricePointResponseDataSerializer(many=False)


class getCycleResponseDataSerializer(serializers.Serializer):
    cycle = getPricePointResponseDataItemSerializer(many=True)


class getCycleResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get cycle successfully.")
    data = getCycleResponseDataSerializer(many=False)


class getFlowBrandNameResponseDataSerializer(serializers.Serializer):
    flow_brand_name = getPricePointResponseDataItemSerializer(many=False)


class getFlowBrandNameResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get Flow Title successfully.")
    data = getFlowBrandNameResponseDataSerializer(many=False)


class getFlowStepNumberResponseDataSerializer(serializers.Serializer):
    flow_step_number = getPricePointResponseDataItemSerializer(many=False)


class getFlowStepNumberResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get Flow Step successfully.")
    data = getFlowStepNumberResponseDataSerializer(many=False)


class getCardBrandResponseDataSerializer(serializers.Serializer):
    card_brand = getPricePointResponseDataItemSerializer(many=False)


class getCardBrandResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get Card Brand successfully.")
    data = getCardBrandResponseDataSerializer(many=False)


class getCardTypeResponseDataSerializer(serializers.Serializer):
    card_type = getPricePointResponseDataItemSerializer(many=False)


class getCardTypeResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get Card Type successfully.")
    data = getCardTypeResponseDataSerializer(many=False)


class getIssuerResponseDataSerializer(serializers.Serializer):
    issuer = getPricePointResponseDataItemSerializer(many=False)


class getIssuerResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get Issuer successfully.")
    data = getIssuerResponseDataSerializer(many=False)


class get3dsResponseDataSerializer(serializers.Serializer):
    items = getPricePointResponseDataItemSerializer(many=False)


class get3dsResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get 3ds successfully.")
    data = get3dsResponseDataSerializer(many=False)


class get3dsByMidResponseDataSerializer(serializers.Serializer):
    items = getPricePointResponseDataItemSerializer(many=False)


class get3dsByMidResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get 3ds By Mid successfully.")
    data = get3dsByMidResponseDataSerializer(many=False)


class getCLVFilterValueDataResponseSerializer(serializers.Serializer):
    flow_brand_name = serializers.ListField()
    card_brand = serializers.ListField()
    network = serializers.ListField()
    pub = serializers.ListField()
    original_sale_date = DateRangeSerializer(many=False)
    as_of_date = DateRangeSerializer(many=False)
    sales = NumberRangeSerializer(many=False)


class getCLVFilterValueResponseSerializer(serializers.Serializer):
    message = serializers.CharField(
        default="Get Customer Lifetime Value Filter Value successfully."
    )
    data = getCLVFilterValueDataResponseSerializer(many=False)


class CustomerLifetimeValueDataSerializer(serializers.Serializer):
    month = serializers.DateTimeField()
    sale = serializers.IntegerField()
    revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    CPA = serializers.DecimalField(max_digits=10, decimal_places=2)
    refuded_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    voided_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    refuded_voids_percentage = serializers.DecimalField(max_digits=5, decimal_places=2)
    cb_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    cb_percentage = serializers.DecimalField(max_digits=5, decimal_places=2)
    cb_fees = serializers.DecimalField(max_digits=10, decimal_places=2)
    processing_fees = serializers.DecimalField(max_digits=10, decimal_places=2)
    call_center_fees = serializers.DecimalField(max_digits=10, decimal_places=2)
    custom_fee_calc = serializers.DecimalField(max_digits=10, decimal_places=2)
    net_profit = serializers.DecimalField(max_digits=10, decimal_places=2)
    clv = serializers.DecimalField(max_digits=10, decimal_places=2)


class CustomerLifetimeValueBrandSerializer(serializers.Serializer):
    flow_brand_name = serializers.CharField()
    data = CustomerLifetimeValueDataSerializer(many=True)


class getCLVResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get Customer Lifetime Value successfully.")
    data = CustomerLifetimeValueBrandSerializer(many=True)


class ChartItemSerializer(serializers.Serializer):
    date = serializers.DateField()
    count = serializers.FloatField()


class ChartDataSerializer(serializers.Serializer):
    brand = serializers.CharField()
    data = ChartItemSerializer(many=True)


class getClvToolTipsItemDataResponseSerializer(serializers.Serializer):
    flow_brand_name = serializers.CharField()
    month_of_original_sale_date = serializers.DateField()
    total_sales = serializers.DecimalField(max_digits=10, decimal_places=0)
    total_clv = serializers.DecimalField(max_digits=10, decimal_places=2)
    data = ChartDataSerializer(many=True)


class getClvToolTipsResponseSerializer(serializers.Serializer):
    message = serializers.CharField(
        default="Get Customer Lifetime Value Overview Data successfully."
    )
    data = getClvToolTipsItemDataResponseSerializer(many=False)


class getDailySalesRecapDataResponseSerializer(serializers.Serializer):
    date = serializers.DateField()
    sales_count = serializers.IntegerField()
    processed_volume = serializers.DecimalField(max_digits=12, decimal_places=2)


class getDailySalesRecapResponseSerializer(serializers.Serializer):
    message = serializers.CharField(
        default="Daily Sales Recap data retrieved successfully."
    )
    data = getDailySalesRecapDataResponseSerializer(many=True)


class getDailyPerformanceSummaryReportsFilterValueDataResponseSerializer(
    serializers.Serializer
):
    flow_brand_name = serializers.ListField()
    card_brand = serializers.ListField()
    flow_step_number = serializers.ListField()


class getDailyPerformanceSummaryReportsFilterValueResponseSerializer(
    serializers.Serializer
):
    message = serializers.CharField(
        default="Get Customer Lifetime Value Filter Value successfully."
    )
    data = getDailyPerformanceSummaryReportsFilterValueDataResponseSerializer(
        many=False
    )


class TrafficSourcePerformanceDataSerializer(serializers.Serializer):
    flow_brand_name = serializers.CharField()
    flow_step_number = serializers.IntegerField()
    pub = serializers.CharField()
    card_brand = serializers.CharField()
    total_sales = serializers.IntegerField()
    step_sales_processed = serializers.IntegerField()
    approved_volume = serializers.CharField()
    approval_rate = serializers.CharField()


class TrafficSourcePerformanceResponseSerializer(serializers.Serializer):
    message = serializers.CharField(
        default="Traffic Source Performance data retrieved successfully."
    )
    data = TrafficSourcePerformanceDataSerializer(many=True)


class get3dsCoverageReportDataSerializer(serializers.Serializer):
    flow_brand_name = serializers.CharField()
    flow_step_number = serializers.IntegerField()
    card_brand = serializers.CharField()
    total_sales = serializers.IntegerField()
    sales_with_3ds = serializers.IntegerField()
    three_ds_coverage = serializers.FloatField()


class get3dsCoverageReportResponseSerializer(serializers.Serializer):
    message = serializers.CharField(
        default="Traffic Source Performance data retrieved successfully."
    )
    data = get3dsCoverageReportDataSerializer(many=True)


class getCashFlowReportDataSerializer(serializers.Serializer):
    month = serializers.DateField(format="%Y%m")
    sales = serializers.IntegerField()
    cash_inflow = serializers.DecimalField(max_digits=12, decimal_places=2)
    cash_outflow = serializers.DecimalField(max_digits=12, decimal_places=2)
    net_profit = serializers.DecimalField(max_digits=12, decimal_places=2)
    net_profit_per_sale = serializers.IntegerField()


class getCashFlowReportResponseSerializer(serializers.Serializer):
    message = serializers.CharField(
        default="Cash Flow Report data retrieved successfully."
    )
    data = getCashFlowReportDataSerializer(many=True)


class getFlowResponseSerializer(serializers.Serializer):
    flow_id = serializers.IntegerField()
    flow_brand_name = serializers.CharField()
    flow_number_of_steps = serializers.IntegerField()
    client_crm_setting = serializers.CharField()
    flow_descriptor1 = serializers.CharField()
    flow_descriptor2 = serializers.CharField()
    flow_descriptor3 = serializers.CharField()
    flow_descriptor4 = serializers.CharField()
    flow_descriptor5 = serializers.CharField()
    first_product_match = serializers.CharField()
    fraud_schema_id = serializers.IntegerField()
    forecasting_schema_id = serializers.IntegerField()


class getFlowStepResponseSerializer(serializers.Serializer):
    offer_flow_step_id = serializers.IntegerField()
    flow_step_number = serializers.IntegerField()
    step_mapped_cycles = serializers.IntegerField()
    continuity_enabled = serializers.IntegerField()
    initial_delay_hours = serializers.IntegerField()
    rebill_delay_hours = serializers.IntegerField()
    cont_cycle_1_delay_hours = serializers.IntegerField()
    cont_cycle_frequency = serializers.IntegerField()
    initial_price = serializers.IntegerField()
    rebill_price = serializers.IntegerField()
    cont_cycle_1_price = serializers.IntegerField()
    cont_cycle_2_price = serializers.IntegerField()
    cont_cycle_3_price = serializers.IntegerField()
    cont_cycle_4_price = serializers.IntegerField()
    cont_cycle_5_price = serializers.IntegerField()
    cont_cycle_6plus_price = serializers.IntegerField()
    initial_product_cost = serializers.IntegerField()
    continuity_product_cost = serializers.IntegerField()
    prior_update = serializers.IntegerField()


class getListClientResponseSerializer(serializers.Serializer):
    class ClientsInformationSerializers(serializers.Serializer):
        clientID = serializers.CharField()
        name = serializers.CharField()
        status = serializers.CharField()
        created_at = serializers.CharField()
        updated_at = serializers.CharField()

    message = serializers.CharField(default="-----------")
    data = ClientsInformationSerializers(many=False)
