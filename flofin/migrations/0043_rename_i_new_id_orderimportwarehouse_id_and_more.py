# Generated by Django 5.0.8 on 2024-09-13 09:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0042_orderimportwarehouse"),
    ]

    operations = [
        migrations.RenameField(
            model_name="orderimportwarehouse",
            old_name="i_new_id",
            new_name="id",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="campaign",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="card_infor",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="card_type_flag1",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="cc_bin",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="cc_is_prepaid",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="cc_last_four",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="client_customerid",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="client_login",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="continuity_product_cost",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="cpa_amount_script",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="cpa_match_field",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="currency",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="cycle_num_lookup",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="fcrm_is_test",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="delay_hours",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="g_i_ancestor_id",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="g_i_ancestor_t_date",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="g_i_direct_parent_id",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="g_i_step_parent_id",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="g_i_step_parent_t_date",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="g_is_unique_sale_attempt",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="g_projected_rebill_date",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="trans_att_num",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="import_product_identifier_1",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="import_product_identifier_2",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="import_product_identifier_3",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="initial_product_cost",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="is_3ds_verified",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="is_scrubbed",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="is_unique_sale_flag",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="merchantId",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="offer_product",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="order_id",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="order_status",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="payment_network",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="pmt_processing_field2",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="pmt_processing_field3",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="pr_global_grouping_field1",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="pre_tax_order_total",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="price_point",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="product_match",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="status_response",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="sub_order_id",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="transaction_date",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="transaction_flag1",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="vendorid1",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="vendorid2",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="vendorid3",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="vendorid4",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="vendorid5",
        ),
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="order_import",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="flofin.orderimport",
            ),
        ),
    ]
