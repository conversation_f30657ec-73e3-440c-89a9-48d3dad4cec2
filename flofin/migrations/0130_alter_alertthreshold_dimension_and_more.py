# Generated by Django 5.0.10 on 2024-12-20 08:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0129_alertthreshold_condition"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="alertthreshold",
            name="dimension",
            field=models.CharField(
                blank=True,
                choices=[
                    ("client", "Client"),
                    ("network", "Network"),
                    ("affiliate", "Affiliate"),
                    ("flow_brand_name", "Flow Title"),
                    ("card_brand", "Card Brand"),
                    ("card_type", "Card Type"),
                    ("merchantId", "MID"),
                    ("All", "All"),
                ],
                max_length=255,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="alertthreshold",
            name="metric",
            field=models.CharField(
                blank=True,
                choices=[
                    ("chargeback_rate", "Chargeback Rate"),
                    ("refund_rate", "Refund Rate"),
                    ("void_rate", "Void Rate"),
                    ("foreign_card_rate", "Foreign Transaction Rate"),
                    ("repeat_transaction_rate", "Repeat Transaction Rate"),
                    ("initial_approval_rate", "Initial Approval Rate"),
                ],
                max_length=255,
                null=True,
            ),
        ),
    ]
