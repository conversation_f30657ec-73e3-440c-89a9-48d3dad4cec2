# Generated by Django 4.2.14 on 2024-11-25 08:27

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0058_offerflow_status_offerflowsteps_status_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="orderimport",
            name="billing_address",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="billing_country",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="billing_state",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="billing_zip_code",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="counter_field",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="end_col",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="ip_address",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="shipping_address",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="shipping_country",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="shipping_state",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="shipping_zip_code",
        ),
    ]
