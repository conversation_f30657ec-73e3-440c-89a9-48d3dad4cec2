# Generated by Django 5.0.8 on 2024-12-05 08:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0075_orderimportwarehouse_offer_product"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="orderupdate",
            name="c_parent_orderid",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="c_parent_suborderid",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="customer_id",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="g_updated",
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="import_product_identifier_1",
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
    ]
