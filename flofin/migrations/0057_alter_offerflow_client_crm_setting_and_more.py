# Generated by Django 4.2.14 on 2024-11-25 03:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0056_homereports_card_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="offerflow",
            name="client_crm_setting",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_brand_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_descriptor1",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_descriptor2",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_descriptor3",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_descriptor4",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_descriptor5",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="offerproductdetail",
            name="cycle_descriptor",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
