# Generated by Django 5.0.8 on 2024-08-21 04:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0007_cardbinlookup_created_at_cardbinlookup_updated_at_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="orderupdate",
            name="g_i_flow_brand_name",
        ),
        migrations.RemoveField(
            model_name="orderupdate",
            name="g_i_new_id",
        ),
        migrations.RemoveField(
            model_name="orderupdate",
            name="g_payment_network",
        ),
        migrations.RemoveField(
            model_name="orderupdate",
            name="i_new_id",
        ),
        migrations.AddField(
            model_name="orderupdate",
            name="order_import",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="flofin.orderimport",
            ),
        ),
    ]
