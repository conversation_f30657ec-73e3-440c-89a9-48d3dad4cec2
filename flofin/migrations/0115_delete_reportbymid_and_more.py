# Generated by Django 5.0.10 on 2024-12-18 04:34

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0114_remove_homeclvreport_prepaid_card"),
    ]

    operations = [
        migrations.DeleteModel(
            name="reportByMid",
        ),
        migrations.RenameField(
            model_name="homeclvreport",
            old_name="mid_number",
            new_name="merchantId",
        ),
        migrations.RemoveField(
            model_name="homeclvreport",
            name="alert_cost",
        ),
        migrations.RemoveField(
            model_name="homeclvreport",
            name="continuity_product_cost",
        ),
        migrations.RemoveField(
            model_name="homeclvreport",
            name="cpa_amount_script",
        ),
        migrations.RemoveField(
            model_name="homeclvreport",
            name="initial_product_cost",
        ),
        migrations.RemoveField(
            model_name="homeclvreport",
            name="order_update_type",
        ),
        migrations.RemoveField(
            model_name="homeclvreport",
            name="revenue_update",
        ),
        migrations.RemoveField(
            model_name="homeclvreport",
            name="total_3ds_verified",
        ),
        migrations.RemoveField(
            model_name="homeclvreport",
            name="update_status",
        ),
    ]
