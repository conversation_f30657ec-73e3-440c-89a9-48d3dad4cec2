# Generated by Django 4.2.14 on 2024-11-19 06:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data", "0001_initial"),
        ("flofin", "0054_remove_offerflowsteps_prior_update_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="offerflow",
            name="client",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="data.clientsinformation",
            ),
        ),
        migrations.AddField(
            model_name="offerflow",
            name="client_login",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="data.clientlogininformation",
            ),
        ),
    ]
