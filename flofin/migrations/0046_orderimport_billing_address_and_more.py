# Generated by Django 5.0.8 on 2024-09-25 07:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0045_orderupdate_c_parent_orderid_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderimport",
            name="billing_address",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="billing_country",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="billing_state",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="billing_zip_code",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="counter_field",
            field=models.Integer<PERSON>ield(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="end_col",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="historical_match_assist",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="ip_address",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="shipping_address",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="shipping_country",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="shipping_state",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderimport",
            name="shipping_zip_code",
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
