# Generated by Django 5.0.8 on 2024-12-05 09:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0076_alter_orderupdate_c_parent_orderid_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="cardbinlookup",
            name="card_brand",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="cardbinlookup",
            name="card_subtype",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="cardbinlookup",
            name="card_type",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="cardbinlookup",
            name="issuer_country",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="homeclvreport",
            name="card_type",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="flow_brand_name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="input_type",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="network",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="prepaid_card",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="status",
            field=models.CharField(blank=True, default="Active", max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerflowsteps",
            name="status",
            field=models.CharField(blank=True, default="Active", max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerproductdetail",
            name="date_created",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerproductdetail",
            name="status",
            field=models.CharField(blank=True, default="Active", max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerproductdetail",
            name="step_price",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="campaign",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="card_type_flag1",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="customer_id",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="import_product_identifier_1",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="import_product_identifier_2",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="import_product_identifier_3",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="merchantId",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="merchant_descriptor",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="mid",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="order_id",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="payment_network",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="pr_global_grouping_field1",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="status_response",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="transaction_type",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="c_parent_orderid",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="c_parent_suborderid",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="customer_id",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="g_updated",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="import_product_identifier_1",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="order_id",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
