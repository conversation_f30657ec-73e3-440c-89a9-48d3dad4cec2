# Generated by Django 5.0.10 on 2024-12-18 03:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0112_rename_dimension_name_alertthreshold_dimension_value"),
    ]

    operations = [
        migrations.AddField(
            model_name="homeclvreport",
            name="card_issuer",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="chargebacks",
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="chargebacks_amount",
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="price_point",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="refunds",
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="refunds_amount",
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="total_3ds",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="transactions",
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="transactions_amount",
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="voids",
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="voids_amount",
            field=models.FloatField(null=True),
        ),
    ]
