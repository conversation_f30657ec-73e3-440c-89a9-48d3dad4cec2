# Generated by Django 4.2.14 on 2024-11-27 04:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0067_alter_offerflowsteps_continuity_enabled"),
    ]

    operations = [
        migrations.AddField(
            model_name="offerproductdetail",
            name="product_cost",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name="offerproductdetail",
            name="shipping_cost",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
    ]
