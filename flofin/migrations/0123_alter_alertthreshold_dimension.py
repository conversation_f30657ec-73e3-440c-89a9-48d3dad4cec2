# Generated by Django 5.0.10 on 2024-12-19 04:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0122_rename_curent_value_alertthreshold_current_value"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="alertthreshold",
            name="dimension",
            field=models.CharField(
                blank=True,
                choices=[
                    ("client", "Client"),
                    ("network", "Network"),
                    ("affiliate", "Affiliate"),
                    ("flow_brand_name", "Flow Title"),
                    ("card_brand", "Card Brand"),
                    ("card_type", "Card Type"),
                    ("merchantId", "MID"),
                ],
                max_length=255,
                null=True,
            ),
        ),
    ]
