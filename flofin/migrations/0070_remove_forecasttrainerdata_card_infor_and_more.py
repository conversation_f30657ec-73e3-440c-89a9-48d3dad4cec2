# Generated by Django 5.0.8 on 2024-12-02 04:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0069_remove_offerflowsteps_initial_price"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="forecasttrainerdata",
            name="card_infor",
        ),
        migrations.RemoveField(
            model_name="forecasttrainerdata",
            name="client_login",
        ),
        migrations.RemoveField(
            model_name="forecasttrainerdata",
            name="offer_product",
        ),
        migrations.RenameField(
            model_name="orderimport",
            old_name="i_new_id",
            new_name="id",
        ),
        migrations.RenameField(
            model_name="orderimportwarehouse",
            old_name="rebill_i_new_id",
            new_name="blacklisted",
        ),
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="rebill_id",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="refunded",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.DeleteModel(
            name="CostPerAcqFull",
        ),
        migrations.DeleteModel(
            name="ForecastTrainerData",
        ),
    ]
