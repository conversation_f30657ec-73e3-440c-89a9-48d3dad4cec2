# Generated by Django 5.0.8 on 2024-12-12 04:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0083_orderimport_blacklisted_orderimport_refunded_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="homeclvreport",
            name="card_brand",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="cycle_num",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="flow_step_number",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="mid_number",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
