# Generated by Django 5.0.8 on 2024-08-29 06:21

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0025_alter_reportbymid_table"),
    ]

    operations = [
        migrations.CreateModel(
            name="HomeCLVReport",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("clientID", models.IntegerField(blank=True, null=True)),
                ("input_type", models.CharField(blank=True, max_length=45, null=True)),
                ("update_date", models.DateTimeField(blank=True, null=True)),
                ("transaction_date", models.DateTimeField(blank=True, null=True)),
                ("network", models.CharField(blank=True, max_length=45, null=True)),
                ("pub", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                (
                    "fcrm_is_test",
                    models.Char<PERSON>ield(blank=True, max_length=230, null=True),
                ),
                (
                    "post_tax_order_total",
                    models.Decimal<PERSON>ield(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("order_status", models.IntegerField(blank=True, null=True)),
                ("g_i_ancestor_t_date", models.DateTimeField(blank=True, null=True)),
                (
                    "flow_brand_name",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "cpa_amount_script",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "initial_product_cost",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "continuity_product_cost",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("is_unique_sale_flag", models.IntegerField(blank=True, null=True)),
                ("is_3ds_verified", models.IntegerField(blank=True, null=True)),
                (
                    "counter_field",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("order_update_type", models.IntegerField(blank=True, null=True)),
                ("order_update_status", models.IntegerField(blank=True, null=True)),
                (
                    "revenue_update",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "alert_cost",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("g_card_type", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "g_prepaid_card",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                "db_table": "FloFin_Home_CLV",
            },
        ),
        migrations.CreateModel(
            name="HomeQuickReports",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("clientID", models.CharField(blank=True, max_length=255, null=True)),
                ("transaction_date", models.DateField(blank=True, null=True)),
                ("card_brand", models.CharField(blank=True, max_length=255, null=True)),
                ("mid_number", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "flow_brand_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("flow_step_number", models.IntegerField(blank=True, null=True)),
                ("cycle_num", models.IntegerField(blank=True, null=True)),
                (
                    "price_point",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "approved_volume",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("is_3ds_verified", models.IntegerField(default=0)),
                ("pub", models.CharField(blank=True, max_length=255, null=True)),
                ("order_status", models.IntegerField(default=0)),
                ("counter_field", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                "db_table": "FloFin_Report_Home_QuickReports",
            },
        ),
        migrations.RenameModel(
            old_name="CbByMidView",
            new_name="HomeCbByMid",
        ),
        migrations.RemoveField(
            model_name="oiousummary",
            name="client_login",
        ),
        migrations.DeleteModel(
            name="QuickReports",
        ),
        migrations.AlterModelTable(
            name="homecbbymid",
            table="FloFin_Report_Home_CbReportByMid",
        ),
        migrations.DeleteModel(
            name="OiOuSummary",
        ),
    ]
