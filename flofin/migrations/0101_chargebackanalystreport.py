# Generated by Django 5.0.10 on 2024-12-16 04:22

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('flofin', '0100_alter_orderimport_retention_alter_orderimport_score'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChargebackAnalystReport',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('clientID', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('client', models.CharField(blank=True, max_length=255, null=True)),
                ('input_type', models.CharField(blank=True, max_length=255, null=True)),
                ('report_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('flow_brand_name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('flow_step_number', models.IntegerField(blank=True, null=True)),
                ('card_brand', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('card_type', models.CharField(blank=True, max_length=255, null=True)),
                ('card_issuer', models.CharField(blank=True, max_length=255, null=True)),
                ('price_point', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('cycle_num', models.IntegerField(blank=True, null=True)),
                ('card_issuer_3ds', models.IntegerField(blank=True, null=True)),
                ('mid_number_3ds', models.CharField(blank=True, max_length=255, null=True)),
                ('transactions', models.IntegerField(null=True)),
                ('transactions_amount', models.FloatField(null=True)),
                ('refunds', models.IntegerField(null=True)),
                ('refunds_amount', models.FloatField(null=True)),
                ('chargebacks', models.IntegerField(null=True)),
                ('chargebacks_amount', models.FloatField(null=True)),
                ('voids', models.IntegerField(null=True)),
                ('voids_amount', models.FloatField(null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'FloFin_Report_Chargeback_Analyst',
            },
        ),
    ]
