# Generated by Django 5.0.8 on 2024-12-06 01:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0077_alter_cardbinlookup_card_brand_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="cardbinlookup",
            name="card_issuer_name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="pub",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="client_crm_setting",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_brand_name",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="offerflow",
            name="flow_descriptor1",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_descriptor2",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_descriptor3",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_descriptor4",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerflow",
            name="flow_descriptor5",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="offerproductdetail",
            name="cycle_descriptor",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="ancestor_vendorid1",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="ancestor_vendorid2",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="vendorid1",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="vendorid2",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="vendorid3",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="vendorid4",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderimport",
            name="vendorid5",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderupdate",
            name="status_response",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
