# Generated by Django 5.0.10 on 2024-12-17 08:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0109_remove_alertthreshold_alert_type"),
        ("user", "0002_statusaccountlog_users_company_users_first_name_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="alertthreshold",
            name="lst_email",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="alertthreshold",
            name="user",
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to="user.users"),
        ),
    ]
