# Generated by Django 5.0.8 on 2024-12-02 10:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0070_remove_forecasttrainerdata_card_infor_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="rebill_id",
        ),
        migrations.RemoveField(
            model_name="orderimportwarehouse",
            name="rebill_status",
        ),
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="card_infor",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="flofin.cardbinlookup",
            ),
        ),
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="offer_product",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="flofin.offerproductdetail",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="orderimportwarehouse",
            name="blacklisted",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="orderimportwarehouse",
            name="refunded",
            field=models.IntegerField(default=0),
        ),
    ]
