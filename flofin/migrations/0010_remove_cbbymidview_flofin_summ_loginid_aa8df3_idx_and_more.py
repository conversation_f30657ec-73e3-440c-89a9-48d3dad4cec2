# Generated by Django 5.0.8 on 2024-08-22 07:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "flofin",
            "0009_rename_g_cycle_num_lookup_forecasttrainerdata_cycle_num_lookup_and_more",
        ),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="cbbymidview",
            name="FloFin_summ_loginID_aa8df3_idx",
        ),
        migrations.RenameField(
            model_name="cbbymidview",
            old_name="mid_identifier",
            new_name="merchantId",
        ),
        migrations.RenameField(
            model_name="forecasttrainerdata",
            old_name="mid_identifier",
            new_name="merchantId",
        ),
        migrations.RenameField(
            model_name="oiousummary",
            old_name="mid_identifier",
            new_name="merchantId",
        ),
        migrations.RemoveField(
            model_name="orderupdate",
            name="client_customerid",
        ),
        migrations.RemoveField(
            model_name="orderupdate",
            name="import_product_identifier_1",
        ),
        migrations.RemoveField(
            model_name="orderupdate",
            name="import_product_identifier_2",
        ),
        migrations.RemoveField(
            model_name="orderupdate",
            name="import_product_identifier_3",
        ),
        migrations.RemoveField(
            model_name="orderupdate",
            name="is_3ds_verified",
        ),
        migrations.RemoveField(
            model_name="orderupdate",
            name="mid_identifier",
        ),
        migrations.AddIndex(
            model_name="cbbymidview",
            index=models.Index(
                fields=["loginID", "merchantId", "update_date"],
                name="FloFin_summ_loginID_c0b1cb_idx",
            ),
        ),
    ]
