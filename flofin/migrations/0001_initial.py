# Generated by Django 5.0.8 on 2024-08-19 04:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("data", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CardBinLookup",
            fields=[
                ("card_bin", models.IntegerField(primary_key=True, serialize=False)),
                (
                    "card_issuer_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("prepaid_card", models.IntegerField(blank=True, null=True)),
                (
                    "issuer_country",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("card_brand", models.CharField(blank=True, max_length=45, null=True)),
                ("card_type", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "card_subtype",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("end_col", models.IntegerField(blank=True, default=0, null=True)),
            ],
            options={
                "db_table": "FloFin_card_bin_lookup",
            },
        ),
        migrations.CreateModel(
            name="ForecastTrainerData",
            fields=[
                ("referral_id", models.AutoField(primary_key=True, serialize=False)),
                ("i_new_id", models.IntegerField()),
                ("id", models.IntegerField(blank=True, null=True)),
                ("client_id", models.IntegerField(blank=True, null=True)),
                ("client_crm", models.CharField(blank=True, max_length=45, null=True)),
                ("transaction_category", models.IntegerField(blank=True, null=True)),
                (
                    "transaction_category_subtype",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "flow_category",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "traffic_source_name",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "source_category",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("transaction_date", models.DateTimeField(blank=True, null=True)),
                (
                    "amount",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("status_code", models.IntegerField()),
                (
                    "status_reason_code",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "mid_identifier",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("mid_name", models.CharField(blank=True, max_length=100, null=True)),
                ("processor", models.CharField(blank=True, max_length=45, null=True)),
                ("mid_start_date", models.DateTimeField(blank=True, null=True)),
                (
                    "billing_state",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "shipping_state",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("flow_id", models.IntegerField(blank=True, null=True)),
                (
                    "flow_brand_name",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("flow_step_number", models.IntegerField(blank=True, null=True)),
                ("cc_bin", models.IntegerField(blank=True, null=True)),
                (
                    "cc_issuing_bank",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("cc_is_foreign_card", models.IntegerField(blank=True, null=True)),
                ("cc_is_repeat_card", models.IntegerField(blank=True, null=True)),
                ("cc_is_prepaid", models.IntegerField(blank=True, null=True)),
                ("cc_brand", models.CharField(blank=True, max_length=45, null=True)),
                ("cc_type", models.CharField(blank=True, max_length=45, null=True)),
                ("is_3ds_verified", models.IntegerField(blank=True, null=True)),
                ("rebill_status", models.IntegerField(blank=True, null=True)),
                ("rebill_i_new_id", models.IntegerField(blank=True, null=True)),
                ("i_ancestor_i_new_id", models.IntegerField(blank=True, null=True)),
                ("i_ancestor_t_date", models.DateTimeField(blank=True, null=True)),
                ("i_step_parent_i_new_id", models.IntegerField(blank=True, null=True)),
                ("i_step_parent_t_date", models.DateTimeField(blank=True, null=True)),
                ("trans_att_num", models.IntegerField(blank=True, null=True)),
                ("g_cycle_num_lookup", models.IntegerField(blank=True, null=True)),
                ("has_declined_upsell", models.IntegerField(blank=True, null=True)),
                ("has_refund", models.IntegerField(blank=True, null=True)),
                ("has_chargeback", models.IntegerField(blank=True, null=True)),
                ("approved_upsell_dollars", models.FloatField(blank=True, null=True)),
                ("rebill_amount", models.FloatField(blank=True, null=True)),
                ("has_attempted_rebill", models.IntegerField(blank=True, null=True)),
                ("pub_recent_repeat_card", models.FloatField(blank=True, null=True)),
                ("pub_recent_foreign_card", models.FloatField(blank=True, null=True)),
                (
                    "transaction_stored_probability",
                    models.FloatField(blank=True, null=True),
                ),
                (
                    "transaction_stored_result",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "transaction_current_probability",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_1", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_1_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_2", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_2_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_3", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_3_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_4", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_4_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_5", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_5_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_6", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_6_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_7", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_7_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_8", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_8_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_9", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_9_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("approv_metric_10", models.FloatField(blank=True, null=True)),
                (
                    "approv_metric_10_denominator",
                    models.FloatField(blank=True, null=True),
                ),
                ("forecasted", models.IntegerField(blank=True, null=True)),
            ],
            options={
                "db_table": "FloFin_forecast_trainer_data",
            },
        ),
        migrations.CreateModel(
            name="OfferFlow",
            fields=[
                ("flow_id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "flow_brand_name",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("flow_number_of_steps", models.IntegerField()),
                (
                    "client_crm_setting",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "flow_descriptor1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "flow_descriptor2",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "flow_descriptor3",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "flow_descriptor4",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "flow_descriptor5",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "first_product_match",
                    models.CharField(blank=True, max_length=230, null=True),
                ),
                ("fraud_schema_id", models.IntegerField(blank=True, null=True)),
                ("forecasting_schema_id", models.IntegerField(blank=True, null=True)),
            ],
            options={
                "db_table": "FloFin_offer_flow",
            },
        ),
        migrations.CreateModel(
            name="QuickReports",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("loginID", models.CharField(blank=True, max_length=255, null=True)),
                ("transaction_date", models.DateField(blank=True, null=True)),
                ("card_brand", models.CharField(blank=True, max_length=255, null=True)),
                ("mid_number", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "flow_brand_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("flow_step_number", models.IntegerField(blank=True, null=True)),
                ("cycle_num", models.IntegerField(blank=True, null=True)),
                (
                    "post_tax_order_total",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "approved_volume",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("is_3ds_verified", models.IntegerField(default=0)),
                ("pub", models.CharField(blank=True, max_length=255, null=True)),
                ("order_status", models.IntegerField(default=0)),
                ("counter_field", models.IntegerField(default=0)),
            ],
            options={
                "db_table": "FloFin_summary_quick_reports",
            },
        ),
        migrations.CreateModel(
            name="CbByMidView",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("loginID", models.CharField(blank=True, max_length=255, null=True)),
                ("update_date", models.DateField()),
                ("refund_count", models.IntegerField(default=0)),
                ("chargeback_count", models.IntegerField(default=0)),
                ("visa_chargeback_count", models.IntegerField(default=0)),
                ("mastercard_chargeback_count", models.IntegerField(default=0)),
                (
                    "refunded_revenue",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "chargedback_revenue",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "mid_identifier",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("mid_name", models.CharField(blank=True, max_length=255, null=True)),
                ("transaction_count", models.IntegerField(default=0)),
                (
                    "revenue",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
            ],
            options={
                "db_table": "FloFin_summary_cb_by_mid",
                "indexes": [
                    models.Index(
                        fields=["loginID", "mid_identifier", "update_date"],
                        name="FloFin_summ_loginID_aa8df3_idx",
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="OfferFlowSteps",
            fields=[
                (
                    "offer_flow_step_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("flow_step_number", models.IntegerField(blank=True, null=True)),
                ("step_mapped_cycles", models.IntegerField(blank=True, null=True)),
                ("continuity_enabled", models.IntegerField()),
                ("initial_delay_hours", models.IntegerField(blank=True, null=True)),
                ("rebill_delay_hours", models.IntegerField(blank=True, null=True)),
                (
                    "cont_cycle_1_delay_hours",
                    models.IntegerField(blank=True, null=True),
                ),
                ("cont_cycle_frequency", models.IntegerField(blank=True, null=True)),
                (
                    "initial_price",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "rebill_price",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "cont_cycle_1_price",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "cont_cycle_2_price",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "cont_cycle_3_price",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "cont_cycle_4_price",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "cont_cycle_5_price",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "cont_cycle_6plus_price",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "initial_product_cost",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "continuity_product_cost",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("prior_update", models.IntegerField(blank=True, null=True)),
                (
                    "flow",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="flofin.offerflow",
                    ),
                ),
            ],
            options={
                "db_table": "FloFin_offer_flow_steps",
            },
        ),
        migrations.CreateModel(
            name="CostPerAcqFull",
            fields=[
                (
                    "cpa_full_model_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "cpa_amount",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "cpa_match_field",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("client_id", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "client_crm_instance",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("description", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "offer_flow_step",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="flofin.offerflowsteps",
                    ),
                ),
            ],
            options={
                "db_table": "FloFin_cost_per_acq_full",
            },
        ),
        migrations.CreateModel(
            name="OfferProductDetail",
            fields=[
                ("offer_product", models.AutoField(primary_key=True, serialize=False)),
                ("cycle_number", models.IntegerField(blank=True, null=True)),
                (
                    "cycle_descriptor",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("step_price", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "date_created",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("price_valid_start", models.DateTimeField(blank=True, null=True)),
                ("status", models.IntegerField()),
                (
                    "product_match",
                    models.CharField(blank=True, max_length=230, null=True),
                ),
                ("backlogged_cycle_flag", models.IntegerField(blank=True, null=True)),
                (
                    "unique_product_match",
                    models.CharField(blank=True, max_length=230, null=True, unique=True),
                ),
                ("prior_update_count", models.IntegerField(default=-1)),
                (
                    "historical_unique",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "offer_flow_step",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="flofin.offerflowsteps",
                    ),
                ),
            ],
            options={
                "db_table": "FloFin_offer_product_detail",
            },
        ),
        migrations.CreateModel(
            name="OiOuSummaryBackup",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("order_id", models.CharField(blank=True, max_length=255, null=True)),
                ("transaction_date", models.DateField(blank=True, null=True)),
                ("original_sale_date", models.DateField(blank=True, null=True)),
                (
                    "flow_brand_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("flow_step_number", models.IntegerField(blank=True, null=True)),
                ("cc_BIN", models.CharField(blank=True, max_length=10, null=True)),
                ("cycle_number", models.IntegerField(blank=True, null=True)),
                ("card_brand", models.CharField(blank=True, max_length=50, null=True)),
                ("card_type", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "card_issuer",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("g_prepaid_card", models.BooleanField(blank=True, null=True)),
                (
                    "post_tax_order_total",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("order_count", models.IntegerField(blank=True, null=True)),
                ("price_point", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "mid_identifier",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("is_3DS_verified", models.BooleanField(blank=True, null=True)),
                ("counter_field", models.IntegerField(blank=True, null=True)),
                ("update_date", models.DateField(blank=True, null=True)),
                ("order_update_type", models.CharField(max_length=255, null=True)),
                ("update_count", models.IntegerField(null=True)),
                ("order_update_status", models.IntegerField(null=True)),
                ("update_reason", models.CharField(max_length=255, null=True)),
                (
                    "revenue_update",
                    models.DecimalField(decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "cost_update",
                    models.DecimalField(decimal_places=2, max_digits=10, null=True),
                ),
                ("status_response", models.CharField(max_length=255, null=True)),
                (
                    "client_login",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="data.clientlogininformation",
                    ),
                ),
            ],
            options={
                "db_table": "FloFin_summary_oiou",
            },
        ),
        migrations.CreateModel(
            name="OrderImport",
            fields=[
                ("i_new_id", models.AutoField(primary_key=True, serialize=False)),
                ("order_id", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "sub_order_id",
                    models.CharField(blank=True, max_length=45, null=True, unique=True),
                ),
                ("transaction_date", models.DateTimeField(blank=True, null=True)),
                (
                    "import_product_identifier_1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "import_product_identifier_2",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "import_product_identifier_3",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "product_match",
                    models.CharField(blank=True, max_length=230, null=True),
                ),
                ("fcrm_is_test", models.IntegerField(blank=True, null=True)),
                (
                    "pre_tax_order_total",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "post_tax_order_total",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("order_status", models.IntegerField(blank=True, null=True)),
                (
                    "status_response",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("vendorid1", models.CharField(blank=True, max_length=45, null=True)),
                ("vendorid2", models.CharField(blank=True, max_length=45, null=True)),
                ("vendorid3", models.CharField(blank=True, max_length=45, null=True)),
                ("vendorid4", models.CharField(blank=True, max_length=45, null=True)),
                ("vendorid5", models.CharField(blank=True, max_length=45, null=True)),
                ("campaign", models.CharField(blank=True, max_length=45, null=True)),
                ("is_scrubbed", models.IntegerField(blank=True, null=True)),
                (
                    "payment_network",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "client_customerid",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("cc_bin", models.IntegerField(blank=True, null=True)),
                ("cc_last_four", models.IntegerField(blank=True, null=True)),
                (
                    "pmt_processing_field1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "pmt_processing_field2",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "pmt_processing_field3",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "pr_global_grouping_field1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "card_type_flag1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "cc_is_prepaid",
                    models.IntegerField(blank=True, default=0, null=True),
                ),
                ("currency", models.CharField(blank=True, max_length=3, null=True)),
                ("is_3ds_verified", models.IntegerField(blank=True, null=True)),
                (
                    "transaction_flag1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "cpa_match_field",
                    models.CharField(blank=True, max_length=230, null=True),
                ),
                (
                    "cpa_amount_script",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("cpa_updated_script", models.IntegerField(blank=True, null=True)),
                ("delay_hours", models.IntegerField(blank=True, null=True)),
                (
                    "initial_product_cost",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "continuity_product_cost",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("g_cycle_num_lookup", models.IntegerField(blank=True, null=True)),
                ("g_i_ancestor_id", models.IntegerField(blank=True, null=True)),
                ("g_i_ancestor_t_date", models.DateTimeField(blank=True, null=True)),
                ("g_i_step_parent_id", models.IntegerField(blank=True, null=True)),
                ("g_i_step_parent_t_date", models.DateTimeField(blank=True, null=True)),
                ("trans_att_num", models.IntegerField(blank=True, null=True)),
                ("g_i_direct_parent_id", models.IntegerField(blank=True, null=True)),
                (
                    "g_projected_rebill_date",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("is_unique_sale_flag", models.IntegerField(blank=True, null=True)),
                (
                    "g_is_unique_sale_attempt",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "delay_hours_updated_flag",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "projected_rebill_updated_flag",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "trans_attempt_updated_flag",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "card_infor",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="flofin.cardbinlookup",
                    ),
                ),
                (
                    "client_login",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="data.clientlogininformation",
                    ),
                ),
                (
                    "offer_product",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="flofin.offerproductdetail",
                    ),
                ),
            ],
            options={
                "db_table": "FloFin_order_import",
            },
        ),
        migrations.CreateModel(
            name="OrderUpdate",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("i_new_id", models.IntegerField(blank=True, null=True)),
                ("order_id", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "sub_order_id",
                    models.CharField(blank=True, max_length=45, null=True, unique=True),
                ),
                ("update_date", models.DateTimeField(blank=True, null=True)),
                ("order_update_type", models.IntegerField(blank=True, null=True)),
                (
                    "update_reason",
                    models.CharField(blank=True, max_length=250, null=True),
                ),
                (
                    "revenue_update",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "import_product_identifier_1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "import_product_identifier_2",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "import_product_identifier_3",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "product_match",
                    models.CharField(blank=True, max_length=230, null=True),
                ),
                (
                    "client_customerid",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("order_update_status", models.IntegerField(blank=True, null=True)),
                (
                    "status_response",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("is_3ds_verified", models.IntegerField(blank=True, null=True)),
                (
                    "mid_identifier",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("g_i_new_id", models.IntegerField(blank=True, null=True)),
                (
                    "g_i_flow_brand_name",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "g_payment_network",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("g_updated", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "client_login",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="data.clientlogininformation",
                    ),
                ),
            ],
            options={
                "db_table": "FloFin_order_update",
            },
        ),
    ]
