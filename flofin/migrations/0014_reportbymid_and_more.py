# Generated by Django 5.0.8 on 2024-08-23 06:42

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0013_rename_i_update_id_oiousummary_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="reportByMid",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("loginID", models.CharField(blank=True, max_length=255, null=True)),
                ("clientID", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "report_date",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                (
                    "card_issuer",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "flow_brand_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("flow_step_number", models.IntegerField(null=True)),
                ("card_brand", models.CharField(blank=True, max_length=255, null=True)),
                ("card_type", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "price_point",
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                ("cycle", models.IntegerField(null=True)),
                ("type_report", models.IntegerField(default=0)),
                ("is_3ds", models.IntegerField(default=0)),
                ("transactions", models.IntegerField(null=True)),
                ("transactions_amount", models.FloatField(null=True)),
                ("refunds", models.IntegerField(null=True)),
                ("refunds_amount", models.FloatField(null=True)),
                ("voids", models.IntegerField(null=True)),
                ("voids_amount", models.FloatField(null=True)),
                ("chargebacks", models.IntegerField(null=True)),
                ("chargebacks_amount", models.FloatField(null=True)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                "db_table": "FloFin_report_by_mid",
            },
        ),
        migrations.RenameIndex(
            model_name="cbbymidview",
            new_name="Draft_FloFi_loginID_07a580_idx",
            old_name="FloFin_summ_loginID_c0b1cb_idx",
        ),
        migrations.AlterModelTable(
            name="cbbymidview",
            table="Draft_FloFin_summary_cb_by_mid",
        ),
        migrations.AlterModelTable(
            name="oiousummary",
            table="Draft_FloFin_summary_oiou",
        ),
        migrations.AlterModelTable(
            name="quickreports",
            table="Draft_FloFin_summary_quick_reports",
        ),
    ]
