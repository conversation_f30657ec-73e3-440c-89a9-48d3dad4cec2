# Generated by Django 5.0.8 on 2025-02-10 16:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0130_alter_alertthreshold_dimension_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="homeclvreport",
            name="flow_brand",
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to="flofin.offerflow"),
        ),
        migrations.AddField(
            model_name="homeclvreport",
            name="flow_step",
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to="flofin.offerflowsteps"),
        ),
    ]
