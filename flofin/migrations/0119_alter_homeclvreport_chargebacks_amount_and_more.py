# Generated by Django 5.0.10 on 2024-12-18 06:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0118_alter_homeclvreport_chargebacks_amount_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="homeclvreport",
            name="chargebacks_amount",
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="post_tax_order_total",
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="price_point",
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="refunds_amount",
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="transactions_amount",
            field=models.FloatField(null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="homeclvreport",
            name="voids_amount",
            field=models.Float<PERSON>ield(null=True),
        ),
    ]
