# Generated by Django 5.0.8 on 2024-09-13 03:49

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data", "0001_initial"),
        ("flofin", "0041_remove_forecasttrainerdata_cc_is_foreign_card_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderImportWareHouse",
            fields=[
                ("i_new_id", models.AutoField(primary_key=True, serialize=False)),
                ("order_id", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "sub_order_id",
                    models.CharField(blank=True, max_length=45, null=True, unique=True),
                ),
                ("transaction_date", models.DateTimeField(blank=True, null=True)),
                (
                    "import_product_identifier_1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "import_product_identifier_2",
                    models.Char<PERSON>ield(blank=True, max_length=45, null=True),
                ),
                (
                    "import_product_identifier_3",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "product_match",
                    models.CharField(blank=True, max_length=230, null=True),
                ),
                ("fcrm_is_test", models.IntegerField(blank=True, null=True)),
                (
                    "pre_tax_order_total",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "price_point",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("order_status", models.IntegerField(blank=True, null=True)),
                (
                    "status_response",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("vendorid1", models.CharField(blank=True, max_length=100, null=True)),
                ("vendorid2", models.CharField(blank=True, max_length=100, null=True)),
                ("vendorid3", models.CharField(blank=True, max_length=100, null=True)),
                ("vendorid4", models.CharField(blank=True, max_length=100, null=True)),
                ("vendorid5", models.CharField(blank=True, max_length=100, null=True)),
                ("campaign", models.CharField(blank=True, max_length=45, null=True)),
                ("is_scrubbed", models.IntegerField(blank=True, null=True)),
                (
                    "payment_network",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "client_customerid",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("cc_bin", models.IntegerField(blank=True, null=True)),
                ("cc_last_four", models.IntegerField(blank=True, null=True)),
                ("merchantId", models.CharField(blank=True, max_length=45, null=True)),
                (
                    "pmt_processing_field2",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "pmt_processing_field3",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "pr_global_grouping_field1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "card_type_flag1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "cc_is_prepaid",
                    models.IntegerField(blank=True, default=0, null=True),
                ),
                ("currency", models.CharField(blank=True, max_length=3, null=True)),
                ("is_3ds_verified", models.IntegerField(blank=True, null=True)),
                (
                    "transaction_flag1",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "cpa_match_field",
                    models.CharField(blank=True, max_length=230, null=True),
                ),
                (
                    "cpa_amount_script",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("delay_hours", models.IntegerField(blank=True, null=True)),
                (
                    "initial_product_cost",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "continuity_product_cost",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("cycle_num_lookup", models.IntegerField(blank=True, null=True)),
                ("g_i_ancestor_id", models.IntegerField(blank=True, null=True)),
                ("g_i_ancestor_t_date", models.DateTimeField(blank=True, null=True)),
                ("g_i_step_parent_id", models.IntegerField(blank=True, null=True)),
                ("g_i_step_parent_t_date", models.DateTimeField(blank=True, null=True)),
                ("trans_att_num", models.IntegerField(blank=True, null=True)),
                ("g_i_direct_parent_id", models.IntegerField(blank=True, null=True)),
                (
                    "g_projected_rebill_date",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("is_unique_sale_flag", models.IntegerField(blank=True, null=True)),
                (
                    "g_is_unique_sale_attempt",
                    models.IntegerField(blank=True, null=True),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "card_infor",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="flofin.cardbinlookup",
                    ),
                ),
                (
                    "client_login",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="data.clientlogininformation",
                    ),
                ),
                (
                    "offer_product",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="flofin.offerproductdetail",
                    ),
                ),
            ],
            options={
                "db_table": "FloFin_order_import_warehouse",
            },
        ),
    ]
