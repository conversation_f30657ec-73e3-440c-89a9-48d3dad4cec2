# Generated by Django 5.0.8 on 2024-12-06 08:50

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data", "0002_clientlogininformation_active_and_more"),
        ("flofin", "0079_alter_orderimport_table"),
    ]

    operations = [
        migrations.AlterModelTable(
            name="orderimport",
            table="FloFin_order_import",
        ),
        migrations.CreateModel(
            name="OrderImportTemp",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("order_id", models.CharField(blank=True, max_length=255, null=True)),
                ("sub_order_id", models.CharField(blank=True, max_length=45, null=True, unique=True)),
                ("transaction_date", models.DateTimeField(blank=True, null=True)),
                ("import_product_identifier_1", models.CharField(blank=True, max_length=255, null=True)),
                ("import_product_identifier_2", models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ("import_product_identifier_3", models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ("product_match", models.CharField(blank=True, max_length=230, null=True)),
                ("fcrm_is_test", models.IntegerField(blank=True, null=True)),
                ("pre_tax_order_total", models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ("price_point", models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ("order_status", models.IntegerField(blank=True, null=True)),
                ("status_response", models.CharField(blank=True, max_length=255, null=True)),
                ("vendorid1", models.CharField(blank=True, max_length=255, null=True)),
                ("vendorid2", models.CharField(blank=True, max_length=255, null=True)),
                ("vendorid3", models.CharField(blank=True, max_length=255, null=True)),
                ("vendorid4", models.CharField(blank=True, max_length=255, null=True)),
                ("vendorid5", models.CharField(blank=True, max_length=255, null=True)),
                ("campaign", models.CharField(blank=True, max_length=255, null=True)),
                ("is_scrubbed", models.IntegerField(blank=True, null=True)),
                ("payment_network", models.CharField(blank=True, max_length=255, null=True)),
                ("customer_id", models.CharField(blank=True, max_length=255, null=True)),
                ("cc_bin", models.IntegerField(blank=True, null=True)),
                ("card_last4", models.IntegerField(blank=True, null=True)),
                ("merchantId", models.CharField(blank=True, max_length=255, null=True)),
                ("merchant_descriptor", models.CharField(blank=True, max_length=255, null=True)),
                ("mid", models.CharField(blank=True, max_length=255, null=True)),
                ("pr_global_grouping_field1", models.CharField(blank=True, max_length=255, null=True)),
                ("card_type_flag1", models.CharField(blank=True, max_length=255, null=True)),
                ("cc_is_prepaid", models.IntegerField(blank=True, default=0, null=True)),
                ("currency", models.CharField(blank=True, max_length=3, null=True)),
                ("is_3ds_verified", models.IntegerField(blank=True, null=True)),
                ("transaction_type", models.CharField(blank=True, max_length=255, null=True)),
                ("cpa_amount_script", models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ("g_delay_hours", models.IntegerField(blank=True, null=True)),
                ("initial_product_cost", models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ("continuity_product_cost", models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ("cycle_num_lookup", models.IntegerField(blank=True, null=True)),
                ("g_i_ancestor_id", models.IntegerField(blank=True, null=True)),
                ("g_i_ancestor_t_date", models.DateTimeField(blank=True, null=True)),
                ("g_i_step_parent_id", models.IntegerField(blank=True, null=True)),
                ("g_i_step_parent_t_date", models.DateTimeField(blank=True, null=True)),
                ("g_trans_att_num", models.IntegerField(blank=True, null=True)),
                ("g_i_direct_parent_id", models.IntegerField(blank=True, null=True)),
                ("g_projected_rebill_date", models.DateTimeField(blank=True, null=True)),
                ("is_unique_sale_flag", models.IntegerField(blank=True, null=True)),
                ("g_is_unique_sale_attempt", models.IntegerField(blank=True, null=True)),
                ("ancestor_vendorid1", models.CharField(blank=True, max_length=255, null=True)),
                ("ancestor_vendorid2", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("card_infor", models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to="flofin.cardbinlookup")),
                ("client_login", models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="data.clientlogininformation")),
                ("offer_product", models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to="flofin.offerproductdetail")),
            ],
            options={
                "db_table": "FloFin_order_import_temp",
            },
        ),
    ]
