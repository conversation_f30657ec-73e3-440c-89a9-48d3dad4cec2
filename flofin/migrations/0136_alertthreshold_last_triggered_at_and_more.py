# Generated by Django 5.0.14 on 2025-06-13 07:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('flofin', '0135_offerflowsteps_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='alertthreshold',
            name='last_triggered_at',
            field=models.DateTimeField(blank=True, default=None, null=True),
        ),
        migrations.AddField(
            model_name='alertthreshold',
            name='next_trigger_at',
            field=models.DateTimeField(blank=True, default=None, null=True),
        ),
    ]
