# Generated by Django 5.0.10 on 2024-12-15 14:53

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('flofin', '0093_rename_g_i_ancestor_t_date_homeclvreport_ancestor_date_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='homeclvreport',
            old_name='is_unique_sale_flag',
            new_name='is_unique_sale',
        ),
        migrations.RenameField(
            model_name='orderimport',
            old_name='g_i_ancestor_id',
            new_name='ancestor_id',
        ),
        migrations.RenameField(
            model_name='orderimport',
            old_name='g_i_direct_parent_id',
            new_name='direct_parent_id',
        ),
        migrations.RenameField(
            model_name='orderimport',
            old_name='g_i_step_parent_id',
            new_name='is_attempt_sale',
        ),
        migrations.RenameField(
            model_name='orderimport',
            old_name='g_is_unique_sale_attempt',
            new_name='is_unique_sale',
        ),
        migrations.RenameField(
            model_name='orderimport',
            old_name='g_i_step_parent_t_date',
            new_name='projected_rebill_date',
        ),
        migrations.RenameField(
            model_name='orderimport',
            old_name='g_projected_rebill_date',
            new_name='step_parent_date',
        ),
        migrations.RenameField(
            model_name='orderimport',
            old_name='is_unique_sale_flag',
            new_name='step_parent_id',
        ),
        migrations.RemoveField(
            model_name='offerproductdetail',
            name='date_created',
        ),
    ]
