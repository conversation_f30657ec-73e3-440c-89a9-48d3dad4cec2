# Generated by Django 5.0.8 on 2024-12-03 08:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0072_rename_order_import_orderimportwarehouse_oi_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="ancestor_oi",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="ancestor_oi_warehouse",
                to="flofin.orderimport",
            ),
        ),
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="cycle_num_lookup",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="direct_parent_oi",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="direct_parent_oi_warehouse",
                to="flofin.orderimport",
            ),
        ),
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="g_trans_att_num",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderimportwarehouse",
            name="step_parent_oi",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="step_parent_oi_warehouse",
                to="flofin.orderimport",
            ),
        ),
        migrations.AlterField(
            model_name="orderimportwarehouse",
            name="oi",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="oi_warehouse",
                to="flofin.orderimport",
            ),
        ),
    ]
