# Generated by Django 5.0.10 on 2024-12-19 04:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0124_alter_alertthreshold_trigger_frequency"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="alertthreshold",
            name="metric",
            field=models.CharField(
                blank=True,
                choices=[
                    ("chargeback_rate", "Chargeback Rate"),
                    ("refund_rate", "Refund Rate"),
                    ("void_rate", "Void Rate"),
                    ("foreign_card_rate", "Foreign Transaction Rate"),
                    ("repeat_transaction_rate", "Repeat Transaction Rate"),
                    ("initial_approval_rate", "Initial Approval Rate"),
                ],
                max_length=255,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="alertthreshold",
            name="trigger_frequency",
            field=models.CharField(
                blank=True,
                choices=[("Hourly", "Hourly"), ("daily", "Daily"), ("weekly", "Weekly"), ("monthly", "Monthly"), ("quarterly", "Quarterly"), ("yearly", "Yearly")],
                max_length=255,
                null=True,
            ),
        ),
    ]
