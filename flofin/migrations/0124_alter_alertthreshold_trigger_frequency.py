# Generated by Django 5.0.10 on 2024-12-19 04:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0123_alter_alertthreshold_dimension"),
    ]

    operations = [
        migrations.AlterField(
            model_name="alertthreshold",
            name="trigger_frequency",
            field=models.CharField(
                blank=True,
                choices=[("Hourly", "Hourly"), ("daily", "Daily"), ("weekly", "Weekly"), ("monthly", "Monthly"), ("Quarterly", "Quarterly"), ("Yearly", "Yearly")],
                max_length=255,
                null=True,
            ),
        ),
    ]
