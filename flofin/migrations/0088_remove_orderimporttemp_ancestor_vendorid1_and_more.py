# Generated by Django 5.0.10 on 2024-12-12 10:24

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('flofin', '0087_delete_homecbbymid_delete_homereports'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='ancestor_vendorid1',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='ancestor_vendorid2',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='campaign',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='card_infor',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='card_last4',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='card_type_flag1',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='cc_bin',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='cc_is_prepaid',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='continuity_product_cost',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='cpa_amount_script',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='currency',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='customer_id',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='cycle_num_lookup',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='g_delay_hours',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='g_i_ancestor_id',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='g_i_ancestor_t_date',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='g_i_direct_parent_id',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='g_i_step_parent_id',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='g_i_step_parent_t_date',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='g_is_unique_sale_attempt',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='g_projected_rebill_date',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='g_trans_att_num',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='import_product_identifier_1',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='import_product_identifier_2',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='import_product_identifier_3',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='initial_product_cost',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='is_3ds_verified',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='is_scrubbed',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='is_unique_sale_flag',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='merchant_descriptor',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='mid',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='offer_product',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='order_id',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='pr_global_grouping_field1',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='pre_tax_order_total',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='price_point',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='product_match',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='status_response',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='sub_order_id',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='transaction_type',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='vendorid1',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='vendorid2',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='vendorid3',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='vendorid4',
        ),
        migrations.RemoveField(
            model_name='orderimporttemp',
            name='vendorid5',
        ),
    ]
