# Generated by Django 5.0.10 on 2024-12-17 08:20

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data", "0002_clientlogininformation_active_and_more"),
        ("flofin", "0107_orderimport_retention_gt"),
    ]

    operations = [
        migrations.CreateModel(
            name="AlertThreshold",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "metric",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("chargeback_rate", "Chargeback Rate"),
                            ("refund_rate", "Refund Rate"),
                            ("void_rate", "Void Rate"),
                            ("transaction_rate", "Transaction Rate"),
                            ("foreign_transaction_rate", "Foreign Transaction Rate"),
                            ("repeat_transaction_rate", "Repeat Transaction Rate"),
                        ],
                        max_length=255,
                        null=True,
                    ),
                ),
                ("value", models.FloatField(blank=True, null=True)),
                (
                    "dimension",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("client", "Client"),
                            ("network", "Network"),
                            ("affiliate", "Affiliate"),
                            ("flow_brand_name", "Flow Brand Name"),
                            ("card_brand", "Card Brand"),
                            ("card_type", "Card Type"),
                            ("merchantId", "MID"),
                        ],
                        max_length=255,
                        null=True,
                    ),
                ),
                ("network", models.CharField(blank=True, max_length=255, null=True)),
                ("affiliate", models.CharField(blank=True, max_length=255, null=True)),
                ("flow_brand_name", models.CharField(blank=True, max_length=255, null=True)),
                ("card_brand", models.CharField(blank=True, max_length=255, null=True)),
                ("card_type", models.CharField(blank=True, max_length=255, null=True)),
                ("merchantId", models.CharField(blank=True, max_length=255, null=True)),
                ("alert_type", models.CharField(blank=True, max_length=255, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("trigger_frequency", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("client", models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to="data.clientsinformation")),
            ],
            options={
                "db_table": "FloFin_alert_threshold",
            },
        ),
    ]
