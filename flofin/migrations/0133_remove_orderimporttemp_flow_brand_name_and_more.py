# Generated by Django 5.0.8 on 2025-02-12 05:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0132_remove_homeclvreport_flow_brand_name_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="orderimporttemp",
            name="flow_brand_name",
        ),
        migrations.RemoveField(
            model_name="orderimporttemp",
            name="step_number",
        ),
        migrations.AddField(
            model_name="orderimporttemp",
            name="flow_brand",
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to="flofin.offerflow"),
        ),
        migrations.AddField(
            model_name="orderimporttemp",
            name="flow_step",
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to="flofin.offerflowsteps"),
        ),
    ]
