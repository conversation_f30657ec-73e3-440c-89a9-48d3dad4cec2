# Generated by Django 5.0.8 on 2024-08-19 10:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data", "0001_initial"),
        ("flofin", "0001_initial"),
    ]

    operations = [
        migrations.RenameModel(
            old_name="OiOuSummaryBackup",
            new_name="OiOuSummary",
        ),
        migrations.RenameField(
            model_name="orderimport",
            old_name="post_tax_order_total",
            new_name="price_point",
        ),
        migrations.RenameField(
            model_name="orderupdate",
            old_name="order_update_status",
            new_name="update_count",
        ),
        migrations.RenameField(
            model_name="quickreports",
            old_name="post_tax_order_total",
            new_name="price_point",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="pmt_processing_field1",
        ),
        migrations.AddField(
            model_name="orderimport",
            name="merchantId",
            field=models.Char<PERSON>ield(blank=True, max_length=45, null=True),
        ),
    ]
