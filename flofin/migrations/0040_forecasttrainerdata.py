# Generated by Django 5.0.8 on 2024-09-12 08:05

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data", "0001_initial"),
        ("flofin", "0039_delete_forecasttrainerdata"),
    ]

    operations = [
        migrations.CreateModel(
            name="ForecastTrainerData",
            fields=[
                ("referral_id", models.AutoField(primary_key=True, serialize=False)),
                ("i_new_id", models.IntegerField()),
                ("transaction_category", models.IntegerField(blank=True, null=True)),
                (
                    "flow_category",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                (
                    "traffic_source_name",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("transaction_date", models.DateTimeField(blank=True, null=True)),
                (
                    "amount",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("status_code", models.IntegerField()),
                (
                    "status_reason_code",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("merchantId", models.CharField(blank=True, max_length=45, null=True)),
                ("mid_name", models.CharField(blank=True, max_length=100, null=True)),
                ("cc_bin", models.IntegerField(blank=True, null=True)),
                ("cc_is_foreign_card", models.IntegerField(blank=True, null=True)),
                ("cc_is_repeat_card", models.IntegerField(blank=True, null=True)),
                ("cc_is_prepaid", models.IntegerField(blank=True, null=True)),
                ("is_3ds_verified", models.IntegerField(blank=True, null=True)),
                ("rebill_status", models.IntegerField(blank=True, null=True)),
                ("rebill_i_new_id", models.IntegerField(blank=True, null=True)),
                ("i_ancestor_i_new_id", models.IntegerField(blank=True, null=True)),
                ("i_ancestor_t_date", models.DateTimeField(blank=True, null=True)),
                ("i_step_parent_i_new_id", models.IntegerField(blank=True, null=True)),
                ("i_step_parent_t_date", models.DateTimeField(blank=True, null=True)),
                ("trans_att_num", models.IntegerField(blank=True, null=True)),
                ("cycle_num_lookup", models.IntegerField(blank=True, null=True)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "card_infor",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="flofin.cardbinlookup",
                    ),
                ),
                (
                    "client_login",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="data.clientlogininformation",
                    ),
                ),
                (
                    "offer_product",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="flofin.offerproductdetail",
                    ),
                ),
            ],
            options={
                "db_table": "FloFin_forecast_trainer_data",
            },
        ),
    ]
