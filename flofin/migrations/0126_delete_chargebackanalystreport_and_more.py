# Generated by Django 5.0.10 on 2024-12-19 07:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0125_alter_alertthreshold_metric_and_more"),
    ]

    operations = [
        migrations.DeleteModel(
            name="ChargebackAnalystReport",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="continuity_product_cost",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="cpa_amount_script",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="delay_hours",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="direct_parent_id",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="initial_product_cost",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="projected_rebill_date",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="step_parent_date",
        ),
        migrations.RemoveField(
            model_name="orderimport",
            name="step_parent_id",
        ),
        migrations.AlterField(
            model_name="alertthreshold",
            name="dimension",
            field=models.CharField(
                blank=True,
                choices=[
                    ("client", "Client"),
                    ("network", "Network"),
                    ("affiliate", "Affiliate"),
                    ("flow_brand_name", "Flow Title"),
                    ("card_brand", "Card Brand"),
                    ("card_type", "Card Type"),
                    ("merchantId", "MID"),
                    ("all", "All"),
                ],
                max_length=255,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="alertthreshold",
            name="metric",
            field=models.CharField(
                blank=True,
                choices=[
                    ("chargeback_rate", "Chargeback Rate"),
                    ("refund_rate", "Refund Rate"),
                    ("void_rate", "Void Rate"),
                    ("foreign_card_rate", "Foreign Transaction Rate"),
                    ("repeat_transaction_rate", "Repeat Transaction Rate"),
                    ("initial_approval_rate", "Initial Approval Rate"),
                    ("all", "All"),
                ],
                max_length=255,
                null=True,
            ),
        ),
    ]
