# Generated by Django 5.0.10 on 2024-12-18 06:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0117_remove_homeclvreport_update_date"),
    ]

    operations = [
        migrations.AlterField(
            model_name="homeclvreport",
            name="chargebacks_amount",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="refunds_amount",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="transactions_amount",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="homeclvreport",
            name="voids_amount",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
    ]
