# Generated by Django 4.2.14 on 2024-11-25 06:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0057_alter_offerflow_client_crm_setting_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="offerflow",
            name="status",
            field=models.CharField(blank=True, default="Active", max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="offerflowsteps",
            name="status",
            field=models.CharField(blank=True, default="Active", max_length=45, null=True),
        ),
        migrations.AlterField(
            model_name="offerproductdetail",
            name="status",
            field=models.CharField(blank=True, default="Active", max_length=45, null=True),
        ),
    ]
