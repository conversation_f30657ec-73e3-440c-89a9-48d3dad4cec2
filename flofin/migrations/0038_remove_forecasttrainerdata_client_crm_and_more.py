# Generated by Django 5.0.8 on 2024-09-12 07:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data", "0001_initial"),
        ("flofin", "0037_remove_orderimport_trans_attempt_updated_flag"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="forecasttrainerdata",
            name="client_crm",
        ),
        migrations.RemoveField(
            model_name="forecasttrainerdata",
            name="client_id",
        ),
        migrations.AddField(
            model_name="forecasttrainerdata",
            name="client_login",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="data.clientlogininformation",
            ),
        ),
    ]
