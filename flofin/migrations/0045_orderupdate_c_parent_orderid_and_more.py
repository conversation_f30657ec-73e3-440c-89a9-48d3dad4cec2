# Generated by Django 5.0.8 on 2024-09-19 02:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("flofin", "0044_orderimportwarehouse_rebill_i_new_id_and_more"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="orderupdate",
            name="c_parent_orderid",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderupdate",
            name="c_parent_suborderid",
            field=models.Char<PERSON>ield(blank=True, max_length=45, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="orderupdate",
            name="client_customerid",
            field=models.Char<PERSON>ield(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderupdate",
            name="historical_match_assist",
            field=models.CharField(blank=True, max_length=45, null=True),
        ),
        migrations.AddField(
            model_name="orderupdate",
            name="import_product_identifier_1",
            field=models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=45, null=True),
        ),
    ]
