from django.urls import path

from flofin.services import (
    KpiCLV,
    KpiDailyRp,
    SaleFlow,
    SaleQuality,
    ThresholdManage,
    TransactionInsights,
    utils,
)

daily_rp_path = "reporting/daily-performance-summary-reports/"
urlpatterns = [
    path("data/dashboard/getListClients", SaleFlow.getListClients),
    # ReportingHub
    path(
        "reporting/analysis/breakdown-by-issuer",
        TransactionInsights.getAnalysisBreakdownByIssuer,
    ),
    path("reporting/analysis/chart-view", TransactionInsights.getAnalysisChartView),
    path("reporting/flow-brand-name", TransactionInsights.get_flow_brand_name),
    path("reporting/flow-step-number", TransactionInsights.get_flow_step_number),
    path("reporting/card-brand", TransactionInsights.get_card_brand),
    path("reporting/card-type", TransactionInsights.get_card_type),
    path("reporting/price-point", TransactionInsights.get_price_point),
    path("reporting/cycle", TransactionInsights.get_cycle),
    path("reporting/issuer", TransactionInsights.get_issuer),
    path("reporting/3ds", TransactionInsights.get3ds),
    path("reporting/3ds-by-mid", TransactionInsights.get3ds_by_mid),
    path("reporting/download-report", TransactionInsights.downloadReport),
    path(
        "reporting/downloadReportChartView", TransactionInsights.downloadReportChartView
    ),
    path("reporting/meta-data", utils.getClvFilterMetaDataReporting),
    # KpiCLV
    path("reporting/customer-lifetime-value/meta-data", KpiCLV.getClvFilterMetaData),
    path("reporting/customer-lifetime-value/overall", KpiCLV.getCLV),
    path("reporting/customer-lifetime-value/getCashFlow", KpiCLV.getCashFlow),
    path("reporting/customer-lifetime-value/overview", KpiCLV.getClvToolTips),
    path("reporting/customer-lifetime-value/downloadCLV", KpiCLV.downloadCLV),
    # KpiDailyRp
    path(
        f"{daily_rp_path}filter",
        KpiDailyRp.getDailyPerformanceSummaryReportsFilterValue,
    ),
    path(f"{daily_rp_path}daily-sales-recap", KpiDailyRp.getDailySalesRecap),
    path(
        f"{daily_rp_path}daily-sales-recap/download",
        KpiDailyRp.download_daily_sales_recap,
    ),
    path(
        f"{daily_rp_path}traffic-source-performance",
        KpiDailyRp.getTrafficSourcePerformance,
    ),
    path(
        f"{daily_rp_path}traffic-source-performance/download",
        KpiDailyRp.download_traffic_source_performance,
    ),
    path(f"{daily_rp_path}3ds-coverage", KpiDailyRp.get3dsCoverageReport),
    path(
        f"{daily_rp_path}3ds-coverage/download", KpiDailyRp.download_3ds_coverage_report
    ),
    path(f"{daily_rp_path}midPerformanceAnalysis", KpiDailyRp.mid_performance_analysis),
    path(
        f"{daily_rp_path}midPerformanceAnalysis/download",
        KpiDailyRp.download_mid_performance_analysis,
    ),
    # SaleFlow
    path("sale-flow/getListClients", SaleFlow.getListClients),
    path("sale-flow/getLoginByClient", SaleFlow.getLoginByClient),
    path("sale-flow/getListProducts", SaleFlow.get_list_products),
    path("sale-flow/getTableSaleFlows", SaleFlow.getTableSaleFlows),
    path("sale-flow/adjustSaleFlow", SaleFlow.adjust_sales_flow),
    path("sale-flow/removeSaleFlow", SaleFlow.remove_sale_flow),
    path("sale-flow/viewSaleFlow", SaleFlow.view_sales_flow),
    # Fraud
    path("fraud/getFraudOverview", SaleQuality.getFraudOverview),
    path("fraud/getTrafficSourceTable", SaleQuality.getTrafficSourceTable),
    path(
        "fraud/getRecommendationTransactions", SaleQuality.getRecommendationTransactions
    ),
    path("fraud/downloadTrafficSourceTable", SaleQuality.downloadTrafficSourceTable),
    path("fraud/postActionTransaction", SaleQuality.postActionTransaction),
    path("fraud/meta-data", utils.getClvFilterMetaDataFraud),
    # AlertThreshold
    path("alert-threshold/createAlertThreshold", ThresholdManage.createAlertThreshold),
    path("alert-threshold/listAlertThresholds", ThresholdManage.listAlertThresholds),
    path("alert-threshold/deleteAlertThreshold", ThresholdManage.deleteAlertThreshold),
    path("alert-threshold/getAlertThreshold", ThresholdManage.getAlertThreshold),
    path(
        "alert-threshold/getAlertThresholdOverview",
        ThresholdManage.getAlertThresholdOverview,
    ),
    path("alert-threshold/getCurrentValue", ThresholdManage.getCurrentValue),
    path("alert-threshold/meta-data", utils.getClvFilterMetaDataAlertThreshold),
]
