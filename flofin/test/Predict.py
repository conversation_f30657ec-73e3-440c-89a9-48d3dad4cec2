from datetime import datetime, timedelta

import numpy as np
import pandas as pd
from django.db.models import Case, F
from django.db.models import Value as V
from django.db.models import When
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

from flofin.models import OrderImport

# Global variables
now = datetime.now()
searchStartDate = now - timedelta(days=200)

# Filtering ForecastTrainerData for relevant records
all_actual_data = OrderImport.objects.filter(
    flow_step_number=1,
    cycle_num_lookup=0,
    flow_category__isnull=False,  # flow_descriptor4
    approv_metric_1__isnull=False,
    approv_metric_2__isnull=False,
)
actual_data = all_actual_data.filter(status_code=1)

# Annotate relevant fields for issuing bank and credit card brand
actual_data = actual_data.annotate(
    summary_issuing_bank=Case(
        When(
            cc_issuing_bank__in=[
                "SUTTON BANK",
                "CAPITAL ONE BANK (USA), NATIONAL ASSOCIATION",
                "WELLS FARGO BANK, N.A.",
                "BANK OF AMERICA, N.A.",
                "JPMORGAN CHASE BANK, N.A.",
                "CHASE BANK USA, N.A.",
                "CITIBANK N.A.",
                "METABANK, NATIONAL ASSOCIATION",
                "DISCOVER BANK",
                "BANCORP BANK, THE",
                "COMERICA BANK",
                "SYNCHRONY BANK",
                "PNC BANK, N.A.",
            ],
            then=F("cc_issuing_bank"),
        ),
        default=V("Long-Tail"),
    ),
    summary_cc_brand=Case(
        When(cc_issuing_bank__in=["VISA", "MASTERCARD"], then=F("cc_brand")),
        default=V("Other"),
    ),
)

# Further filter the data
filtered_data = actual_data.filter(
    rebill_status__isnull=False,
    cc_is_foreign_card__isnull=False,
    cc_is_repeat_card__isnull=False,
    cc_is_prepaid__isnull=False,
    cc_type__isnull=False,
    is_3ds_verified__isnull=False,
    has_declined_upsell__isnull=False,
    pub_recent_repeat_card__isnull=False,
    pub_recent_foreign_card__isnull=False,
)

# Distinct flow categories
categories = filtered_data.values("flow_category").distinct().order_by()

# Logistic regression model creation


def run_logistic_regression(category, filtered_data, actual_data, all_actual_data):
    cat_subset = filtered_data.filter(flow_category=category["flow_category"])
    relevant_f_data = actual_data.filter(flow_category=category["flow_category"])
    relevant_all_actual_f_data = all_actual_data.filter(flow_category=category["flow_category"], forecasted__isnull=True)

    # Prepare DataFrames
    df = pd.DataFrame(
        list(
            cat_subset.values(
                "referral_id",
                "rebill_status",
                "summary_issuing_bank",
                "cc_is_foreign_card",
                "cc_is_repeat_card",
                "cc_is_prepaid",
                "summary_cc_brand",
                "cc_type",
                "is_3ds_verified",
                "has_declined_upsell",
                "approved_upsell_dollars",
                "pub_recent_repeat_card",
                "pub_recent_foreign_card",
                "approv_metric_1",
                "approv_metric_2",
            )
        )
    )

    df2 = pd.DataFrame(
        list(
            relevant_f_data.values(
                "referral_id",
                "rebill_status",
                "summary_issuing_bank",
                "cc_is_foreign_card",
                "cc_is_repeat_card",
                "cc_is_prepaid",
                "summary_cc_brand",
                "cc_type",
                "is_3ds_verified",
                "has_declined_upsell",
                "approved_upsell_dollars",
                "pub_recent_repeat_card",
                "pub_recent_foreign_card",
                "approv_metric_1",
                "approv_metric_2",
            )
        )
    )

    # Convert categorical values to dummy variables
    df = pd.get_dummies(
        df,
        columns=["summary_issuing_bank", "summary_cc_brand", "cc_type"],
        drop_first=True,
    )
    df2 = pd.get_dummies(
        df2,
        columns=["summary_issuing_bank", "summary_cc_brand", "cc_type"],
        drop_first=True,
    )

    # Prepare X and Y arrays
    X = df.drop(columns=["referral_id", "rebill_status"])
    X_full_data = df2.drop(columns=["referral_id", "rebill_status"])
    Y = df["rebill_status"]
    Y_full_data_match = df2["referral_id"]

    # Train-test split
    X_train, X_test, Y_train, Y_test = train_test_split(X, Y, test_size=0.2, random_state=0)

    # Standardize the data
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    X_full_data = scaler.transform(X_full_data)

    # Create and train the logistic regression model
    model = LogisticRegression(solver="liblinear", C=0.05, multi_class="ovr", random_state=0)
    model.fit(X_train, Y_train)

    # Predict on the test set
    Y_pred = model.predict(X_test)
    model.predict_proba(X_test)

    # Display model evaluation results
    print(classification_report(Y_test, Y_pred))
    print(f"Rebill Rate Predicted: {Y_pred.sum() / len(Y_pred)}")
    print(f"Rebill Rate Achieved: {Y_test.sum() / len(Y_test)}")
    print(f"Model Accuracy (Train): {model.score(X_train, Y_train)}")
    print(f"Model Accuracy (Test): {model.score(X_test, Y_test)}")

    # Apply the model to the full dataset
    Y_full_forecast = model.predict(X_full_data)
    Y_full_forecast_proba = model.predict_proba(X_full_data)

    # Update relevant forecasted records
    forecast_results = np.column_stack((Y_full_data_match, Y_full_forecast, Y_full_forecast_proba))
    for result in forecast_results:
        relevant_f_data.filter(referral_id=result[0]).update(
            transaction_stored_result=result[1],
            transaction_stored_probability=1 - result[2],
        )

    # Mark records as forecasted
    for order in relevant_all_actual_f_data:
        order.forecasted = 1
        order.save()


# Loop through each category and apply the logistic regression
for category in categories:
    run_logistic_regression(category, filtered_data, actual_data, all_actual_data)
