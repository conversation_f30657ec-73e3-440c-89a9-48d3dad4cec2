import datetime

from django.utils.timezone import now

from flofin.models import OrderImport, OrderImportWareHouse


class FTDUpdateService:
    def __init__(self, client_id):
        self.client_id = client_id
        self.start_date = datetime.datetime.now() - datetime.timedelta(days=17)

    def update_oi_to_ftd(self):
        oi_records = OrderImport.objects.filter(
            transaction_date__range=[self.start_date, now()],
        )
        print(f"Found {oi_records.count()} records to update to FTD")

        for oi in oi_records:
            if OrderImportWareHouse.objects.filter(id=oi.id).exists():
                continue
            child_orders = OrderImport.objects.filter(direct_parent_id=oi.id)
            child_success = child_orders.filter(order_status=1).values()

            if child_success.exists():
                rebill_status = 1
                rebill_id = child_success[0]["id"]
            elif child_orders.exists():
                rebill_status = 0
                rebill_id = child_orders.first()["id"]

            OrderImportWareHouse.objects.create(
                order_import=oi,
                rebill_status=rebill_status,
                rebill_id=rebill_id,
            )

    def run_hourly_updates(self):
        """
        Run all hourly updates in sequence.
        """
        self.update_oi_to_ftd()
