import concurrent.futures
import math
import time
from datetime import datetime, timedelta

import requests
from django.db import transaction
from tqdm import tqdm

from data.models import ClientLoginInformation
from flofin.models import OrderImport, OrderUpdate


class Step1GetDataFromKnk:
    def __init__(self, client_login: ClientLoginInformation):
        self.client_login = client_login
        self.crm_type = "KONNEKTIVE"
        self.SCRUB_KEY = [
            "Misc",
            "misc",
            "MISC",
            "Prepaid",
            "prepaid",
            "(PP",
            "pp",
            "Pre-paid",
            "pre-paid",
            "pre paid",
            "Pre paid",
            "scrap",
            "Scrap",
        ]
        self.PREPAID_KEY = [
            "Prepaid",
            "prepaid",
            "Pre-paid",
            "pre-paid",
            "pre paid",
            "Pre paid",
            "PP",
            "pp",
        ]

    def makeApiCall(
        self,
        client_login=ClientLoginInformation,
        start_date=(datetime.now() - timedelta(days=1)).strftime("%m/%d/%y"),
        end_date=datetime.now().strftime("%m/%d/%y"),
        page=1,
        results_per_page=200,
    ) -> dict:
        usr = str(client_login.username).replace("\n", "").replace(" ", "")
        pwd = (
            str(client_login.password)
            .replace("#", "%23")
            .replace("&", "%26")
            .replace("\n", "")
            .replace(" ", "")
        )
        start_time = "00:00:00"
        end_time = "23:59:59"
        url = (
            f"https://api.konnektive.com/transactions/query/?loginId={usr}&password={pwd}"
            f"&startDate={start_date}&endDate={end_date}&startTime={start_time}"
            f"&endTime={end_time}&resultsPerPage={results_per_page}"
            f"&exTestCards=1&page={page}"
        )
        response = requests.post(url, headers={}, data="")
        time.sleep(5)
        return response.json()

    def processOrderUpdate(self, order: dict, client_login=ClientLoginInformation):
        if order["txnType"] == "REFUND":
            update_date = order["dateCreated"]
            order_update_type = 1
            revenue_update = -float(order["totalAmount"])
            status_response = order["responseText"]
        elif order["txnType"] == "VOID":
            update_date = order["dateCreated"]
            order_update_type = 2
            revenue_update = -float(order["totalAmount"])
            status_response = order["responseText"]
        elif order["isChargedback"] == 1:
            update_date = order["chargebackDate"]
            order_update_type = 3
            revenue_update = float(order["chargebackAmount"])
            status_response = order["chargebackReasonCode"]
        elif order["txnType"] == "CAPTURE" and order["responseType"] != "SUCCESS":
            update_date = order["dateCreated"]
            order_update_type = 4
            revenue_update = -float(order["totalAmount"])
            status_response = order["responseText"]

        new_ou = OrderUpdate(
            client_login=client_login,
            order_id=order["orderId"],
            sub_order_id=order["transactionId"],
            parent_id=order.get("parentTxnId", ""),
            update_date=update_date,
            order_update_type=order_update_type,
            update_reason=order.get("refundReason", ""),
            revenue_update=revenue_update,
            product_match=order["items"][0]["product"]
            + ""
            + order["campaignCategoryName"]
            if order["items"]
            else "",
            update_status=1 if order["responseType"] == "SUCCESS" else 0,
            status_response=status_response,
            is_3ds_verified=0 if order["3DTxnResult"] == "false" else 1,
            crm_type=self.crm_type,
        )

        return new_ou

    def processOrderImport(
        self, order: dict, client_login=ClientLoginInformation
    ) -> None:
        total_amount = float(order.get("totalAmount", 0) or 0)
        surcharge = float(order.get("surcharge", 0) or 0)

        if order["sourceTitle"]:
            vendorid_1 = order["sourceTitle"]
        else:
            if "(" in order["campaignName"]:
                vendorid_1 = order["campaignName"].split("(")[1].split(" ")[0]
            else:
                vendorid_1 = order["campaignName"]

        new_oi = OrderImport(
            client_login=client_login,
            order_id=order["orderId"],
            sub_order_id=order["transactionId"],
            transaction_date=order["dateCreated"],
            import_product_identifier_1=order["items"][0]["product"]
            if order["items"]
            else "",
            import_product_identifier_2=order["items"][0]["productId"]
            if order["items"]
            else "",
            import_product_identifier_3=order["campaignCategoryName"],
            product_match=order["items"][0]["product"]
            + ""
            + order["campaignCategoryName"]
            if order["items"]
            else "",
            is_test=1
            if order["custom1"] == "" or order["cardType"] == "TESTCARD"
            else 0,
            pre_tax_order_total=total_amount - surcharge,
            price_point=total_amount,
            order_status=1 if order["responseType"] == "SUCCESS" else 0,
            status_response=order["responseText"],
            # 'c_ancestor_orderid': t['custom4'],
            vendorid1=vendorid_1,
            vendorid2=order.get("sourceValue1", ""),
            vendorid3=order.get("sourceValue2", ""),
            vendorid4=order.get("sourceValue3", ""),
            vendorid5=order.get("sourceValue4", ""),
            campaign=order["campaignName"],
            is_scrubbed=1
            if any(x in order["campaignName"] for x in self.SCRUB_KEY)
            else 0,
            payment_network=order["cardType"],
            customer_id=order["customerId"],
            # 'billing_address': f"{t['address1']}{t['address2']}, {t['city']}",
            # 'billing_state': t['state'],
            # 'billing_country': t['country'],
            # 'billing_zip_code': t['postalCode'],
            cc_bin=order["cardBin"],
            card_last4=order["cardLast4"],
            merchantId=order["merchantId"],
            merchant_descriptor=order["merchantDescriptor"],
            mid=order["midNumber"],
            pr_global_grouping_field1=order["campaignCategoryName"],
            card_type_flag1=order["paySource"],
            cc_is_prepaid=1
            if any(x in order["campaignName"] for x in self.PREPAID_KEY)
            else 0,
            currency=order["currencySymbol"],
            is_3ds_verified=0 if order["3DTxnResult"] == "false" else 1,
            transaction_type=order["txnType"],
            crm_type=self.crm_type,
            # 'historical_match_assist': t['orderId'],
            # "cpa_match_field = t["sourceTitle"],
            # 'cycle_number_import': t['items'][0].get('billingCycleNumber', '') if t['items'] else '',
            # 'counter_field': 1,
            # 'end_col': 0
        )

        return new_oi

    def getDataByDate(
        self,
        client_login: ClientLoginInformation,
        start_date=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
        end_date=datetime.now().strftime("%Y-%m-%d"),
    ) -> None:
        def process_page(page):
            r_json = self.makeApiCall(client_login, start_date, end_date, page=page)
            try:
                new_data = r_json.get("message", {}).get("data", [])
                # print(f'Page: {page} | Orders: {len(new_data)}')
            except Exception:
                print(f"Response: {r_json}")
                return process_page(page)

            lst_oi, lst_ou = [], []
            lst_all_ous = OrderUpdate.objects.filter(
                client_login__internalLoginID=client_login.internalLoginID
            ).values_list("order_id", "sub_order_id")
            lst_all_ois = OrderImport.objects.filter(
                client_login__internalLoginID=client_login.internalLoginID
            ).values_list("order_id", "sub_order_id")
            for order in new_data:
                order_id = order["orderId"]
                tnx_type = order["txnType"]
                is_chargeback = order["isChargedback"]
                transaction_id = order["transactionId"]
                
                if (
                    tnx_type in ["REFUND", "VOID", "CAPTURE"]
                    or is_chargeback == 1
                ):
                    # if (order_id, transaction_id) in lst_all_ous:
                    #     continue
                    ou = self.processOrderUpdate(order, client_login)
                    if ou:
                        lst_ou.append(ou)

                if tnx_type in ["SALE", "AUTHORIZE"]:
                    # if (
                    #     order_id,
                    #     transaction_id,
                    # ) in lst_all_ois and is_chargeback == 0:
                    #     continue
                    oi = self.processOrderImport(order, client_login)
                    if oi:
                        lst_oi.append(oi)

            with transaction.atomic():
                print(f"Page: {page} | Orders: {len(lst_oi)} | Updates: {len(lst_ou)}")

                OrderUpdate.objects.bulk_create(lst_ou)
                OrderImport.objects.bulk_create(lst_oi)

        fr_json = self.makeApiCall(client_login, start_date, end_date)
        if fr_json.get("result", False) == "ERROR":
            print(f"Error: {fr_json.get('message', {})}")
            return
        tR = int(fr_json.get("message", {}).get("totalResults", 0))
        rPP = int(fr_json.get("message", {}).get("resultsPerPage", 200))
        pages = math.ceil(tR / rPP)

        print(
            f"Starting to run client: {client_login.mainClientUsed}",
            f"  | Total pages: {pages}",
        )

        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            list(tqdm(executor.map(process_page, range(1, pages + 1)), total=pages))

    def getDataKnk(
        self,
        start_date=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
        end_date=datetime.now().strftime("%Y-%m-%d"),
    ) -> None:
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
        delta = (end_date_obj - start_date_obj).days

        if delta > 5:
            current_start_date = start_date_obj
            while current_start_date < end_date_obj:
                current_end_date = min(
                    current_start_date + timedelta(days=3), end_date_obj
                )
                print(
                    f"---> Start Date: {current_start_date}, End Date: {current_end_date}"
                )
                self.getDataByDate(
                    self.client_login,
                    current_start_date.strftime("%Y-%m-%d"),
                    current_end_date.strftime("%Y-%m-%d"),
                )
                current_start_date = current_end_date + timedelta(days=1)
        else:
            self.getDataByDate(self.client_login, start_date, end_date)
