import math
import time
from datetime import datetime, timedelta
from multiprocessing import Pool
from typing import List

import requests
from django.db import transaction

from data.models import ClientLoginInformation, MerchantsInformation
from flofin.models import OrderImport, OrderUpdate
from django.db import connections
from django.core.exceptions import MultipleObjectsReturned
from tqdm import tqdm


STICKY_DATE_FORMAT = "%m/%d/%Y"


class Step1GetDataFromSticky:
    def __init__(self, client_login: ClientLoginInformation) -> None:
        self.client_login = client_login
        self.crm_type = "STICKY"
        raw_url = client_login.URL
        self.base_url = (
            raw_url[: raw_url.find(".io") + 3]
            if ".io" in raw_url
            else raw_url[: raw_url.find(".com") + 4]
        )

    def format_date(self, date_str):
        if not date_str:
            return None
        try:
            # Parse the date string directly to datetime
            dt = datetime.fromisoformat(date_str)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            return None

    def get_total_orders(
        self,
        start_date="08/01/2024",
        end_date=datetime.now().strftime(STICKY_DATE_FORMAT),
    ):
        url_order_find = f"{self.base_url}/api/v1/order_find"
        for i in range(3):
            try:
                r = requests.post(
                    url_order_find,
                    auth=(self.client_login.username, self.client_login.password),
                    json={
                        "campaign_id": "all",
                        "start_date": start_date,
                        "end_date": end_date,
                        "date_type": "create",
                        "criteria": {"email": "**"},
                    },
                )
                print("GET STICKY ORDERS API URL: ", r.request.body)
                if r.status_code == 200:
                    orders = r.json()
                    break
            except requests.exceptions.RequestException as e:
                print(f"Error: {e} get total orders from sticky failed, retry {i + 1}")
                time.sleep(1)
                if i == 2:
                    print(
                        f"Error: {e} get total orders from sticky failed after 3 retries"
                    )

        start_date = datetime.strptime(start_date, STICKY_DATE_FORMAT).date()

        order_list = orders.get("order_id", [])
        if not order_list:
            print("No order found!")

        return order_list

    def create_order_update(self, ou, update_type=3, product_name=""):
        if update_type == 1:
            update_date = self.format_date(
                ou.get("refund_date", ou.get("time_stamp", ou.get("acquisition_date")))
            )
            revenue_update = float(ou.get("refund_amount", 0))

        if update_type == 2:
            update_date = self.format_date(
                ou.get("void_date", ou.get("time_stamp", ou.get("acquisition_date")))
            )
            revenue_update = float(ou.get("void_amount", 0))

        if update_type == 3:
            update_date = self.format_date(
                ou.get(
                    "chargeback_date", ou.get("time_stamp", ou.get("acquisition_date"))
                )
            )
            revenue_update = float(ou.get("order_total", 0))

        order_id = ou.get("order_id", "")
        parent_id = ou.get("parent_id", order_id)
        try:
            new_ou, created = OrderUpdate.objects.get_or_create(
                client_login=self.client_login,
                order_id=order_id,
                sub_order_id=ou.get("transaction_id", ""),
                parent_id=parent_id,
                update_date=update_date,
                order_update_type=update_type,
                revenue_update=revenue_update,
                import_product_identifier_1=ou["products"][0]["name"],
                product_match=product_name,
                customer_id=ou["customer_id"],
                update_status=1,
                is_3ds_verified=1 if ou["is_3d_protected"].lower() != "no" else 0,
                c_parent_orderid=ou["order_id"],
                c_parent_suborderid=ou["order_id"],
                crm_type=self.crm_type,
            )
            if created:
                return new_ou
        except OrderUpdate.MultipleObjectsReturned:
            print(f"Duplicate OrderUpdate for order_id={order_id}, keeping first and deleting others.")
            duplicate_records = OrderUpdate.objects.filter(
                client_login=self.client_login,
                order_id=order_id,
                sub_order_id=ou.get("transaction_id", ""),
                parent_id=parent_id,
                update_date=update_date,
                order_update_type=update_type,
                revenue_update=revenue_update,
                import_product_identifier_1=ou["products"][0]["name"],
                product_match=product_name,
                customer_id=ou["customer_id"],
                update_status=1,
                is_3ds_verified=1 if ou["is_3d_protected"].lower() != "no" else 0,
                c_parent_orderid=ou["order_id"],
                c_parent_suborderid=ou["order_id"],
                crm_type=self.crm_type,
            )
            first_ou = duplicate_records.first()
            # Delete all except the first
            duplicate_records.exclude(id=first_ou.id).delete()
            return None

    def create_order_import(
        self, oi, campaign="", is_test=0, price_point=0, product_name=""
    ):
        try:
            custom_field_value = oi["custom_fields"][1]["values"][0]["value"]
            c2list = custom_field_value.split("|")
            is_scrubbed = 1 if c2list[2].lower() == "s" else 0
        except (KeyError, IndexError, AttributeError):
            is_scrubbed = 0

        try:
            pr_global_grouping_field1 = oi["products"][0]["offer"]["name"][:40]
        except (KeyError, IndexError):
            pr_global_grouping_field1 = ""

        gateway_id = oi.get("gateway_id")
        mid = None
        merchant_id = None
        if gateway_id:
            merchant_info = MerchantsInformation.objects.filter(
                clientID=self.client_login.listClientsUsed, CRMID1=gateway_id
            )
            if merchant_info.exists():
                merchant = merchant_info.first()
                mid = merchant.MID
                merchant_id = merchant.id

        new_oi = OrderImport(
            client_login=self.client_login,
            order_id=oi["order_id"],
            sub_order_id=oi["transaction_id"],
            transaction_date=self.format_date(
                oi.get("time_stamp", oi.get("acquisition_date"))
            ),
            import_product_identifier_1=oi["products"][0]["name"],
            import_product_identifier_2=oi["products"][0]["name"]
            + oi["products"][0]["sku"],
            product_match=product_name,
            is_test=is_test,
            pre_tax_order_total=float(oi["order_total"])
            - float(oi["order_sales_tax_amount"]),
            price_point=price_point,
            order_status=1 if oi["order_status"] == "2" else 0,
            status_response=oi["decline_reason"][:255]
            if oi["decline_reason"]
            else None,
            vendorid1=oi["affiliate"],
            vendorid2=oi["sid"],
            vendorid3=oi["afid"],
            vendorid4=oi["affid"],
            campaign=campaign,
            is_scrubbed=is_scrubbed,
            payment_network=oi["cc_type"],
            customer_id=oi["customer_id"],
            cc_bin=oi["cc_first_6"] if oi["cc_first_6"] != "" else None,
            card_last4=oi["cc_last_4"] if oi["cc_last_4"] != "" else None,
            merchant_descriptor=oi["gateway_descriptor"],
            pr_global_grouping_field1=pr_global_grouping_field1,
            cc_is_prepaid=1 if oi["prepaid_match"].lower() != "no" else 0,
            currency="USD",
            is_3ds_verified=1 if oi["is_3d_protected"].lower() == "yes" else 0,
            transaction_type=oi["order_status"],
            crm_type=self.crm_type,
            mid=mid,
            merchantId=merchant_id,
        )
        return new_oi

    def create_order_records(self, list_orders: dict):
        orders_import = []
        orders_update = []

        exist_order_import_ids = set(
            OrderImport.objects.filter(
                client_login=self.client_login, crm_type=self.crm_type
            )
            .values_list("order_id", flat=True)
            .distinct()
        )

        for order_id, order_info in tqdm(list_orders.items(), desc="Creating orders"):
            try:
                products = order_info.get("products", [])
                if not products:
                    print(f"Order {order_id} has no products, skipping...")
                    continue

                campaign = products[0].get("offer", {}).get("name", "")
                is_test = order_info.get("is_test_cc", "0")
                custom_fields = order_info.get("custom_fields", [])
                if len(custom_fields) < 2 or is_test == "1":
                    is_test = 1
                else:
                    is_test = 0
                price_point = products[0].get("price", 0)
                product_name = products[0].get("name", "")

                is_chargeback = order_info.get("is_chargeback", "0")
                if order_id not in exist_order_import_ids:
                    try:
                        order_import = self.create_order_import(
                            oi=order_info,
                            campaign=campaign,
                            is_test=is_test,
                            price_point=price_point,
                            product_name=product_name,
                        )
                        orders_import.append(order_import)
                    except Exception as e:
                        print(
                            f"Error creating OrderImport for order {order_id}: {str(e)}"
                        )
                        continue

                try:
                    order_update = None
                    if is_chargeback == "1":
                        order_update = self.create_order_update(
                            ou=order_info, update_type=3, product_name=product_name
                        )
                    is_void = order_info.get("is_void", "no")
                    if is_void == "yes":
                        order_update = self.create_order_update(
                            ou=order_info, update_type=2, product_name=product_name
                        )
                    is_refund = order_info.get("is_refund", "no")
                    if is_refund == "yes":
                        order_update = self.create_order_update(
                            ou=order_info, update_type=1, product_name=product_name
                        )
                    if order_update is not None:
                        orders_update.append(order_update)
                except Exception as e:
                    print(f"Error creating OrderUpdate for order {order_id}: {str(e)}")
                    continue
            except Exception as e:
                print(f"Error processing order {order_id}: {str(e)}")
                continue

        print(
            f"Created OrderImport: {len(orders_import)}, Created OrderUpdate: {len(orders_update)}"
        )

        created_orderimport_ids = []
        created_orderupdate_ids = []

        try:
            with transaction.atomic():
                if orders_import:
                    created_orderimport_ids = OrderImport.objects.bulk_create(
                        orders_import
                    )
                    print(
                        f"Successfully created {len(created_orderimport_ids)} OrderImports"
                    )
        except Exception as e:
            print(f"Error bulk creating OrderImports: {str(e)}")
            # Try creating records one by one if bulk create fails
            for order_import in orders_import:
                try:
                    with transaction.atomic():
                        created = OrderImport.objects.create(**order_import.__dict__)
                        created_orderimport_ids.append(created)
                except Exception as e:
                    print(f"Error creating individual OrderImport: {str(e)}")
                    continue

        try:
            with transaction.atomic():
                if orders_update:
                    created_orderupdate_ids = OrderUpdate.objects.bulk_create(
                        orders_update
                    )
                    print(
                        f"Successfully created {len(created_orderupdate_ids)} OrderUpdates"
                    )
        except Exception as e:
            print(f"Error bulk creating OrderUpdates: {str(e)}")
            # Try creating records one by one if bulk create fails
            for order_update in orders_update:
                try:
                    with transaction.atomic():
                        created = OrderUpdate.objects.create(**order_update.__dict__)
                        created_orderupdate_ids.append(created)
                except Exception as e:
                    print(f"Error creating individual OrderUpdate: {str(e)}")
                    continue

        return created_orderimport_ids, created_orderupdate_ids

    def processing_orders(self, list_order_ids: List):
        url_order_view = f"{self.base_url}/api/v1/order_view"

        # max_allowed_order_ids per request is 500
        total_page = math.ceil(len(list_order_ids) / 500)
        page = 0

        orders_import = []
        orders_update = []
        while page < total_page:
            print(f"Processing page {page + 1}/{total_page}...")
            for i in range(3):
                try:
                    r = requests.post(
                        url_order_view,
                        auth=(self.client_login.username, self.client_login.password),
                        json={"order_id": list_order_ids[(page * 500) : (page + 1) * 500]},
                    )
                    if r.status_code == 200:
                        orders = r.json()
                        break
                    else:
                        print(f"Error: Received status code {r.status_code}, retry {i + 1}")
                except requests.exceptions.RequestException as e:
                    print(f"Error: {e} when getting order details, retry {i + 1}")
                    time.sleep(1)
                    if i == 2:
                        print(f"Error: {e} after 3 retries")
                        orders = {}
                        break
                    
            list_orders = orders.get("data", {})
            if not list_orders:
                print(f"No orders found for page {page + 1}")
                page += 1
                continue
            _orders_import, _orders_update = self.create_order_records(
                list_orders=list_orders
            )
            orders_import.extend(_orders_import)
            orders_update.extend(_orders_update)
            page = page + 1

        print(
            f"Processing orders completed with {len(orders_import)} OrderImports and {len(orders_update)} OrderUpdates"
        )

    def processing_daily_data(self, daily_order_ids):
        self.processing_orders(list_order_ids=daily_order_ids)

    def get_one_day_data(
        self,
        date: datetime,
    ):
        start_date = date.strftime(STICKY_DATE_FORMAT)
        end_date = date.strftime(STICKY_DATE_FORMAT)

        daily_order_ids = self.get_total_orders(start_date, end_date)
        if not daily_order_ids:
            print(f"No orders found for {date}")
            return
        print(f"-----------------------> {date} Total Orders: {len(daily_order_ids)}")
        print(f"Start process data for {date}")
        self.processing_daily_data(daily_order_ids)

    def get_sticky_data(
        self, start_date="08/01/2024", end_date=datetime.now().strftime("%Y-%m-%d")
    ):
        start_date = datetime.strptime(start_date, "%Y-%m-%d")
        end_date = datetime.strptime(end_date, "%Y-%m-%d")

        date_list = []
        while start_date < end_date:
            date_list.append(start_date)
            start_date = start_date + timedelta(days=1)

        print(f"Total dates to process: {len(date_list)}")
        print("Starting to get sticky data...")

        connections.close_all()
        with Pool(processes=8) as pool:
            pool.map(self.process_date, date_list)

        print("Done")

    def process_date(self, date):
        print(f"---> {date}")
        try:
            self.get_one_day_data(date)
        except Exception as e:
            print(f"Error: {e}")
            time.sleep(5)
            self.get_one_day_data(date)
