import csv
import random
from datetime import datetime, timedelta

from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    IntegerField,
    Q,
    Sum,
    Value,
    When,
    Subquery,
)
from django.db.models.functions import Cast, NullIf, TruncDate
from django.db.models.query import QuerySet
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.utils.dateparse import parse_date
from django.views.decorators.csrf import csrf_exempt
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework.decorators import api_view
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_405_METHOD_NOT_ALLOWED,
)

from flofin.models import (
    HomeCLVReport,
    OrderImportTemp,
    OrderImport,
    OfferProductDetail,
    CardBinLookup,
)
from data.models import ClientLoginInformation, MerchantsInformation
from flofin.serializers import (
    GetDataUnsuccessfulResponseSerializer,
    TrafficSourcePerformanceResponseSerializer,
    get3dsCoverageReportResponseSerializer,
    getDailyPerformanceSummaryReportsFilterValueResponseSerializer,
    getDailySalesRecapResponseSerializer,
    inValidMethodResponseSerializer,
)
from flofin.services import TABLE_PARAMS
from flofin.services.utils import getClientView
from user.utils.utils import require_login
from django.core.paginator import Paginator, EmptyPage

DAILY_SALES_RECAP_FILTER_PARAMS = [
    OpenApiParameter(
        name="flow_step_number[]",
        description="Flow Step - list (ex: [1,2,3])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="flow_brand_name[]",
        description="Flow Title - list (ex: ['abc','123'])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="card_brand[]",
        description="Card Brand - list (ex: ['visa','mastercard'])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="card_type[]",
        description="Card Type - list (ex: ['credit','debit'])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="clientID[]",
        description="Client ID - list (ex: [1,2,3])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="start_date",
        description="Start Date - format: YYYY-MM-DD",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="end_date",
        description="End Date - format: YYYY-MM-DD",
        required=False,
        type=str,
    ),
]


def getDailyPerformanceSummary(
    request, queryset: QuerySet[HomeCLVReport]
) -> QuerySet[HomeCLVReport]:
    flow_brand_name = request.GET.getlist("flow_brand_name[]", None)
    queryset = queryset.annotate(
        flow_brand_name=F("flow_brand__flow_brand_name"),
        flow_step_number=F("flow_step__flow_step_number"),
    )

    flow_step_number = request.GET.getlist("flow_step_number[]", None)
    card_brand = request.GET.getlist("card_brand[]", None)
    card_type = request.GET.getlist("card_type[]", None)
    clientID = request.GET.getlist("clientID[]", None)
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")

    lst_clients, permissions = getClientView(request)
    if "SUPER-PARENT" in permissions.split("_"):
        clientID = [lst_clients[0]["clientID"]]
    if clientID:
        queryset = queryset.filter(clientID__in=clientID)
    if flow_step_number:
        queryset = queryset.filter(flow_step_number__in=flow_step_number)
    if flow_brand_name:
        queryset = queryset.filter(flow_brand_name__in=flow_brand_name)
    if card_brand:
        queryset = queryset.filter(card_brand__in=card_brand)
    if start_date:
        queryset = queryset.filter(ancestor_date__gte=parse_date(start_date))
    if end_date:
        queryset = queryset.filter(ancestor_date__lte=parse_date(end_date))
    if card_type:
        queryset = queryset.filter(card_type__in=card_type)
    return queryset


def create_daily_sales_recap_data(request: HttpRequest) -> tuple[list[dict], dict]:
    queryset = HomeCLVReport.objects.filter().exclude(flow_brand__status="Deleted")
    queryset = getDailyPerformanceSummary(request, queryset)

    metrics = (
        queryset.annotate(date=TruncDate("ancestor_date"))
        .values("date")
        .order_by("date")
        .annotate(
            sales_count=Sum(F("total_unique_sale")),
            processed_volume=Sum(F("post_tax_order_total")),
        )
        .values("date", "sales_count", "processed_volume")
    )

    output = []
    total = {"sales_count": 0, "processed_volume": 0}

    for metric in metrics:
        date_metric = metric["date"]
        data = {
            "month": date_metric.strftime("%b") if date_metric else None,
            "date": date_metric.strftime("%Y-%m-%d") if date_metric else None,
            "sales_count": metric["sales_count"],
            "processed_volume": metric["processed_volume"],
        }
        output.append(data)

        total["sales_count"] += data["sales_count"] if data["sales_count"] else 0
        total["processed_volume"] += (
            data["processed_volume"] if data["processed_volume"] else 0
        )

    return output, total


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS,
    responses={
        HTTP_200_OK: getDailySalesRecapResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Daily Sales Recap",
    tags=["Daily Performance Summary Reports"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getDailySalesRecap(request: HttpRequest) -> JsonResponse:
    # try:
    output, total = create_daily_sales_recap_data(request)

    return JsonResponse(
        {
            "message": "Daily Sales Recap data retrieved successfully.",
            "data": output,
            "total": total,
        },
        status=HTTP_200_OK,
    )


# except Exception:
#     return JsonResponse({"message": "Failed to Get Daily Sales Recap data"}, status=HTTP_400_BAD_REQUEST)


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS,
    responses={
        HTTP_200_OK: getDailySalesRecapResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Daily Sales Recap",
    tags=["Daily Performance Summary Reports"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def download_daily_sales_recap(request: HttpRequest) -> JsonResponse:
    output, total = create_daily_sales_recap_data(request)

    if not output:
        return JsonResponse({"message": "No data found"}, status=HTTP_200_OK)

    header = ["Month", "Date", "Sales Count", "Processed Volume"]

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = 'attachment;filename="daily_sales_recap.csv"'
    writer = csv.writer(response)
    writer.writerow(header)
    for row in output:
        writer.writerow(
            [row["month"], row["date"], row["sales_count"], row["processed_volume"]]
        )
    writer.writerow(["", "Total", total["sales_count"], total["processed_volume"]])

    return response


@extend_schema(
    parameters=[],
    responses={
        HTTP_200_OK: getDailyPerformanceSummaryReportsFilterValueResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Filter Data",
    tags=["Daily Performance Summary Reports"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getDailyPerformanceSummaryReportsFilterValue(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        try:
            home_reports = HomeCLVReport.objects.all().exclude(
                flow_brand__status="Deleted"
            )
            home_reports = home_reports.annotate(
                flow_brand_name=F("flow_brand__flow_brand_name"),
                flow_step_number=F("flow_step__flow_step_number"),
            )
            home_reports = home_reports.exclude(
                ancestor_date=None, flow_brand_name=None
            )

            home_reports = getDailyPerformanceSummary(request, home_reports)
            flow_brand_name = (
                home_reports.exclude(ancestor_date=None)
                .order_by("flow_brand_name")
                .values_list("flow_brand_name", flat=True)
                .distinct()
            )
            flow_step_number = (
                home_reports.exclude(flow_step_number=None)
                .order_by("flow_step_number")
                .values_list("flow_step_number", flat=True)
                .distinct()
            )
            card_brand = (
                home_reports.exclude(card_brand=None)
                .order_by("card_brand")
                .values_list("card_brand", flat=True)
                .distinct()
            )
            card_type = (
                home_reports.exclude(card_type=None)
                .order_by("card_type")
                .values_list("card_type", flat=True)
                .distinct()
            )

            return JsonResponse(
                {
                    "message": "Get Daily Performance Summary Reports Filter Values successfully.",
                    "data": {
                        "flow_brand_name": sorted(list(flow_brand_name)),
                        "card_brand": sorted(list(card_brand)),
                        "flow_step_number": sorted(list(flow_step_number)),
                        "card_type": sorted(list(card_type)),
                    },
                },
                status=HTTP_200_OK,
            )

        except Exception as e:
            return JsonResponse(
                {"message": "Get data unsuccessfully", "exception": str(e)},
                status=HTTP_400_BAD_REQUEST,
            )
    return JsonResponse(
        {"message": "Invalid request method"}, status=HTTP_405_METHOD_NOT_ALLOWED
    )


def create_traffic_source_performance_data(request: HttpRequest) -> list:
    queryset = HomeCLVReport.objects.filter(cycle_num=0, network__isnull=False).exclude(
        flow_brand__status="Deleted"
    )
    queryset = getDailyPerformanceSummary(request, queryset)

    metrics = (
        queryset.values("client", "flow_brand_name", "network", "card_brand", "pub")
        .annotate(affiliate=F("pub"))
        .annotate(
            flow_step_1_sales=Sum(
                Case(
                    When(flow_step_number=1, then=F("count_order")),
                    default=0,
                    output_field=IntegerField(),
                )
            ),
            flow_step_1_sales_success=NullIf(
                Sum(
                    Case(
                        When(flow_step_number=1, then=F("total_sales")),
                        default=0,
                        output_field=IntegerField(),
                    )
                ),
                0,
            ),
            flow_step_1_rate=Cast(
                F("flow_step_1_sales_success") * 100.0 / F("flow_step_1_sales"),
                output_field=DecimalField(max_digits=5, decimal_places=2),
            ),
            flow_step_2_sales=Sum(
                Case(
                    When(flow_step_number=2, then=F("count_order")),
                    default=0,
                    output_field=IntegerField(),
                )
            ),
            flow_step_2_sales_success=NullIf(
                Sum(
                    Case(
                        When(flow_step_number=2, then=F("total_sales")),
                        default=0,
                        output_field=IntegerField(),
                    )
                ),
                0,
            ),
            flow_step_2_rate=Cast(
                F("flow_step_2_sales_success") * 100.0 / F("flow_step_2_sales"),
                output_field=DecimalField(max_digits=5, decimal_places=2),
            ),
            flow_step_3_sales=Sum(
                Case(
                    When(flow_step_number=3, then=F("count_order")),
                    default=0,
                    output_field=IntegerField(),
                )
            ),
            flow_step_3_sales_success=NullIf(
                Sum(
                    Case(
                        When(flow_step_number=3, then=F("total_sales")),
                        default=0,
                        output_field=IntegerField(),
                    )
                ),
                0,
            ),
            flow_step_3_rate=Cast(
                F("flow_step_3_sales_success") * 100.0 / F("flow_step_3_sales"),
                output_field=DecimalField(max_digits=5, decimal_places=2),
            ),
            flow_step_4_sales=Sum(
                Case(
                    When(flow_step_number=4, then=F("count_order")),
                    default=0,
                    output_field=IntegerField(),
                )
            ),
            flow_step_4_sales_success=NullIf(
                Sum(
                    Case(
                        When(flow_step_number=4, then=F("total_sales")),
                        default=0,
                        output_field=IntegerField(),
                    )
                ),
                0,
            ),
            flow_step_4_rate=Cast(
                F("flow_step_4_sales_success") * 100.0 / F("flow_step_4_sales"),
                output_field=DecimalField(max_digits=5, decimal_places=2),
            ),
            flow_step_5_sales=Sum(
                Case(
                    When(flow_step_number__gte=5, then=F("count_order")),
                    default=0,
                    output_field=IntegerField(),
                )
            ),
            flow_step_5_sales_success=NullIf(
                Sum(
                    Case(
                        When(flow_step_number__gte=5, then=F("total_sales")),
                        default=0,
                        output_field=IntegerField(),
                    )
                ),
                0,
            ),
            flow_step_5_rate=Cast(
                F("flow_step_5_sales_success") * 100.0 / F("flow_step_5_sales"),
                output_field=DecimalField(max_digits=5, decimal_places=2),
            ),
        )
        .order_by("client", "flow_brand_name", "network", "affiliate", "card_brand")
        .values(
            "client",
            "flow_brand_name",
            "network",
            "affiliate",
            "card_brand",
            "flow_step_1_sales",
            "flow_step_1_sales_success",
            "flow_step_1_rate",
            "flow_step_2_sales",
            "flow_step_2_sales_success",
            "flow_step_2_rate",
            "flow_step_3_sales",
            "flow_step_3_sales_success",
            "flow_step_3_rate",
            "flow_step_4_sales",
            "flow_step_4_sales_success",
            "flow_step_4_rate",
            "flow_step_5_sales",
            "flow_step_5_sales_success",
            "flow_step_5_rate",
        )
    )

    metrics = metrics.filter(flow_step_1_sales__gt=0, flow_step_1_rate__gt=0)

    return list(metrics)


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS,
    responses={
        HTTP_200_OK: TrafficSourcePerformanceResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Traffic Source Performance Report",
    tags=["Daily Performance Summary Reports"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getTrafficSourcePerformance(request: HttpRequest) -> JsonResponse:
    # try:
    traffic_source_performance_data = create_traffic_source_performance_data(request)
    return JsonResponse(
        {
            "message": "Traffic Source Performance data retrieved successfully.",
            "data": traffic_source_performance_data,
        },
        status=HTTP_200_OK,
    )


# except Exception as e:
#     return JsonResponse(
#         {
#             "message": "Failed to retrieve Traffic Source Performance data",
#             "exception": str(e),
#         },
#         status=HTTP_400_BAD_REQUEST,
#     )


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS,
    responses={
        HTTP_200_OK: TrafficSourcePerformanceResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Traffic Source Performance Report",
    tags=["Daily Performance Summary Reports"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def download_traffic_source_performance(request: HttpRequest) -> JsonResponse:
    traffic_source_performance_data = create_traffic_source_performance_data(request)
    if not traffic_source_performance_data:
        return JsonResponse({"message": "No data found"}, status=HTTP_200_OK)

    header_to_key = {
        "Client": "client",
        "Network": "network",
        "Card Brand": "card_brand",
        "Flow Title": "flow_brand_name",
        "Affiliate": "affiliate",
        "Flow Step 1 Sales": "flow_step_1_sales",
        "Flow Step 1 Sales Success": "flow_step_1_sales_success",
        "Flow Step 1 Rate": "flow_step_1_rate",
        "Flow Step 2 Sales": "flow_step_2_sales",
        "Flow Step 2 Sales Success": "flow_step_2_sales_success",
        "Flow Step 2 Rate": "flow_step_2_rate",
        "Flow Step 3 Sales": "flow_step_3_sales",
        "Flow Step 3 Sales Success": "flow_step_3_sales_success",
        "Flow Step 3 Rate": "flow_step_3_rate",
        "Flow Step 4 Sales": "flow_step_4_sales",
        "Flow Step 4 Sales Success": "flow_step_4_sales_success",
        "Flow Step 4 Rate": "flow_step_4_rate",
        "Flow Step 5 Sales": "flow_step_5_sales",
        "Flow Step 5 Sales Success": "flow_step_5_sales_success",
        "Flow Step 5 Rate": "flow_step_5_rate",
    }

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = (
        'attachment;filename="traffic_source_performance.csv"'
    )
    writer = csv.writer(response)
    writer.writerow(header_to_key.keys())

    for row in traffic_source_performance_data:
        writer.writerow([row[key] for key in header_to_key.values()])

    return response


def create_3ds_coverage_data(request: HttpRequest) -> list:
    queryset = HomeCLVReport.objects.filter(cycle_num=0).exclude(
        flow_brand__status="Deleted"
    )
    queryset = getDailyPerformanceSummary(request, queryset)
    coverage_data = (
        queryset.values("flow_step_number", "flow_brand_name", "card_brand")
        .annotate(
            sales_with_3ds=Sum("total_3ds"),
            total_sales=Sum(F("total_sales")),
        )
        .order_by("flow_step_number", "flow_brand_name", "card_brand")
    )
    # print(coverage_data)

    # coverage_data = coverage_data.filter(total_sales__gt=0, sales_with_3ds__gt=0)
    for data in coverage_data:
        if data["sales_with_3ds"] is None or data["total_sales"] is None:
            data["three_ds_coverage"] = 0
        else:
            data["three_ds_coverage"] = (
                data["sales_with_3ds"] * 100.0 / data["total_sales"]
                if data["total_sales"] > 0
                else 0
            )

    return list(coverage_data)


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS,
    responses={
        HTTP_200_OK: get3dsCoverageReportResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },  # You might want to create a custom serializer for the response
    auth=None,
    operation_id="3DS Coverage Report",
    tags=["Daily Performance Summary Reports"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get3dsCoverageReport(request: HttpRequest) -> JsonResponse:
    # try:
    coverage_data = create_3ds_coverage_data(request)

    return JsonResponse(
        {
            "message": "3DS Coverage Report generated successfully.",
            "data": coverage_data,
        },
        status=HTTP_200_OK,
    )


# except Exception as e:
#     return JsonResponse(
#         {"message": "Failed to generate 3DS Coverage Report", "error": str(e)},
#         status=HTTP_400_BAD_REQUEST,
#     )


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS,
    responses={
        HTTP_200_OK: get3dsCoverageReportResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },  # You might want to create a custom serializer for the response
    auth=None,
    operation_id="3DS Coverage Report",
    tags=["Daily Performance Summary Reports"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def download_3ds_coverage_report(request: HttpRequest) -> JsonResponse:
    # try:
    coverage_data = create_3ds_coverage_data(request)

    if not coverage_data:
        return JsonResponse({"message": "No data found"}, status=HTTP_200_OK)

    header_to_key = {
        "Card Brand": "card_brand",
        "Flow Step Number": "flow_step_number",
        "Flow Brand Name": "flow_brand_name",
        "Sales with 3ds": "sales_with_3ds",
        "Total Sales": "total_sales",
        "Three DS Coverage": "three_ds_coverage",
    }

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = 'attachment;filename="3ds_coverage_report.csv"'
    writer = csv.writer(response)
    writer.writerow(header_to_key.keys())

    for row in coverage_data:
        writer.writerow([row[key] for key in header_to_key.values()])

    return response


def create_mid_performance_data(request: HttpRequest):
    start_date = request.GET.get(
        "start_date", (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    )
    end_date = request.GET.get("end_date", datetime.now().strftime("%Y-%m-%d"))
    flow_brand_name = request.GET.getlist("flow_brand_name[]", None)
    flow_step_number = request.GET.getlist("flow_step_number[]", None)
    card_brand = request.GET.getlist("card_brand[]", None)
    card_type = request.GET.getlist("card_type[]", None)
    clientID = request.GET.getlist("clientID[]", None)

    lst_clients, permissions = getClientView(request)
    if "SUPER-PARENT" in permissions.split("_"):
        clientID = [lst_clients[0]["clientID"]]
        if clientID[0] in ["demo_client", "demo_sales"]:
            clientID = ["recVI1C8QhLuRmtXB"]

    start_date = datetime.strptime(start_date, "%Y-%m-%d").strftime("%Y-%m-%d 00:00:00")
    end_date = datetime.strptime(end_date, "%Y-%m-%d").strftime("%Y-%m-%d 23:59:59")

    now = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S") - timedelta(days=0)
    last_24_hours = now - timedelta(days=1)
    last_48_hours = now - timedelta(days=2)

    ALL_ORDER_IMPORTS = OrderImportTemp.objects.filter(
        is_test=False,
        transaction_date__gte=start_date,
        transaction_date__lte=end_date,
    ).annotate(flow_brand_name=F("flow_brand__flow_brand_name"))
    lst_clients, permissions = getClientView(request)
    if "SUPER-PARENT" in permissions.split("_"):
        if lst_clients[0]["clientID"] in ["demo_client", "demo_sales"]:
            ALL_ORDER_IMPORTS = ALL_ORDER_IMPORTS.annotate(
                flow_brand_name=Case(
                    When(flow_brand_name="Ebook", then=Value("DemoProduct225")),
                    When(flow_brand_name="SS - 4 Bottle", then=Value("DemoProduct241")),
                    When(flow_brand_name="SS - 6 Bottle", then=Value("DemoProduct993")),
                    When(
                        flow_brand_name="SugarShield 2 Bottle",
                        then=Value("DemoProduct731"),
                    ),
                    When(flow_brand_name="KB - 6 Bottle", then=Value("DemoProduct743")),
                    When(flow_brand_name="KB - 2 Bottle", then=Value("DemoProduct367")),
                    When(flow_brand_name="KB 4 Bottle", then=Value("DemoProduct123")),
                    default=F("flow_brand_name"),
                    output_field=CharField(),
                ),
            )

    if flow_brand_name:
        ALL_ORDER_IMPORTS = ALL_ORDER_IMPORTS.filter(
            flow_brand_name__in=flow_brand_name
        )
    if flow_step_number:
        ALL_ORDER_IMPORTS = ALL_ORDER_IMPORTS.filter(
            flow_step__flow_step_number__in=flow_step_number
        )
    if card_brand:
        ALL_ORDER_IMPORTS = ALL_ORDER_IMPORTS.filter(payment_network__in=card_brand)
    if card_type:
        ALL_ORDER_IMPORTS = ALL_ORDER_IMPORTS.filter(card_type__in=card_type)
    if clientID:
        ALL_ORDER_IMPORTS = ALL_ORDER_IMPORTS.filter(
            client_login__listClientsUsed__in=clientID
        )

    all_order = (
        ALL_ORDER_IMPORTS.values(
            "client_login__mainClientUsed",
            "client_login__loginPlatform",
            "merchantId",
            "payment_network",
        )
        .annotate(
            client_name=F("client_login__mainClientUsed"),
            CRM=F("client_login__loginPlatform"),
            card_branch=F("payment_network"),
            sales=Count("id", filter=Q(order_status=1, cycle_num_lookup=0)),
            total=Count("id"),
            rate=Cast(F("sales") * 100.0 / NullIf(F("total"), 0), FloatField()),
            rate_today=Cast(
                Count(
                    "id",
                    filter=Q(
                        order_status=1, transaction_date__date=now, cycle_num_lookup=0
                    ),
                )
                * 100.0
                / NullIf(Count("id", filter=Q(transaction_date__date=now)), 0),
                FloatField(),
            ),
            rate_last_1_day=Cast(
                Count(
                    "id",
                    filter=Q(
                        order_status=1,
                        transaction_date__gte=last_24_hours,
                        cycle_num_lookup=0,
                    ),
                )
                * 100.0
                / NullIf(Count("id", filter=Q(transaction_date__gte=last_24_hours)), 0),
                FloatField(),
            ),
            rate_last_2_days=Cast(
                Count(
                    "id",
                    filter=Q(
                        order_status=1,
                        transaction_date__gte=last_48_hours,
                        cycle_num_lookup=0,
                    ),
                )
                * 100.0
                / NullIf(Count("id", filter=Q(transaction_date__gte=last_48_hours)), 0),
                FloatField(),
            ),
        )
        .annotate(
            decline=F("total") - F("sales"),
        )
        .values(
            "client_name",
            "CRM",
            "merchantId",
            "card_branch",
            "sales",
            "decline",
            "total",
            "rate",
            "rate_today",
            "rate_last_1_day",
            "rate_last_2_days",
        )
    )
    grand_total = {
        "client_name": "Grand Total",
        "CRM": "Grand Total",
        "merchantId": "Grand Total",
        "card_branch": "Grand Total",
        "sales": 0,
        "total": 0,
        "rate": 0,
        "rate_today": 0,
        "rate_last_1_day": 0,
        "rate_last_2_days": 0,
        "rate_last_25": 0,
        "rate_last_45": 0,
        "rate_last_80": 0,
    }
    for order in all_order:
        sub_all_order = ALL_ORDER_IMPORTS.filter(
            client_login__mainClientUsed=order["client_name"],
            client_login__loginPlatform=order["CRM"],
            merchantId=order["merchantId"],
            payment_network=order["card_branch"],
        )
        last_25_orders = sub_all_order[:25]
        order["last_25"] = len(
            [
                1
                for i in last_25_orders
                if i.order_status == 1
                and i.cycle_num_lookup == 0
                and i.flow_step.flow_step_number == 1
            ]
        )
        order["rate_last_25"] = (
            order["last_25"] * 100.0 / 25 if order["last_25"] > 0 else 0
        )

        last_45_orders = sub_all_order[:45]
        order["last_45"] = len(
            [
                1
                for i in last_45_orders
                if i.order_status == 1
                and i.cycle_num_lookup == 0
                and i.flow_step.flow_step_number == 1
            ]
        )
        order["rate_last_45"] = (
            order["last_45"] * 100.0 / 45 if order["last_45"] > 0 else 0
        )

        last_80_orders = sub_all_order[:80]
        order["last_80"] = len(
            [
                1
                for i in last_80_orders
                if i.order_status == 1
                and i.cycle_num_lookup == 0
                and i.flow_step.flow_step_number == 1
            ]
        )
        order["rate_last_80"] = (
            order["last_80"] * 100.0 / 80 if order["last_80"] > 0 else 0
        )

        grand_total["sales"] = grand_total.get("sales", 0) + order["sales"]
        grand_total["total"] = grand_total.get("total", 0) + order["total"]

        grand_total["rate_today"] = grand_total.get("rate_today", 0) + (
            order["rate_today"] or 0
        )
        grand_total["rate_last_1_day"] = grand_total.get("rate_last_1_day", 0) + (
            order["rate_last_1_day"] or 0
        )
        grand_total["rate_last_2_days"] = grand_total.get("rate_last_2_days", 0) + (
            order["rate_last_2_days"] or 0
        )

        grand_total["rate_last_25"] = grand_total.get("rate_last_25", 0) + (
            order["rate_last_25"] or 0
        )
        grand_total["rate_last_45"] = grand_total.get("rate_last_45", 0) + (
            order["rate_last_45"] or 0
        )
        grand_total["rate_last_80"] = grand_total.get("rate_last_80", 0) + (
            order["rate_last_80"] or 0
        )

        lst_clients, permissions = getClientView(request)
        if "SUPER-PARENT" in permissions.split("_"):
            if lst_clients[0]["clientID"] in ["demo_client", "demo_sales"]:
                order["merchantId"] = str(random.randint(1000000000, 9999999999))

    grand_total["rate"] = (
        grand_total["sales"] * 100.0 / grand_total["total"]
        if grand_total["total"] > 0
        else 0
    )

    grand_total["rate_today"] = (
        (grand_total["rate_today"] / len(all_order)) if len(all_order) > 0 else 0
    )
    grand_total["rate_last_1_day"] = (
        (grand_total["rate_last_1_day"] / len(all_order)) if len(all_order) > 0 else 0
    )
    grand_total["rate_last_2_days"] = (
        (grand_total["rate_last_2_days"] / len(all_order)) if len(all_order) > 0 else 0
    )

    grand_total["rate_last_25"] = (
        (grand_total["rate_last_25"] / len(all_order)) if len(all_order) > 0 else 0
    )
    grand_total["rate_last_45"] = (
        (grand_total["rate_last_45"] / len(all_order)) if len(all_order) > 0 else 0
    )
    grand_total["rate_last_80"] = (
        (grand_total["rate_last_80"] / len(all_order)) if len(all_order) > 0 else 0
    )

    sortByVal = request.GET.get("sortByVal", "sales")
    orderByVal = request.GET.get("orderByVal", "DESC")
    all_order = sorted(
        all_order,
        key=lambda k: (k[sortByVal] is None, k[sortByVal]),
        reverse=(orderByVal == "DESC"),
    )

    return grand_total, all_order


def get_mid_performance_each_range(base_query):
    all_order = (
        base_query.values(
            "client_login__mainClientUsed",
            "mid",
            "merchantId",
            "client_login__loginPlatform",
            "payment_network",
        )
        .annotate(
            client_name=F("client_login__mainClientUsed"),
            CRM=F("client_login__loginPlatform"),
            card_branch=F("payment_network"),
            sales=Count("id", filter=Q(order_status=1)),
            total=Count("id"),
            mid_id=F("mid")
            if F("mid") is not None
            else Subquery(
                MerchantsInformation.objects.filter(merchantId=F("merchantId")).values(
                    "mid"
                )[:1]
            ),
        )
        .values("client_name", "mid", "mid_id", "CRM", "card_branch", "sales", "total")
    )

    return all_order


def get_base_query_for_mid_performance(request: HttpRequest) -> QuerySet[OrderImport]:
    start_date = request.GET.get(
        "start_date", (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    )
    end_date = request.GET.get("end_date", datetime.now().strftime("%Y-%m-%d"))

    flow_brand_name = request.GET.getlist("flow_brand_name[]", None)
    flow_step_number = request.GET.getlist("flow_step_number[]", None)
    card_brand = request.GET.getlist("card_brand[]", None)
    card_type = request.GET.getlist("card_type[]", None)

    client_id = request.GET.getlist("clientID[]", None)
    lst_clients, permissions = getClientView(request)
    if "SUPER-PARENT" in permissions.split("_"):
        client_id = [lst_clients[0]["clientID"]]
        if client_id[0] in ["demo_client", "demo_sales"]:
            client_id = ["recVI1C8QhLuRmtXB"]

    # Convert dates to datetime objects with time components

    client_login = (
        ClientLoginInformation.objects.filter(listClientsUsed__in=client_id)
        .values_list("id", flat=True)
        .distinct()
    )

    # Convert dates to datetime objects with time components
    start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
    end_datetime = (
        datetime.strptime(end_date, "%Y-%m-%d")
        + timedelta(days=1)
        - timedelta(seconds=1)
    )
    base_filter = Q(
        is_test=0,
        transaction_date__gte=start_datetime,
        transaction_date__lte=end_datetime,
        client_login__in=client_login,
        merchantId__isnull=False,
    )
    if flow_brand_name:
        product_ids = (
            OfferProductDetail.objects.filter(
                offer_flow_step__flow_id__flow_brand_name__in=flow_brand_name
            )
            .values_list("id", flat=True)
            .distinct()
        )
        base_filter &= Q(offer_product_id__in=product_ids)
    if flow_step_number:
        product_ids = (
            OfferProductDetail.objects.filter(
                offer_flow_step__flow_step_number__in=flow_step_number
            )
            .values_list("id", flat=True)
            .distinct()
        )
        base_filter &= Q(offer_product_id__in=product_ids)
    if card_brand:
        base_filter &= Q(payment_network__in=card_brand)
    if card_type:
        card_info_ids = (
            CardBinLookup.objects.filter(
                card_brand__in=card_brand,
            )
            .values_list("card_bin", flat=True)
            .distinct()
        )
        base_filter &= Q(card_infor_id__in=card_info_ids)

    base_query = OrderImport.objects.filter(
        base_filter,
    ).select_related("client_login")

    return base_query


def get_mid_performance_report(request: HttpRequest):
    end_date = request.GET.get("end_date", datetime.now().strftime("%Y-%m-%d"))
    end_datetime = (
        datetime.strptime(end_date, "%Y-%m-%d")
        + timedelta(days=1)
        - timedelta(seconds=1)
    )

    # First get the base queryset
    base_query = get_base_query_for_mid_performance(request)

    current_datetime = datetime.strptime(end_date, "%Y-%m-%d")
    current_day_data = base_query.filter(
        transaction_date__gte=current_datetime, transaction_date__lte=end_datetime
    )

    last_day_start_datetime = datetime.strptime(end_date, "%Y-%m-%d") - timedelta(
        days=1
    )
    last_day_data = base_query.filter(
        transaction_date__gte=last_day_start_datetime,
        transaction_date__lte=end_datetime,
    )

    last_two_day_start_datetime = datetime.strptime(end_date, "%Y-%m-%d") - timedelta(
        days=2
    )
    last_two_days_data = base_query.filter(
        transaction_date__gte=last_two_day_start_datetime,
        transaction_date__lte=end_datetime,
    )

    last_25_transactions = base_query.order_by("-transaction_date")[:25]
    last_45_transactions = base_query.order_by("-transaction_date")[:45]
    last_80_transactions = base_query.order_by("-transaction_date")[:80]

    # Get the aggregated data
    all_report = get_mid_performance_each_range(base_query)
    current_day_report = get_mid_performance_each_range(current_day_data)
    last_day_report = get_mid_performance_each_range(last_day_data)
    last_two_days_report = get_mid_performance_each_range(last_two_days_data)
    last_25_report = get_mid_performance_each_range(last_25_transactions)
    last_45_report = get_mid_performance_each_range(last_45_transactions)
    last_80_report = get_mid_performance_each_range(last_80_transactions)

    return {
        "all": list(all_report),
        "current_day": list(current_day_report),
        "last_day": list(last_day_report),
        "last_two_days": list(last_two_days_report),
        "last_25": list(last_25_report),
        "last_45": list(last_45_report),
        "last_80": list(last_80_report),
    }


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS + TABLE_PARAMS,
    responses={
        HTTP_200_OK: "OK",
        HTTP_400_BAD_REQUEST: "Bad Request",
        HTTP_405_METHOD_NOT_ALLOWED: "Method Not Allowed",
    },
    auth=None,
    operation_id="Mid Performance Analysis",
    tags=["Daily Performance Summary Reports"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def mid_performance_analysis(request: HttpRequest) -> JsonResponse:
    # try:
    report = get_mid_performance_report(request)
    table_rows, total = map_full_mid_performance_report(report)

    sort_by = request.GET.get("sortByVal", "merchantId")
    order_by = request.GET.get("orderByVal", "DESC")
    page = int(request.GET.get("page", 1))
    page_size = int(request.GET.get("limit", 20))

    table_rows = sorted(
        table_rows,
        key=lambda k: (k[sort_by] is None, k[sort_by]),
        reverse=(order_by == "DESC"),
    )

    paginator = Paginator(table_rows, page_size)
    try:
        page_obj = paginator.page(page)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    table_rows = list(page_obj)
    pagination = {
        "page": page,
        "limit": page_size,
        "total_page": paginator.num_pages,
        "total_item": paginator.count,
    }

    return JsonResponse(
        {
            "message": "get Mid Performance Analysis successfully.",
            "pagination": pagination,
            "total": total,
            "data": table_rows,
        },
        status=HTTP_200_OK,
    )


# except Exception as e:
#     return JsonResponse({"message": "Failed to get Mid Performance Analysis", "error": str(e)}, status=HTTP_400_BAD_REQUEST)


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS + TABLE_PARAMS,
    responses={
        HTTP_200_OK: "OK",
        HTTP_400_BAD_REQUEST: "Bad Request",
        HTTP_405_METHOD_NOT_ALLOWED: "Method Not Allowed",
    },
    auth=None,
    operation_id="Mid Performance Analysis",
    tags=["Daily Performance Summary Reports"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def download_mid_performance_analysis(request: HttpRequest) -> JsonResponse:
    # try:
    grand_total, all_order = create_mid_performance_data(request)
    all_order = list(all_order)

    if not all_order:
        return JsonResponse({"message": "No data found"}, status=HTTP_200_OK)

    header_to_key = {
        "Client": "client_name",
        "CRM": "CRM",
        "Merchant ID": "merchantId",
        "Card Branch": "card_branch",
        "Sales": "sales",
        "Decline": "decline",
        "Total": "total",
        "Rate": "rate",
        "Rate Today": "rate_today",
        "Rate Last 1 Day": "rate_last_1_day",
        "Rate Last 2 Days": "rate_last_2_days",
        "Rate Last 25": "rate_last_25",
        "Rate Last 45": "rate_last_45",
        "Rate Last 80": "rate_last_80",
    }

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = (
        'attachment;filename="mid_performance_analysis.csv"'
    )
    writer = csv.writer(response)
    writer.writerow(header_to_key.keys())

    for row in all_order:
        writer.writerow([row[key] for key in header_to_key.values()])

    total_row = []
    for key in header_to_key.values():
        if key in ["client_name", "CRM", "merchantId", "card_branch"]:
            total_row.append("Grand Total")
        else:
            total_row.append(grand_total.get(key))

    writer.writerow(total_row)

    return response


# Helper to build a key for each MID/CRM/Card Brand
def build_key(row):
    return f"{row.get('mid', row.get('merchantId'))}|{row['CRM']}|{row['card_branch']}"


def map_full_mid_performance_report(report):
    # report: output of get_mid_performance_report(request)
    # returns: list of dicts, each dict is a row for the table

    # Build a dict for each range
    ranges = [
        "all",
        "current_day",
        "last_day",
        "last_two_days",
        "last_25",
        "last_45",
        "last_80",
    ]
    data_by_key = {}

    base_sales = 0
    sales_last_day = 0
    sales_last_2_days = 0
    sales_current_day = 0
    sales_last_25 = 0
    sales_last_45 = 0
    sales_last_80 = 0

    total_sales = 0
    total_last_day = 0
    total_last_2_days = 0
    total_current_day = 0
    total_last_25 = 0
    total_last_45 = 0
    total_last_80 = 0
    for range_name in ranges:
        for row in report[range_name]:
            key = build_key(row)
            if key not in data_by_key:
                data_by_key[key] = {
                    "client_name": row["client_name"],
                    "merchantId": row.get("mid", row.get("mid_id")),
                    "CRM": row["CRM"],
                    "card_branch": row["card_branch"],
                    "sales": 0,
                    "total": 0,
                    "rate": 0,
                    "rate_today": 0,
                    "rate_last_1_day": 0,
                    "rate_last_2_days": 0,
                    "rate_last_25": 0,
                    "rate_last_45": 0,
                    "rate_last_80": 0,
                }
            # Fill in the values for each range
            sales = row.get("sales", 0)
            total = row.get("total", 0)
            approved_rate = sales * 100.0 / total if total else 0
            if range_name == "all":
                data_by_key[key]["sales"] = row["sales"]
                data_by_key[key]["total"] = row["total"]
                data_by_key[key]["rate"] = approved_rate
                base_sales += sales
                total_sales += total
            elif range_name == "current_day":
                data_by_key[key]["rate_today"] = approved_rate
                sales_current_day += sales
                total_current_day += total
            elif range_name == "last_day":
                data_by_key[key]["rate_last_1_day"] = approved_rate
                sales_last_day += sales
                total_last_day += total
            elif range_name == "last_two_days":
                data_by_key[key]["rate_last_2_days"] = approved_rate
                sales_last_2_days += sales
                total_last_2_days += total
            elif range_name == "last_25":
                data_by_key[key]["rate_last_25"] = approved_rate
                sales_last_25 += sales
                total_last_25 += total
            elif range_name == "last_45":
                data_by_key[key]["rate_last_45"] = approved_rate
                sales_last_45 += sales
                total_last_45 += total
            elif range_name == "last_80":
                data_by_key[key]["rate_last_80"] = approved_rate
                sales_last_80 += sales
                total_last_80 += total

    # Convert to list
    rows = list(data_by_key.values())

    # Calculate Grand Total
    grand_total = {
        "client_name": "Grand Total",
        "CRM": "Grand Total",
        "merchantId": "Grand Total",
        "card_branch": "Grand Total",
        "sales": base_sales,
        "total": total_sales,
        "rate": base_sales * 100.0 / total_sales if total_sales else 0,
        "rate_today": sales_current_day * 100.0 / total_current_day
        if total_current_day
        else 0,
        "rate_last_1_day": sales_last_day * 100.0 / total_last_day
        if total_last_day
        else 0,
        "rate_last_2_days": sales_last_2_days * 100.0 / total_last_2_days
        if total_last_2_days
        else 0,
        "rate_last_25": sales_last_25 * 100.0 / total_last_25 if total_last_25 else 0,
        "rate_last_45": sales_last_45 * 100.0 / total_last_45 if total_last_45 else 0,
        "rate_last_80": sales_last_80 * 100.0 / total_last_80 if total_last_80 else 0,
    }

    return rows, grand_total
