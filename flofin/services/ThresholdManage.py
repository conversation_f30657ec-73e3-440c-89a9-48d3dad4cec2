import os
from datetime import datetime, timedelta
from django.utils import timezone

from django.db.models import F, IntegerField, Sum, Value
from django.db.models.functions import Coalesce
from django.http import HttpRequest, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from dotenv import find_dotenv, load_dotenv
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework.decorators import api_view
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from data.models import ClientsInformation
from flofin.models import AlertThreshold, HomeCLVReport
from flofin.services import filterQueryAfter
from flofin.services.utils import getClientView
from flofin.source.email import sendEmailAlertThreshold
from user.models import Users
from user.utils.utils import require_login
import operator

dotenv_file = find_dotenv()
load_dotenv(dotenv_file, override=True)


FILTER_PARAMS = [
    OpenApiParameter(name="flow_step_number[]", description="Flow Step - list (ex: [1,2,3])", required=False, type=str),
    OpenApiParameter(name="flow_brand_name[]", description="Flow Title - list (ex: ['abc','123'])", required=False, type=str),
    OpenApiParameter(name="card_brand[]", description="Card Brand - list (ex: ['visa','mastercard'])", required=False, type=str),
    OpenApiParameter(name="card_type[]", description="Card Type - list (ex: ['credit','debit'])", required=False, type=str),
    OpenApiParameter(name="clientID[]", description="Client ID - list (ex: [1,2,3])", required=False, type=str),
    OpenApiParameter(name="start_date", description="Start Date - format: YYYY-MM-DD", required=False, type=str),
    OpenApiParameter(name="end_date", description="End Date - format: YYYY-MM-DD", required=False, type=str),
]


@extend_schema(
    parameters=[
        OpenApiParameter(name="threshold_id", description="id", required=False, type=str),
        OpenApiParameter(name="name", description="Name", required=True, type=str),
        OpenApiParameter(
            name="dimension",
            description="Dimension",
            required=True,
            type=str,
            enum=["client", "network", "affiliate", "flow_brand_name", "card_brand", "card_type", "merchantId"],
        ),
        OpenApiParameter(
            name="metric",
            description="Metric",
            required=True,
            type=str,
            enum=["chargeback_rate", "refund_rate", "void_rate", "transaction_rate", "foreign_card_rate", "initial_approval_rate"],
        ),
        OpenApiParameter(name="value", description="Value", required=True, type=float),
        OpenApiParameter(name="clientID", description="Client ID", required=True, type=str),
        OpenApiParameter(name="dimension_value", description="Dimension Name", required=True, type=str),
        OpenApiParameter(name="is_active", description="Is Active", required=True, type=bool),
        OpenApiParameter(name="trigger_frequency", description="Trigger Frequency", required=True, type=str),
        OpenApiParameter(name="lst_email", description="List Email", required=True, type=str),
        OpenApiParameter(name="condition", description="Condition", required=False, type=str, enum=["<", ">", "<=", ">="]),
    ],
    responses={
        HTTP_200_OK: "Create AlertThreshold API successfully",
    },
    auth=None,
    operation_id="Create AlertThreshold API",
    tags=["AlertThreshold"],
)
@csrf_exempt
@api_view(["POST"])
@require_login
def createAlertThreshold(request: HttpRequest) -> JsonResponse:
    # try:
    userInfo = request.user
    user = Users.objects.filter(id=userInfo["id"]).first()

    data = request.data
    client_id = data.get("clientID", None)
    if not client_id:
        return JsonResponse({"message": "clientID is required"}, status=HTTP_400_BAD_REQUEST)
    client = ClientsInformation.objects.filter(clientID=client_id).first()

    trigger_frequency = data.get("trigger_frequency", None)
    if trigger_frequency not in ["hourly", "daily", "weekly", "monthly", "quarterly", "yearly"]:
        return JsonResponse({"message": "trigger_frequency is invalid please choose hourly, daily, weekly, monthly, quarterly, yearly"}, status=HTTP_400_BAD_REQUEST)

    metric = data.get("metric", None)
    if metric not in ["chargeback_rate", "refund_rate", "void_rate", "transaction_rate", "foreign_card_rate", "initial_approval_rate"]:
        return JsonResponse(
            {"message": ("metric is invalid please choose chargeback_rate, refund_rate, void_rate, foreign_card_rate, initial_approval_rate")},
            status=HTTP_400_BAD_REQUEST,
        )

    is_active = data.get("is_active", 1)
    is_active = isinstance(is_active, int) and is_active or int(is_active)
    if is_active not in [1, 0]:
        return JsonResponse({"message": "is_active is invalid please choose 1 or 0"}, status=HTTP_400_BAD_REQUEST)

    name = data.get("name", None)
    if AlertThreshold.objects.filter(client=client, name=name).exists() and not data.get("threshold_id", None):
        return JsonResponse({"message": "Name is already exists"}, status=HTTP_400_BAD_REQUEST)

    dimension = data.get("dimension", None)
    if dimension not in ["client", "network", "affiliate", "flow_brand_name", "card_brand", "card_type", "merchantId"]:
        return JsonResponse(
            {"message": "dimension is invalid please choose client, network, affiliate, flow_brand_name, card_brand, card_type, merchantId"},
            status=HTTP_400_BAD_REQUEST,
        )

    value = data.get("value", None)
    value = isinstance(value, float) and value or value
    if value is None:
        return JsonResponse({"message": "Value is required"}, status=HTTP_400_BAD_REQUEST)
    dimension_value = data.get("dimension_value", None)
    if dimension_value is None:
        return JsonResponse({"message": "Dimension Value is required"}, status=HTTP_400_BAD_REQUEST)

    lst_email = data.get("lst_email", None)
    if isinstance(lst_email, str):
        lst_email = lst_email.strip("[]").replace(" ", "").replace("'", "").replace('"', "").split(",")
        lst_email = [email.strip() for email in lst_email]

    condition = data.get("condition", None)
    if condition not in ["<", ">", "<=", ">="]:
        return JsonResponse({"message": "condition is invalid please choose <, >, <=, >="}, status=HTTP_400_BAD_REQUEST)

    threshold_id = data.get("threshold_id", None)
    if threshold_id:
        alert_threshold = AlertThreshold.objects.filter(id=threshold_id).first()
        if not alert_threshold:
            return JsonResponse({"message": "threshold_id is not exists"}, status=HTTP_400_BAD_REQUEST)
        alert_threshold.name = name
        alert_threshold.dimension = dimension
        alert_threshold.metric = metric
        alert_threshold.value = value
        alert_threshold.dimension_value = dimension_value
        alert_threshold.is_active = is_active
        alert_threshold.trigger_frequency = trigger_frequency
        alert_threshold.condition = condition
        alert_threshold.lst_email = lst_email
        alert_threshold.user = user
        alert_threshold.updated_at = datetime.now()
        alert_threshold.client = client
        alert_threshold.save()
    else:
        alert_threshold = AlertThreshold.objects.create(
            client=client,
            name=name,
            dimension=dimension,
            metric=metric,
            value=value,
            dimension_value=dimension_value,
            is_active=is_active,
            trigger_frequency=trigger_frequency,
            condition=condition,
            user=user,
            lst_email=lst_email,
        )

    if lst_email:
        sendEmailAlertThreshold(
            {
                "client": client.name,
                "alert_name": name,
                "alert_id": alert_threshold.id,
                "dimension": dimension,
                "dimension_value": dimension_value,
                "metric": metric,
                "condition": condition,
                "current_value": getCurrentData(dimension, dimension_value, metric, trigger_frequency, client_id),
                "threshold": value,
                "frequency": trigger_frequency,
            },
            lst_email,
            subject=f"[FLOFIN] Alert Threshold for {name} has been created!",
        )
    return JsonResponse({"message": "Create AlertThreshold API successfully"}, status=HTTP_200_OK)


# except Exception as e:
#     return JsonResponse({"message": "Get data unsuccessfully", "exception": str(e)}, status=HTTP_400_BAD_REQUEST)


def getCurrentData(dimension, dimension_value, metric, trigger_frequency, clientID=None):
    REPORTS = (
        HomeCLVReport.objects.filter(clientID=clientID)
        .exclude(flow_brand__status="Deleted")
        .annotate(
            flow_brand_name=F("flow_brand__flow_brand_name"),
            flow_step_number=F("flow_step__flow_step_number"),
        )
    )

    if dimension == "flow_brand_name":
        REPORTS = REPORTS.filter(flow_brand_name=dimension_value)
    elif dimension == "card_brand":
        REPORTS = REPORTS.filter(card_brand=dimension_value)
    elif dimension == "card_type":
        REPORTS = REPORTS.filter(card_type=dimension_value)
    elif dimension == "merchantId":
        REPORTS = REPORTS.filter(merchantId=dimension_value)
    elif dimension == "affiliate":
        REPORTS = REPORTS.filter(pub=dimension_value)
    elif dimension == "network":
        REPORTS = REPORTS.filter(network=dimension_value)

    # if trigger_frequency == 1:
    #     start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    #     end_date = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)
    # elif trigger_frequency == 7:
    #     today = datetime.now()
    #     start_date = today - timedelta(days=today.weekday() + 7)
    #     end_date = start_date + timedelta(days=6, hours=23, minutes=59, seconds=59, microseconds=999999)
    # elif trigger_frequency == 30:
    #     today = datetime.now()
    #     start_date = today.replace(day=1) - timedelta(days=today.day)
    #     end_date = today.replace(day=1) - timedelta(seconds=1)
    # elif trigger_frequency == 90:
    #     today = datetime.now()
    #     month = (today.month - 1) // 3 * 3 + 1
    #     start_date = today.replace(month=month, day=1) - timedelta(days=today.day)
    #     end_date = today.replace(month=month + 3, day=1) - timedelta(seconds=1)
    # elif trigger_frequency == 365:
    #     today = datetime.now()
    #     start_date = today.replace(year=today.year - 1, month=1, day=1)
    #     end_date = today.replace(month=1, day=1) - timedelta(seconds=1)
    # else:
    #     start_date = datetime.now() - timedelta(days=trigger_frequency)
    #     end_date = datetime.now()

    # REPORTS = REPORTS.filter(transaction_date__gte=start_date, transaction_date__lte=end_date)

    REPORTS = REPORTS.aggregate(
        t_transactions=Coalesce(Sum("transactions"), Value(0), output_field=IntegerField()),
        t_chargebacks=Coalesce(Sum("chargebacks"), Value(0), output_field=IntegerField()),
        t_refunds=Coalesce(Sum("refunds"), Value(0), output_field=IntegerField()),
        t_voids=Coalesce(Sum("voids"), Value(0), output_field=IntegerField()),
        t_foreign_card=Coalesce(Sum("total_foreign_card"), Value(0), output_field=IntegerField()),
        t_unique=Coalesce(Sum("total_unique_sale"), Value(0), output_field=IntegerField()),
    )

    if metric == "chargeback_rate":
        current_value = REPORTS["t_chargebacks"] / REPORTS["t_transactions"] * 100 if REPORTS["t_transactions"] != 0 else 0
    elif metric == "refund_rate":
        current_value = REPORTS["t_refunds"] / REPORTS["t_transactions"] * 100 if REPORTS["t_transactions"] != 0 else 0
    elif metric == "void_rate":
        current_value = REPORTS["t_voids"] / REPORTS["t_transactions"] * 100 if REPORTS["t_transactions"] != 0 else 0
    elif metric == "foreign_card_rate":
        current_value = REPORTS["t_foreign_card"] / REPORTS["t_transactions"] * 100 if REPORTS["t_transactions"] != 0 else 0
    elif metric == "initial_approval_rate":
        current_value = REPORTS["t_unique"] / REPORTS["t_transactions"] * 100 if REPORTS["t_transactions"] != 0 else 0
    else:
        current_value = None
    current_value = current_value or 0
    return round(current_value, 2)


@extend_schema(
    responses={
        HTTP_200_OK: "List AlertThresholds API successfully",
    },
    auth=None,
    operation_id="List AlertThresholds API",
    tags=["AlertThreshold"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def listAlertThresholds(request):
    # try:
    ALL_THRESHOLD = AlertThreshold.objects.all()
    if os.getenv("ENVIRONMENT") == "SANDBOX":
        ALL_THRESHOLD = ALL_THRESHOLD.filter(client__clientID="demo_client")
    else:
        ALL_THRESHOLD = ALL_THRESHOLD.exclude(client__clientID="demo_client")

    lst_clients, permissions = getClientView(request)
    listClientId = request.GET.getlist("clientID[]", [])

    if "SUPER-PARENT" in permissions.split("_") or len(listClientId) > 0:
        clientIDs = [client["clientID"] for client in lst_clients]
        ALL_THRESHOLD = ALL_THRESHOLD.filter(client__clientID__in=clientIDs)
        
    alert_thresholds = ALL_THRESHOLD.annotate(client_name=F("client__name"), clientID=F("client__clientID"), user_updated=F("user__user_name"),).values(
        "id",
        "name",
        "client_name",
        "clientID",
        "dimension",
        "metric",
        "value",
        "dimension_value",
        "is_active",
        "trigger_frequency",
        "condition",
        "lst_email",
        "updated_at",
        "user_updated",
    )

    alert_thresholds = list(alert_thresholds)
    for alert_threshold in alert_thresholds:
        current_value = getCurrentData(
            alert_threshold["dimension"], alert_threshold["dimension_value"], alert_threshold["metric"], alert_threshold["trigger_frequency"], alert_threshold["clientID"]
        )
        alert_threshold["current_value"] = current_value
        alert_threshold["status"] = "Active" if alert_threshold["is_active"] else "In active"

        if alert_threshold["current_value"] is not None:
            if alert_threshold["condition"] == "<":
                alert_threshold["is_flagged"] = alert_threshold["current_value"] < alert_threshold["value"]
                alert_threshold["condition"] = "< (Less than)"
            elif alert_threshold["condition"] == ">":
                alert_threshold["is_flagged"] = alert_threshold["current_value"] > alert_threshold["value"]
                alert_threshold["condition"] = "> (Greater than)"
            elif alert_threshold["condition"] == "<=":
                alert_threshold["is_flagged"] = alert_threshold["current_value"] <= alert_threshold["value"]
                alert_threshold["condition"] = "<= (Less than or equal)"
            elif alert_threshold["condition"] == ">=":
                alert_threshold["is_flagged"] = alert_threshold["current_value"] >= alert_threshold["value"]
                alert_threshold["condition"] = ">= (Greater than or equal)"
        else:
            alert_threshold["is_flagged"] = False

        alert_threshold["updated_at"] = alert_threshold["updated_at"].strftime("%Y-%m-%d %H:%M:%S")

    alert_thresholds, paginator = filterQueryAfter(request, alert_thresholds)

    return JsonResponse(
        {
            "message": "Get table sale flows successfully!",
            "pagination": paginator,
            "data": list(alert_thresholds),
        },
        status=HTTP_200_OK,
    )


# except Exception as e:
#     return JsonResponse({"message": "Get data unsuccessfully", "exception": str(e)}, status=HTTP_400_BAD_REQUEST)


@extend_schema(
    parameters=[
        OpenApiParameter(name="threshold_id", description="id", required=True, type=str),
    ],
    responses={
        HTTP_200_OK: "Delete AlertThreshold API successfully",
    },
    auth=None,
    operation_id="Delete AlertThreshold API",
    tags=["AlertThreshold"],
)
@csrf_exempt
@api_view(["DELETE"])
@require_login
def deleteAlertThreshold(request):
    try:
        threshold_id = request.GET.get("threshold_id", None)
        if threshold_id:
            AlertThreshold.objects.filter(id=threshold_id).delete()
            return JsonResponse({"message": "Delete AlertThreshold API successfully"}, status=HTTP_200_OK)
        else:
            return JsonResponse({"message": "threshold_id is required"}, status=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return JsonResponse({"message": "Get data unsuccessfully", "exception": str(e)}, status=HTTP_400_BAD_REQUEST)


@extend_schema(
    parameters=[
        OpenApiParameter(name="threshold_id", description="id", required=True, type=str),
    ],
    responses={
        HTTP_200_OK: "GET AlertThreshold API successfully",
    },
    auth=None,
    operation_id="GET AlertThreshold API",
    tags=["AlertThreshold"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getAlertThreshold(request):
    try:
        threshold_id = request.GET.get("threshold_id", None)
        if threshold_id:
            if not AlertThreshold.objects.filter(id=threshold_id).exists():
                return JsonResponse({"message": "threshold_id is not exists"}, status=HTTP_400_BAD_REQUEST)
            at = AlertThreshold.objects.filter(id=threshold_id).annotate(
                clientID=F("client__clientID"),
                client_name=F("client__name"),
            )
            return JsonResponse(
                {
                    "message": "Get AlertThreshold API successfully",
                    "data": at.values(
                        "id",
                        "name",
                        "client_name",
                        "clientID",
                        "dimension",
                        "metric",
                        "value",
                        "dimension_value",
                        "is_active",
                        "trigger_frequency",
                        "condition",
                        "lst_email",
                        "updated_at",
                    ).first(),
                },
                status=HTTP_200_OK,
            )
        else:
            return JsonResponse({"message": "threshold_id is required"}, status=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return JsonResponse({"message": "Get data unsuccessfully", "exception": str(e)}, status=HTTP_400_BAD_REQUEST)


@extend_schema(
    parameters=[],
    responses={
        HTTP_200_OK: "GET AlertThreshold Overview API successfully",
    },
    auth=None,
    operation_id="GET AlertThreshold Overview API",
    tags=["AlertThreshold"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getAlertThresholdOverview(request):
    try:
        ALL_THRESHOLD = AlertThreshold.objects.all()
        if os.getenv("ENVIRONMENT") == "SANDBOX":
            ALL_THRESHOLD = ALL_THRESHOLD.filter(client__clientID="demo_client")
        else:
            ALL_THRESHOLD = ALL_THRESHOLD.exclude(client__clientID="demo_client")

        lst_clients, permissions = getClientView(request)
        listClientId = request.GET.getlist("clientID[]", [])

        if "SUPER-PARENT" in permissions.split("_") or len(listClientId) > 0:
            clientIDs = [client["clientID"] for client in lst_clients]
            ALL_THRESHOLD = ALL_THRESHOLD.filter(client__clientID__in=clientIDs)

        at = ALL_THRESHOLD.annotate(
            client_name=F("client__name"),
            clientID=F("client__clientID"),
        )
        at1 = at.values("name", "value", "dimension", "metric", "dimension_value", "trigger_frequency", "condition", "clientID")
        for at2 in at1:
            current_value = getCurrentData(at2["dimension"], at2["dimension_value"], at2["metric"], at2["trigger_frequency"], at2["clientID"])
            at2["current_value"] = current_value
            if at2["condition"] == "<":
                if at2["current_value"] < at2["value"]:
                    at2["is_flagged"] = True
            elif at2["condition"] == ">":
                if at2["current_value"] > at2["value"]:
                    at2["is_flagged"] = True
            elif at2["condition"] == "<=":
                if at2["current_value"] <= at2["value"]:
                    at2["is_flagged"] = True
            elif at2["condition"] == ">=":
                if at2["current_value"] >= at2["value"]:
                    at2["is_flagged"] = True

        return JsonResponse(
            {
                "message": "AlertThreshold data retrieved successfully.",
                "data": {
                    "total_threshold": {
                        "name": "Total Threshold",
                        "total": at.count(),
                        "data": list(at1),
                    },
                    "email_today": {
                        "name": "Email Today",
                        "total": 0,
                        "data": [],
                    },
                },
            },
            status=HTTP_200_OK,
        )
    except Exception as e:
        return JsonResponse({"message": "Get data unsuccessfully", "exception": str(e)}, status=HTTP_400_BAD_REQUEST)


@extend_schema(
    parameters=[
        OpenApiParameter(name="clientID", description="Client ID", required=True, type=str),
        OpenApiParameter(
            name="dimension",
            description="Dimension",
            required=True,
            type=str,
            enum=["client", "network", "affiliate", "flow_brand_name", "card_brand", "card_type", "merchantId"],
        ),
        OpenApiParameter(
            name="metric",
            description="Metric",
            required=True,
            type=str,
            enum=["chargeback_rate", "refund_rate", "void_rate", "transaction_rate", "foreign_card_rate", "initial_approval_rate"],
        ),
        OpenApiParameter(name="trigger_frequency", description="Trigger Frequency", required=True, type=int),
        OpenApiParameter(name="dimension_value", description="Dimension Value", required=True, type=str),
    ],
    responses={
        HTTP_200_OK: "GET Current-Value API successfully",
    },
    auth=None,
    operation_id="GET Current-Value API",
    tags=["AlertThreshold"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getCurrentValue(request):
    # try:
    clientID = request.GET.get("clientID", None)
    if not clientID:
        return JsonResponse({"message": "clientID is required"}, status=HTTP_400_BAD_REQUEST)
    dimension = request.GET.get("dimension", None)
    if dimension not in ["client", "network", "affiliate", "flow_brand_name", "card_brand", "card_type", "merchantId"]:
        return JsonResponse(
            {"message": "dimension is invalid please choose client, network, affiliate, flow_brand_name, card_brand, card_type, merchantId"},
            status=HTTP_400_BAD_REQUEST,
        )
    metric = request.GET.get("metric", None)
    if metric not in ["chargeback_rate", "refund_rate", "void_rate", "transaction_rate", "foreign_card_rate", "initial_approval_rate"]:
        return JsonResponse(
            {"message": "metric is invalid please choose chargeback_rate, refund_rate, void_rate, transaction_rate, foreign_card_rate, initial_approval_rate"},
            status=HTTP_400_BAD_REQUEST,
        )
    trigger_frequency = request.GET.get("trigger_frequency", None)
    if not trigger_frequency:
        return JsonResponse({"message": "trigger_frequency is required, please choose hourly, daily, weekly, monthly, quarterly, yearly"}, status=HTTP_400_BAD_REQUEST)
    dimension_value = request.GET.get("dimension_value", None)
    if not dimension_value:
        return JsonResponse({"message": "dimension_value is required"}, status=HTTP_400_BAD_REQUEST)

    current_value = getCurrentData(dimension, dimension_value, metric, trigger_frequency, clientID)
    return JsonResponse({"message": "Get Current-Value API successfully", "data": current_value}, status=HTTP_200_OK)


# except Exception as e:
#     return JsonResponse({"message": "Get data unsuccessfully", "exception": str(e)}, status=HTTP_400_BAD_REQUEST)

def triggerAlertThresholdJob():

    thresholds = AlertThreshold.objects.filter(is_active=1).all()
    now = timezone.now()
    OPS = {
        ">": operator.gt,
        ">=": operator.ge,
        "<": operator.lt,
        "<=": operator.le,
        "==": operator.eq,
        "!=": operator.ne,
    }
    
    TRIGGER_FREQUENCY = {
        "hourly": 1/24,
        "daily": 1,
        "weekly": 7,
        "monthly": 30,
        "quarterly": 90,
        "yearly": 365,
    }
    
    
    for threshold in thresholds:
        try:
            if threshold.next_trigger_at and threshold.next_trigger_at > now:
                print(f"Skip threshold {threshold.id} of client {threshold.client.clientID} because next trigger at {threshold.next_trigger_at} is later than now {now}")
                continue

            current_value = getCurrentData(
                threshold.dimension,
                threshold.dimension_value,
                threshold.metric,
                threshold.trigger_frequency,
                threshold.client.clientID
            )
            
            print(f"Client {threshold.client.clientID}: Checking threshold {threshold.id} - Current value: {current_value}, Threshold value: {threshold.value}, Condition: {threshold.condition}")

            threshold.last_triggered_at = now
            threshold.error_message = None
            if threshold.next_trigger_at is None or threshold.next_trigger_at < now:
                threshold.next_trigger_at = now
            threshold.next_trigger_at +=  timedelta(days=TRIGGER_FREQUENCY.get(threshold.trigger_frequency, 1))
            threshold.save()
            
            compare = OPS.get(threshold.condition)
            try:
                left = float(current_value)
                right = float(threshold.value)
            except (ValueError, TypeError):
                left = str(current_value)
                right = str(threshold.value)
            is_triggered = False
            
            if compare:
                is_triggered = compare(left, right)
            else:
                raise ValueError(f"Unknown condition operator: {threshold.condition}")
            
            if not is_triggered:
                continue     
            
            sendEmailAlertThreshold(
                {
                    "client": threshold.client.name,
                    "alert_name": threshold.name,
                    "alert_id": threshold.id,
                    "dimension": threshold.dimension,
                    "dimension_value": threshold.dimension_value,
                    "metric": threshold.metric,
                    "condition": threshold.condition,
                    "current_value": current_value,
                    "threshold": threshold.value,
                    "frequency": threshold.trigger_frequency,
                },
                threshold.lst_email,
                subject=f"[FLOFIN] Alert Threshold for {threshold.name} has been triggered!",
            )
            
        except Exception as e:
            threshold.error_message = str(e)
            threshold.save()
            print(f"Error processing threshold {threshold.id}: {str(e)}")
            
