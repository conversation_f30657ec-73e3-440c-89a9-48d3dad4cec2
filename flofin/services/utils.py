from datetime import date, <PERSON><PERSON><PERSON>
from types import SimpleNamespace

from django.db.models import F, Q, Sum, Value, IntegerField
from django.db.models.functions import Coalesce
from django.http import HttpRequest, JsonResponse
from django.utils.dateparse import parse_date
from django.views.decorators.csrf import csrf_exempt
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework.decorators import api_view

from data.models import ClientsInformation
from flofin.models import OrderImport, HomeCLVReport
from flofin.services import EClientStatus
from user import models as user_models
from user.utils.utils import require_login


def getClientView(request: HttpRequest) -> list:
    userInfo = request.user
    listClientId = request.GET.getlist("clientID[]", [])
    
    temp = SimpleNamespace(**userInfo)
    permissions = str(temp.permission)
    if "SUPER-PARENT" in permissions.split("_"):
        clientIDs = user_models.ClientParent.objects.filter(
            user__id=temp.id
        ).values_list("client__id", flat=True)
        clients = ClientsInformation.objects.filter(id__in=list(clientIDs)).order_by(
            "name"
        )
    else:
        clients = ClientsInformation.objects.filter(
            status__in=[
                EClientStatus.Active.value,
                EClientStatus.Onboarding.value,
                EClientStatus.Pre_ONB.value,
                EClientStatus.Integration,
            ]
        ).order_by("name")
    
    if len(listClientId) > 0:
        clients = clients.filter(clientID__in=listClientId)
    
    clients = list(clients.values("name", "clientID", "status"))
    return clients, permissions


@extend_schema(
    parameters=[
        OpenApiParameter(
            name="show_by_date",
            description="Show by date of month",
            required=True,
            type=str,
        ),
        OpenApiParameter(
            name="show_by_network",
            description="Show By Network",
            required=True,
            type=str,
        ),
        OpenApiParameter(
            name="show_by_pub", description="Show By Pub", required=True, type=str
        ),
        OpenApiParameter(
            name="showing_filters[]",
            description="Is Show Filter",
            required=True,
            type=str,
        ),
        OpenApiParameter(
            name="lst_flow_brand_name[]",
            description="Flow Title",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="clientID[]", description="ClientID", required=False, type=str
        ),
    ],
    operation_id="Filter Meta Data Reporting",
    tags=["AlertThreshold"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getClvFilterMetaDataReporting(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        try:
            showing_filters = request.GET.getlist(
                "showing_filters[]",
                ["original_sale", "as_of_date", "sales", "custom_fees"],
            )
            lst_flow_brand_name = request.GET.getlist("flow_brand_name[]", None)
            lst_clientID = request.GET.getlist("clientID[]", None)

            original_sale_start_date = request.GET.get("original_sale_start_date", None)
            original_sale_end_date = request.GET.get("original_sale_end_date", None)
            as_of_date_start_date = request.GET.get("as_of_date_start_date", None)
            as_of_date_end_date = request.GET.get("as_of_date_end_date", None)

            lst_clients, permissions = getClientView(request)
            if "SUPER-PARENT" in permissions.split("_"):
                lst_clientID = [lst_clients[0]["clientID"]]

            base_filters = Q(flow_brand_id__isnull=False, ancestor_date__isnull=False)
            if lst_clientID:
                base_filters &= Q(clientID__in=lst_clientID)

            flofin = (
                HomeCLVReport.objects.filter(base_filters)
                .select_related("flow_brand", "flow_step")
                .prefetch_related("flow_brand__flow_brand_name")
                .exclude(
                    flow_brand__status="Deleted",
                )
                .annotate(
                    flow_brand_name=F("flow_brand__flow_brand_name"),
                    flow_step_number=F("flow_step__flow_step_number"),
                    sales=Coalesce(
                        Sum("total_unique_sale"), Value(0), output_field=IntegerField()
                    ),
                )
                .filter(sales__gt=0)
            )

            flow_brand_names = (
                flofin.order_by("flow_brand_name")
                .values_list("flow_brand_name", flat=True)
                .distinct()
            )

            if lst_flow_brand_name:
                flofin = flofin.filter(flow_brand_name__in=lst_flow_brand_name)

            card_brands = (
                flofin.exclude(card_brand=None)
                .order_by("card_brand")
                .values_list("card_brand", flat=True)
                .distinct()
            )
            card_types = (
                flofin.exclude(card_type=None)
                .order_by("card_type")
                .values_list("card_type", flat=True)
                .distinct()
            )
            card_issuers = (
                flofin.exclude(card_issuer=None)
                .order_by("card_issuer")
                .values_list("card_issuer", flat=True)
                .distinct()
            )
            price_points = (
                flofin.exclude(price_point=None)
                .order_by("price_point")
                .values_list("price_point", flat=True)
                .distinct()
            )
            cycle_num = (
                flofin.exclude(cycle_num=None)
                .order_by("cycle_num")
                .values_list("cycle_num", flat=True)
                .distinct()
            )
            flow_step_number = (
                flofin.exclude(flow_step_number=None)
                .order_by("flow_step_number")
                .values_list("flow_step_number", flat=True)
                .distinct()
            )

            return JsonResponse(
                {
                    "message": "Get AlertThreshold Filter Value successfully.",
                    "data": [
                        {
                            "label": "Flow Title",
                            "key": "flow_brand_name",
                            "isShow": False
                            if "flow_brand_name" not in showing_filters
                            else True,
                            "type": "SELECT_MULTI",
                            "metaData": [
                                {"label": brand_name, "value": brand_name}
                                for brand_name in flow_brand_names
                            ],
                        },
                        {
                            "label": "Card Brand",
                            "key": "card_brand",
                            "isShow": False
                            if "card_brand" not in showing_filters
                            else True,
                            "type": "SELECT_MULTI",
                            "metaData": [
                                {"label": card, "value": card} for card in card_brands
                            ],
                        },
                        {
                            "label": "Card Type",
                            "key": "card_type",
                            "isShow": False
                            if "card_type" not in showing_filters
                            else True,
                            "type": "SELECT_MULTI",
                            "metaData": [
                                {"label": card_type, "value": card_type}
                                for card_type in card_types
                            ],
                        },
                        {
                            "label": "Price Point",
                            "key": "price_point",
                            "isShow": False
                            if "price_point" not in showing_filters
                            else True,
                            "type": "SELECT_MULTI",
                            "metaData": [
                                {"label": price_point, "value": price_point}
                                for price_point in price_points
                            ],
                        },
                        {
                            "label": "Billing Cycle",
                            "key": "cycle_num",
                            "isShow": False
                            if "cycle_num" not in showing_filters
                            else True,
                            "type": "SELECT_MULTI",
                            "metaData": [
                                {"label": cycle_num, "value": cycle_num}
                                for cycle_num in cycle_num
                            ],
                        },
                        {
                            "label": "Flow Step",
                            "key": "flow_step_number",
                            "isShow": False
                            if "flow_step_number" not in showing_filters
                            else True,
                            "type": "SELECT_MULTI",
                            "metaData": [
                                {"label": flow_step_number, "value": flow_step_number}
                                for flow_step_number in flow_step_number
                            ],
                        },
                        {
                            "label": "Card Issuer",
                            "key": "card_issuer",
                            "isShow": False
                            if "card_issuer" not in showing_filters
                            else True,
                            "type": "SELECT_MULTI",
                            "metaData": [
                                {"label": card_issuer, "value": card_issuer}
                                for card_issuer in card_issuers
                            ],
                        },
                        {
                            "label": "Original Sales Date",
                            "key": "original_sale",
                            "isShow": False
                            if "original_sale" not in showing_filters
                            else True,
                            "type": "DATE_RANGE",
                            "metaData": {
                                "start_date": date.today() - timedelta(days=90)
                                if not original_sale_start_date
                                else parse_date(original_sale_start_date),
                                "end_date": date.today()
                                if not original_sale_end_date
                                else parse_date(original_sale_end_date),
                                "list_date": None,
                            },
                        },
                        {
                            "label": "As of Date",
                            "key": "as_of_date",
                            "isShow": False
                            if "as_of_date" not in showing_filters
                            else True,
                            "type": "DATE_RANGE",
                            "metaData": {
                                "start_date": date.today() - timedelta(days=90)
                                if not as_of_date_start_date
                                else parse_date(as_of_date_start_date),
                                "end_date": date.today()
                                if not as_of_date_end_date
                                else parse_date(as_of_date_end_date),
                                "list_date": None,
                            },
                        },
                    ],
                }
            )
        except Exception as e:
            return JsonResponse(
                {"message": "Get data unsuccessfully", "exception": str(e)}, status=400
            )

    return JsonResponse({"message": "Invalid request method"}, status=405)


@extend_schema(
    parameters=[
        OpenApiParameter(
            name="showing_filters[]",
            description="Is Show Filter",
            required=True,
            type=str,
        ),
        OpenApiParameter(
            name="lst_flow_brand_name[]",
            description="Flow Title",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="clientID[]", description="ClientID", required=False, type=str
        ),
    ],
    operation_id="Filter Meta Data Reporting",
    tags=["AlertThreshold"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getClvFilterMetaDataFraud(request: HttpRequest) -> JsonResponse:
    """
    Get metadata for fraud reporting filters.

    This function retrieves filter options for the fraud reporting interface, including:
    - Flow titles
    - Card brands
    - Card types
    - Price points
    - Flow step numbers
    - Sales date ranges

    Args:
        request: HTTP request containing filter parameters

    Returns:
        JSON response with filter metadata
    """
    if request.method != "GET":
        return JsonResponse({"message": "Invalid request method"}, status=405)

    try:
        # Extract filter parameters from request
        showing_filters = request.GET.getlist(
            "showing_filters[]", ["original_sale", "as_of_date", "sales", "custom_fees"]
        )
        lst_flow_brand_name = request.GET.getlist("flow_brand_name[]", None)
        lst_clientID = request.GET.getlist("clientID[]", None)
        original_sale_start_date = request.GET.get("original_sale_start_date", None)
        original_sale_end_date = request.GET.get("original_sale_end_date", None)

        # Get client permissions
        lst_clients, permissions = getClientView(request)
        if "SUPER-PARENT" in permissions.split("_"):
            lst_clientID = [lst_clients[0]["clientID"]]

        # Build base filter conditions
        base_filters = Q(
            is_test=False,
            card_infor__isnull=False,
            vendorid1__isnull=False,
            score__isnull=False,
            retention__isnull=False,
            retention_gt__isnull=False,
        )

        # Add client filter if provided
        if lst_clientID:
            base_filters = base_filters & Q(
                client_login__listClientsUsed__in=lst_clientID
            )

        # Base query for order imports with all necessary annotations in a single query
        base_query = (
            OrderImport.objects.filter(base_filters)
            .annotate(
                flow_brand_name=F(
                    "offer_product__offer_flow_step__flow__flow_brand_name"
                ),
                flow_step_number=F("offer_product__offer_flow_step__flow_step_number"),
                card_brand=F("card_infor__card_brand"),
                card_type=F("card_infor__card_type"),
            )
            .filter(flow_brand_name__isnull=False, ancestor_date__isnull=False)
        )

        # Apply flow brand name filter if provided
        if lst_flow_brand_name:
            base_query = base_query.filter(flow_brand_name__in=lst_flow_brand_name)

        # Get distinct values for each filter in a single query to reduce database hits
        # Use a single query with annotations to get all distinct values at once
        distinct_values = base_query.values(
            "flow_brand_name",
            "card_brand",
            "card_type",
            "price_point",
            "flow_step_number",
        ).distinct()

        # Process the results to extract unique values for each filter
        flow_brand_names = set()
        card_brands = set()
        card_types = set()
        price_points = set()
        flow_step_numbers = set()

        for item in distinct_values:
            if item["flow_brand_name"]:
                flow_brand_names.add(item["flow_brand_name"])
            if item["card_brand"]:
                card_brands.add(item["card_brand"])
            if item["card_type"]:
                card_types.add(item["card_type"])
            if item["price_point"]:
                price_points.add(item["price_point"])
            if item["flow_step_number"]:
                flow_step_numbers.add(item["flow_step_number"])

        # Sort the sets for consistent output
        flow_brand_names = sorted(flow_brand_names)
        card_brands = sorted(card_brands)
        card_types = sorted(card_types)
        price_points = sorted(price_points)
        flow_step_numbers = sorted(flow_step_numbers)

        # Build response data
        filter_data = [
            {
                "label": "Flow Title",
                "key": "flow_brand_name",
                "isShow": "flow_brand_name" in showing_filters,
                "type": "SELECT_MULTI",
                "metaData": [
                    {"label": brand_name, "value": brand_name}
                    for brand_name in flow_brand_names
                ],
            },
            {
                "label": "Card Brand",
                "key": "card_brand",
                "isShow": "card_brand" in showing_filters,
                "type": "SELECT_MULTI",
                "metaData": [{"label": card, "value": card} for card in card_brands],
            },
            {
                "label": "Card Type",
                "key": "card_type",
                "isShow": "card_type" in showing_filters,
                "type": "SELECT_MULTI",
                "metaData": [
                    {"label": card_type, "value": card_type} for card_type in card_types
                ],
            },
            {
                "label": "Price Point",
                "key": "price_point",
                "isShow": "price_point" in showing_filters,
                "type": "SELECT_MULTI",
                "metaData": [
                    {"label": price_point, "value": price_point}
                    for price_point in price_points
                ],
            },
            {
                "label": "Flow Step",
                "key": "flow_step_number",
                "isShow": "flow_step_number" in showing_filters,
                "type": "SELECT_MULTI",
                "metaData": [
                    {"label": step, "value": step} for step in flow_step_numbers
                ],
            },
            {
                "label": "Sales Date",
                "key": "original_sale",
                "isShow": "original_sale" in showing_filters,
                "type": "DATE_RANGE",
                "metaData": {
                    "start_date": date.today() - timedelta(days=90)
                    if not original_sale_start_date
                    else parse_date(original_sale_start_date),
                    "end_date": date.today()
                    if not original_sale_end_date
                    else parse_date(original_sale_end_date),
                    "list_date": None,
                },
            },
        ]

        return JsonResponse(
            {
                "message": "Get Sales Quality Monitor Filter Value successfully.",
                "data": filter_data,
            },
            status=200,
        )
    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)}, status=400
        )


@extend_schema(
    parameters=[
        OpenApiParameter(
            name="showing_filters[]",
            description="Is Show Filter",
            required=True,
            type=str,
        ),
        OpenApiParameter(
            name="lst_flow_brand_name[]",
            description="Flow Title",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="clientID[]", description="ClientID", required=False, type=str
        ),
    ],
    operation_id="Filter Meta Data AlertThreshold",
    tags=["AlertThreshold"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getClvFilterMetaDataAlertThreshold(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        try:
            lst_clientID = request.GET.getlist("clientID[]", None)

            lst_clients, permissions = getClientView(request)
            if "SUPER-PARENT" in permissions.split("_"):
                lst_clientID = [lst_clients[0]["clientID"]]

            flofin = HomeCLVReport.objects.all().exclude(flow_brand__status="Deleted")
            flofin = flofin.annotate(
                flow_brand_name=F("flow_brand__flow_brand_name"),
                flow_step_number=F("flow_step__flow_step_number"),
            )
            flofin = flofin.exclude(ancestor_date=None, flow_brand_name=None)
            if lst_clientID:
                flofin = flofin.filter(clientID__in=lst_clientID)

            flow_brand_names = (
                flofin.exclude(ancestor_date=None)
                .order_by("flow_brand_name")
                .values_list("flow_brand_name", flat=True)
                .distinct()
            )
            card_brands = (
                flofin.exclude(card_brand=None)
                .order_by("card_brand")
                .values_list("card_brand", flat=True)
                .distinct()
            )
            card_types = (
                flofin.exclude(card_type=None)
                .order_by("card_type")
                .values_list("card_type", flat=True)
                .distinct()
            )
            networks = (
                flofin.exclude(network=None)
                .order_by("network")
                .values_list("network", flat=True)
                .distinct()
            )
            pubs = (
                flofin.exclude(pub=None)
                .order_by("pub")
                .values_list("pub", flat=True)
                .distinct()
            )
            merchantIds = (
                flofin.exclude(merchantId=None)
                .order_by("merchantId")
                .values_list("merchantId", flat=True)
                .distinct()
            )

            return JsonResponse(
                {
                    "message": "Get AlertThreshold Filter Value successfully.",
                    "data": {
                        "flow_brand_name": [
                            {"label": brand_name, "value": brand_name}
                            for brand_name in (["All"] + list(flow_brand_names))
                        ],
                        "card_brand": [
                            {"label": card, "value": card}
                            for card in (["All"] + list(card_brands))
                        ],
                        "card_type": [
                            {"label": card_type, "value": card_type}
                            for card_type in (["All"] + list(card_types))
                        ],
                        "network": [
                            {"label": network, "value": network}
                            for network in (["All"] + list(networks))
                        ],
                        "affiliate": [
                            {"label": pub, "value": pub}
                            for pub in (["All"] + list(pubs))
                        ],
                        "merchantId": [
                            {"label": merchantId, "value": merchantId}
                            for merchantId in (["All"] + list(merchantIds))
                        ],
                    },
                }
            )
        except Exception as e:
            return JsonResponse(
                {"message": "Get data unsuccessfully", "exception": str(e)}, status=400
            )

    return JsonResponse({"message": "Invalid request method"}, status=405)


def get_filter_from_request(request: HttpRequest) -> dict:
    clients = request.GET.getlist("clientID[]", [])
    flow_brand_name = request.GET.getlist("flow_brand_name[]", [])
    lst_flow_brand_name = request.GET.getlist("lst_flow_brand_name[]", [])

    card_brand = request.GET.getlist("card_brand[]", [])
    card_type = request.GET.getlist("card_type[]", [])
    card_issuer = request.GET.getlist("card_issuer[]", [])

    network = request.GET.getlist("network[]", [])
    pub = request.GET.getlist("pub[]", [])

    cycle_num = request.GET.getlist("cycle_num[]", [])
    price_point = request.GET.getlist("price_point[]", [])
    flow_step_number = request.GET.getlist("flow_step_number[]", [])

    original_sale_start_date = request.GET.get("original_sale_start_date")
    original_sale_end_date = request.GET.get("original_sale_end_date")
    as_of_date_start_date = request.GET.get("as_of_date_start_date")
    as_of_date_end_date = request.GET.get("as_of_date_end_date")

    lst_clients, permissions = getClientView(request)
    if "SUPER-PARENT" in permissions.split("_"):
        clients = [lst_clients[0]["clientID"]]

    filters = Q()
    if clients:
        filters &= Q(clientID__in=clients)
    if flow_brand_name:
        filters &= Q(flow_brand__flow_brand_name__in=flow_brand_name)
    if lst_flow_brand_name:
        filters &= Q(flow_brand__flow_brand_name__in=lst_flow_brand_name)
    if card_brand:
        filters &= Q(card_brand__in=card_brand)
    if card_type:
        filters &= Q(card_type__in=card_type)
    if card_issuer:
        filters &= Q(card_issuer__in=card_issuer)
    if network:
        filters &= Q(network__in=network)
    if pub:
        filters &= Q(pub__in=pub)

    if cycle_num:
        filters &= Q(cycle_num__in=cycle_num)
    if price_point:
        filters &= Q(price_point__in=price_point)
    if flow_step_number:
        filters &= Q(flow_step_number__in=flow_step_number)

    if original_sale_start_date:
        filters &= Q(ancestor_date__gte=parse_date(original_sale_start_date))
    if original_sale_end_date:
        filters &= Q(ancestor_date__lte=parse_date(original_sale_end_date))
    if as_of_date_start_date:
        filters &= Q(Q(transaction_date__gte=parse_date(as_of_date_start_date)))
    if as_of_date_end_date:
        filters &= Q(Q(transaction_date__lte=parse_date(as_of_date_end_date)))

    return filters
