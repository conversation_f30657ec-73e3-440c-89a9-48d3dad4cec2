import os
import threading
from datetime import datetime

from django.db import transaction
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Count, F, Func, Value, Q
from django.http import HttpRequest, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from dotenv import find_dotenv, load_dotenv
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework.decorators import api_view
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_405_METHOD_NOT_ALLOWED,
)

from data.models import ClientLoginInformation, ClientsInformation
from flofin.models import (
    OfferFlow,
    OrderImport,
)
from flofin.serializers import getListClientResponseSerializer
from flofin.services import TABLE_PARAMS, filterQueryAfter
from flofin.services.utils import getClientView
from user.utils.utils import require_login
from flofin.celery.tasks import run_data_flow_task, process_delete_sale_flow_task

# Extract flow data
FLOW_FIELDS = {
    "flow_id",
    "flow_brand_name",
    "flow_descriptor1",
    "flow_descriptor2",
    "flow_descriptor3",
    "flow_descriptor4",
    "flow_descriptor5",
}
STEP_FIELDS = {"flow_step_number", "cont_cycle_frequency", "type"}
PRODUCT_FIELDS = {
    "cycle_number",
    "step_price",
    "product_match",
    "product_cost",
    "shipping_cost",
    "cpa_cost",
    "misc_cost",
}
dotenv_file = find_dotenv()
load_dotenv(dotenv_file, override=True)
ENVIRONMENT = os.getenv("ENVIRONMENT")


@extend_schema(
    methods=["GET"],
    parameters=[],
    description="Get list of clients active",
    responses={HTTP_200_OK: getListClientResponseSerializer},
    auth=None,
    operation_id="getListClients",
    tags=["sale-flow"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getListClients(request: HttpRequest) -> JsonResponse:
    try:
        clients, _ = getClientView(request)
        for client in clients:
            client_login = (
                ClientLoginInformation.objects.filter(
                    listClientsUsed=client["clientID"],
                    loginType="API",
                    listClientsUsed__contains=client["clientID"],
                    loginPlatform__isnull=False,
                    loginID__isnull=False,
                    username__isnull=False,
                )
                .exclude(validation=2)
                .first()
            )
            if not client_login:
                clients.remove(client)
        return JsonResponse(
            {"message": "Get list of clients successfully!", "data": clients},
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get list of clients failed", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=[
        OpenApiParameter(name="clientID", required=True, type=str),
    ],
    description="Get list clients login",
    responses={HTTP_200_OK: "Get list of clients login successfully!"},
    auth=None,
    operation_id="getListClients",
    tags=["sale-flow"],
    operation=None,
    methods=["GET"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getLoginByClient(request: HttpRequest) -> JsonResponse:
    try:
        client_id = request.GET.get("clientID")
        client_login_filter = Q(
            loginType="API",
            loginPlatform__isnull=False,
            loginID__isnull=False,
            username__isnull=False,
            listClientsUsed__isnull=False,
        )
        client_login_id_in_use = (
            OrderImport.objects.filter(client_login__isnull=False)
            .values_list("client_login", flat=True)
            .distinct()
        )
        client_login_filter &= Q(id__in=client_login_id_in_use)

        if client_id != "All":
            client_login_filter &= Q(listClientsUsed=client_id)

        client_logins = (
            ClientLoginInformation.objects.filter(client_login_filter)
            .exclude(validation=2)
            .annotate(crm_id=F("loginID"), crm=F("loginPlatform"))
            .values("loginID", "loginPlatform", "crm", "crm_id", "username")
        )

        for client_login in client_logins:
            if client_login["crm"] is None:
                client_login["crm"] = ""
            if client_login["loginPlatform"] is None:
                client_login["loginPlatform"] = ""
            if client_login["username"] is None:
                client_login["username"] = ""

            client_login["crm"] = client_login["crm"] + " - " + client_login["username"]
            client_login["loginPlatform"] = (
                client_login["loginPlatform"] + " - " + client_login["username"]
            )
        return JsonResponse(
            {
                "message": "Get list of clients login successfully!",
                "data": list(client_logins),
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get list of clients login failed", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=[
        OpenApiParameter(name="loginID", required=True, type=str),
    ],
    description="Get list products",
    responses={HTTP_200_OK: "getListProducts successfully!"},
    auth=None,
    operation_id="getListProducts",
    tags=["sale-flow"],
    operation=None,
    methods=["GET"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get_list_products(request: HttpRequest) -> JsonResponse:
    try:
        login_id = request.GET.get("loginID")

        product_list = (
            OrderImport.objects.filter(
                client_login__loginID=login_id, offer_product_id__isnull=True
            )
            .distinct()
            .values_list("product_match", flat=True)
        )

        if ENVIRONMENT == "SANDBOX" and login_id == "recHgBj9BsgGB9nq7":
            length = len(product_list)
            product_list = [
                "DemoProduct" + str(i).zfill(3) for i in range(1, length + 1)
            ]
        return JsonResponse(
            {
                "message": "Get list product of crm successfully!",
                "data": list(product_list),
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get list product of crm failed", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=TABLE_PARAMS,
    responses={HTTP_200_OK: "getTableSaleFlows"},
    auth=None,
    exclude=True,
    operation_id="getTableSaleFlows",
    tags=["sale-flow"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getTableSaleFlows(request: HttpRequest) -> JsonResponse:
    try:
        lst_clients, permissions = getClientView(request)
        base_filter = Q(client__isnull=False)
        if "SUPER-PARENT" in permissions.split("_"):
            clients = [lst_clients[0]["clientID"]]
            print("clients", clients)
            base_filter &= Q(client__clientID__in=clients)

        flows_query = (
            OfferFlow.objects.filter(
                base_filter,
                status__in=["Pending", "Active"],
            )
            .annotate(
                number_step=Count("offerflowsteps"),
                client_name=F("client__name"),
                client_ID=F("client__clientID"),
                crm=F("client_login__loginPlatform"),
                crm_id=F("client_login__loginID"),
                formatted_created_at=Func(
                    F("created_at"),
                    Value("MMM dd, yyyy"),
                    function="FORMAT",
                    output_field=CharField(),
                ),
                # status=Value("Success", output_field=CharField()),
            )
            .values(
                "flow_id",
                "client_name",
                "flow_brand_name",
                "number_step",
                "crm",
                "crm_id",
                "created_at",
                "client_ID",
                "flow_descriptor1",
                "flow_descriptor2",
                "flow_descriptor3",
                "flow_descriptor4",
                "flow_descriptor5",
                "formatted_created_at",
                "status",
            )
        )
        flows_list, paginator = filterQueryAfter(request, flows_query)

        return JsonResponse(
            {
                "message": "Get table sale flows successfully!",
                "pagination": paginator,
                "data": flows_list,
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get table sale flows failed", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


def extractModelData(instance, fields):
    """Utility function to extract model fields excluding specified fields."""
    d = {key: value for key, value in instance.__dict__.items() if key in fields}
    if "flow_brand_name" in d:
        d["client_name"] = instance.client.name
        d["clientID"] = instance.client.clientID
        d["crm"] = instance.client_login.loginPlatform
        d["crm_id"] = instance.client_login.loginID
    return d


def get_sales_flow_data(flow_id):
    flow = OfferFlow.objects.filter(flow_id=flow_id).first()
    if not flow:
        return {key: None for key in FLOW_FIELDS}
    flow_data = extractModelData(flow, FLOW_FIELDS)

    flow_data["steps"] = []
    steps = flow.offerflowsteps_set.all()
    for step in steps:
        step_data = extractModelData(step, STEP_FIELDS)

        products = step.offerproductdetail_set.filter().order_by("cycle_number")
        step_data["products"] = []
        for product in products:
            if product.cycle_number == 0:
                step_data["product_match"] = product.product_match
                step_data["initial_price"] = product.step_price
                step_data["product_cost"] = product.product_cost
                step_data["shipping_cost"] = product.shipping_cost
                step_data["cpa_cost"] = product.cpa_cost
                step_data["misc_cost"] = product.misc_cost
            else:
                product_data = extractModelData(product, PRODUCT_FIELDS)
                step_data["products"].append(product_data)

        step_type = step_data["type"]
        if step_type == "":
            if step_data["flow_step_number"] == 1:
                step_data["type"] = "Initial Offer"
            elif step_data["flow_step_number"] >= 2:
                step_data["type"] = f"Upsell {step_data['flow_step_number'] - 1}"

        flow_data["steps"].append(step_data)

    return flow_data


@extend_schema(
    parameters=[OpenApiParameter(name="flow_id", required=True, type=int)],
    responses={HTTP_200_OK: "viewSaleFlow"},
    auth=None,
    operation_id="sale-flow",
    tags=["sale-flow"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def view_sales_flow(request: HttpRequest) -> JsonResponse:
    try:
        flow_id = request.GET.get("flow_id")
        flow_data = get_sales_flow_data(flow_id)
        return JsonResponse(
            {"message": "View Sale Flow successfully!", "data": flow_data},
            status=HTTP_200_OK,
        )
    except Exception as e:
        return JsonResponse(
            {"message": "View Sale Flow failed", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=[],
    responses={HTTP_200_OK: "Adjust Sale Flow"},
    auth=None,
    operation_id="Adjust Sale Flow",
    tags=["sale-flow"],
    operation=None,
)
@csrf_exempt
@api_view(["POST"])
@require_login
def adjust_sales_flow(request: HttpRequest) -> JsonResponse:
    if request.method != "POST":
        return JsonResponse(
            {"message": "Invalid request method"}, status=HTTP_405_METHOD_NOT_ALLOWED
        )
    try:
        request_data = request.data
        if not request_data:
            return JsonResponse(
                {"message": "No data provided"}, status=HTTP_400_BAD_REQUEST
            )

        flow_id = request_data.get("flow_id", None)
        change_flow_data = request_data

        client = ClientsInformation.objects.get(
            clientID=change_flow_data.get("clientID")
        )
        client_login = ClientLoginInformation.objects.get(
            loginID=change_flow_data.get("crm_id")
        )
        with transaction.atomic():
            # Create or update flow
            flow_data = {
                key: value
                for key, value in change_flow_data.items()
                if key in FLOW_FIELDS
            }

            # if flow_id is None that mean this request is to create a new one
            if flow_id is None:
                flow_id = OfferFlow.objects.latest("flow_id").flow_id + 1

            flow_data["client"] = client
            flow_data["client_login"] = client_login
            flow_data["status"] = "Pending"

            # create or update the sales flow
            flow, _ = OfferFlow.objects.update_or_create(
                flow_id=flow_id, defaults=flow_data
            )

            # Create or update steps
            steps = change_flow_data.get("steps", [])

            # steps is a list of initial offer and upsell
            for step in steps:
                flow_step_number = step.get("flow_step_number", None)

                if flow_step_number is None:
                    return JsonResponse(
                        {"message": "flow_step_number not provided"},
                        status=HTTP_400_BAD_REQUEST,
                    )

                if (
                    not flow.offerflowsteps_set.filter(
                        flow_step_number=(flow_step_number - 1)
                    ).exists()
                    and flow_step_number != 1
                ):
                    return JsonResponse(
                        {"message": "Please add previous step first"},
                        status=HTTP_400_BAD_REQUEST,
                    )

                new_step_data = {
                    key: value for key, value in step.items() if key in STEP_FIELDS
                }

                db_step, _ = flow.offerflowsteps_set.update_or_create(
                    flow_step_number=flow_step_number,
                    defaults={**new_step_data, "flow": flow},
                )

                # Create or update products
                cycle_0_data = {
                    "cycle_number": 0,
                    "step_price": step.get("initial_price", None),
                    "product_match": step.get("product_match", None),
                    "product_cost": step.get("product_cost", None),
                    "shipping_cost": step.get("shipping_cost", None),
                    "misc_cost": step.get("misc_cost", 0),
                    "cpa_cost": step.get("cpa_cost", 0),
                }

                products = [cycle_0_data] + step.get("products", [])
                for product in products:
                    cycle_number = product.get("cycle_number", None)
                    if cycle_number is None:
                        return JsonResponse(
                            {"message": "cycle_number not provided"},
                            status=HTTP_400_BAD_REQUEST,
                        )

                    new_product_data = {
                        key: value
                        for key, value in product.items()
                        if key in PRODUCT_FIELDS and value != ""
                    }

                    db_step.offerproductdetail_set.update_or_create(
                        cycle_number=cycle_number,
                        defaults={**new_product_data, "offer_flow_step": db_step},
                    )

                existing_cycle_numbers = [
                    product["cycle_number"] for product in products
                ]
                db_step.offerproductdetail_set.exclude(
                    cycle_number__in=existing_cycle_numbers
                ).delete()

            # Delete steps that are not in the provided steps list
            existing_step_numbers = [step["flow_step_number"] for step in steps]
            detele_steps = flow.offerflowsteps_set.exclude(
                flow_step_number__in=existing_step_numbers
            )
            for step in detele_steps:
                step.offerproductdetail_set.all().delete()
            detele_steps.delete()
        flow_data = get_sales_flow_data(flow.flow_id)
        
        
        run_data_flow_task.delay(
                client_login.id,
                datetime(2025, 1, 1, 0, 0, 0).strftime("%Y-%m-%d"),
                datetime.now().strftime("%Y-%m-%d"),
                [flow.flow_id],
        )
        # threading.Thread(
        #     target=run_data_flow,
        #     args=(
        #         client_login,
        #         datetime(2024, 1, 1, 0, 0, 0).strftime("%Y-%m-%d"),
        #         datetime.now().strftime("%Y-%m-%d"),
        #         [flow],
        #     ),
        # ).start()
        return JsonResponse(
            {
                "message": "Add or Update Sale Flow successfully!, Please wait about 5 hours to see the changes",
                "data": flow_data,
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Add or Update Sale Flow failed", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )




@extend_schema(
    parameters=[OpenApiParameter(name="flow_id", required=True, type=int)],
    responses={HTTP_200_OK: "remove_sale_flow"},
    auth=None,
    operation_id="sale-flow",
    tags=["sale-flow"],
    operation=None,
)
@csrf_exempt
@api_view(["DELETE"])
@require_login
def remove_sale_flow(request: HttpRequest) -> JsonResponse:
    if request.method != "DELETE":
        return JsonResponse(
            {"message": "Invalid request method"}, status=HTTP_405_METHOD_NOT_ALLOWED
        )
    try:
        lst_clients, permissions = getClientView(request)
        clients = [client["clientID"] for client in lst_clients]
        delete_by = permissions
        if "SUPER-PARENT" in permissions.split("_"):
            clients = [lst_clients[0]["clientID"]]
            delete_by = clients
            print("clients", clients)

        flow_id = request.GET.get("flow_id")
        if not flow_id:
            return JsonResponse(
                {"message": "Flow ID not provided"}, status=HTTP_400_BAD_REQUEST
            )

        existing_flow = OfferFlow.objects.filter(
            flow_id=flow_id, client__clientID__in=clients
        ).first()

        if not existing_flow:
            return JsonResponse(
                {"message": "Flow not found"}, status=HTTP_400_BAD_REQUEST
            )
            
        existing_flow.status = "Deleted"
        existing_flow.save()
        
        # Run celery task
        process_delete_sale_flow_task.delay(flow_id, delete_by)

        # threading.Thread(
        #     target=process_delete_sale_flow,
        #     args=(flow_id, delete_by),
        # ).start()

        return JsonResponse(
            {"message": "Flow deleted successfully!"}, status=HTTP_200_OK
        )
    except Exception as e:
        return JsonResponse(
            {"message": "Delete Sale Flow failed", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )
