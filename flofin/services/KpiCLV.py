import csv
import os
from dotenv import load_dotenv
from datetime import date, datetime, timedelta
from io import String<PERSON>
from typing import List, <PERSON>ple

import numpy as np
import pandas as pd
from dateutil.relativedelta import relativedelta
from django.db.models import (
    F,
    <PERSON><PERSON><PERSON>ield,
    IntegerField,
    Max,
    Min,
    Q,
    Sum,
    Value,
    OuterRef,
    Subquery,
)
from django.db.models.functions import <PERSON>esce, TruncDay, TruncMonth
from django.db.models.query import QuerySet
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.utils.dateparse import parse_date
from django.views.decorators.csrf import csrf_exempt
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework.decorators import api_view
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_405_METHOD_NOT_ALLOWED,
)

from data.models import ClientsInformation
from flofin.models import HomeCLVReport, OfferProductDetail
from flofin.serializers import (
    GetDataUnsuccessfulResponseSerializer,
    getCLVResponseSerializer,
    getClvToolTipsResponseSerializer,
    inValidMethodResponseSerializer,
)
from flofin.services.utils import getClientView, get_filter_from_request
from user.utils.utils import require_login


load_dotenv()
MAX_STEPS = int(os.getenv("MAX_SALES_FLOW_STEP"))
MAX_CYCLES = int(os.getenv("MAX_CYCLES"))


def get_total_sales_annotations(
    n_cycles: int, n_steps: int, outer_ref_queryset: QuerySet
):
    """
    Generate dynamic annotations for total sales per cycle and step using correlated subqueries.

    Args:
        n_cycles (int): Number of cycles.
        n_steps (int): Number of steps per cycle.
        outer_ref_queryset (QuerySet): The original queryset to build subqueries against.

    Returns:
        dict: A dictionary of annotations (Subquery expressions).
    """
    annotations = {}

    for cycle in range(n_cycles):
        for step in range(1, n_steps + 1):  # Steps start from 1
            key = f"total_sales_cycle_{cycle}_step_{step}"

            # Define the filters for the subquery to correlate with the outer query's group
            subquery_filter = Q(
                clientID=OuterRef("clientID"),
                flow_brand_name=OuterRef("flow_brand_name"),
                transaction_date__date=OuterRef("date"),  # Correlate by truncated date
                ancestor_date__date=OuterRef(
                    "as_of_date"
                ),  # Correlate by truncated as_of_date
                network=OuterRef("network"),  # Correlate network
                pub=OuterRef("pub"),  # Correlate pub
                cycle_num=cycle,
                flow_step_number=step,
            )

            # Build the subquery: filter the original queryset and sum total_sales
            total_sales_sub = (
                outer_ref_queryset.filter(subquery_filter)
                .annotate(total=Sum("total_sales"))
                .values("total")[:1]
            )  # [:1] to ensure it returns a single value

            annotations[key] = Coalesce(
                Subquery(total_sales_sub), Value(0), output_field=IntegerField()
            )

    return annotations


def calculate_clv_show_metrics(
    queryset: QuerySet[HomeCLVReport],
    group_by="month",
    show_by_network=None,
    show_by_pub=None,
    additional_value=None,
):
    date_trunc = TruncMonth if group_by == "month" else TruncDay

    # Step 1: Annotate base fields (dates, flow_brand_name, flow_step_number)
    metrics = queryset.annotate(
        date=date_trunc("transaction_date"),
        as_of_date=date_trunc("ancestor_date"),
        trans_date=TruncMonth(
            "transaction_date"
        ),  # Always use TruncMonth for trans_date
        flow_brand_name=F("flow_brand__flow_brand_name"),
        flow_step_number=F("flow_step__flow_step_number"),
    )

    # Step 2: Define grouping fields for .values() call
    grouping_fields = [
        "clientID",
        "flow_brand_name",
        "date",
        "as_of_date",
        "trans_date",  # Always include trans_date for consistent grouping
        "network",  # Always include network for consistent grouping
        "pub",  # Always include pub for consistent grouping
        "flow_step_number",
        "cycle_num",
        "card_brand",
        "card_type",
        "price_point",
        "card_issuer",
        "merchantId",
        "voids",
        "refunds",
    ]

    # Step 3: Apply .values() to define the grouping
    grouped_metrics = metrics.values(*grouping_fields)

    # Step 4: Apply all aggregate functions
    metrics = grouped_metrics.annotate(
        sales=Coalesce(Sum("total_unique_sale"), Value(0), output_field=IntegerField()),
        revenue=Coalesce(
            Sum("post_tax_order_total"), Value(0), output_field=FloatField()
        ),
        refuded_revenue=Sum("refunds_amount"),
        voided_revenue=Sum("voids_amount"),
        cb_revenue=Sum("chargebacks_amount"),
        cb_count=Sum("chargebacks"),
    )

    # Step 5: Apply ordering
    order_by_fields = [
        "clientID",
        "flow_brand_name",
        "date",
        "trans_date",
    ]
    if show_by_network:
        order_by_fields.append("network")
    if show_by_pub:
        order_by_fields.append("pub")

    metrics = metrics.order_by(*order_by_fields)

    return metrics


def get_clv_data(
    request: HttpRequest, type_of_data="overall", additional_value=None
) -> QuerySet[HomeCLVReport]:
    filters = get_filter_from_request(request)

    show_by_date = request.GET.get("show_by_date")
    show_by_network = request.GET.get("show_by_network")
    show_by_pub = request.GET.get("show_by_pub")

    sales_start_number = request.GET.get("sales_start_number")
    sales_end_number = request.GET.get("sales_end_number")

    filters &= Q(
        flow_brand_id__isnull=False,
        ancestor_date__isnull=False,
        flow_brand__status__in=["Active", "Pending"],
    )

    # Ensure flow_brand_name and flow_step_number are annotated on the initial queryset
    # as they are needed as base fields for grouping/filtering in calculate_clv_show_metrics
    queryset = (
        HomeCLVReport.objects.select_related("flow_brand", "flow_step")
        .filter(filters)
        .annotate(
            flow_brand_name=F("flow_brand__flow_brand_name"),
            flow_step_number=F("flow_step__flow_step_number"),
        )
    )

    metrics = calculate_clv_show_metrics(
        queryset,
        group_by="day" if show_by_date == "1" else "month",
        show_by_network=show_by_network,
        show_by_pub=show_by_pub,
        additional_value=additional_value,
    )

    # Apply sales number filter if provided, after aggregation in calculate_clv_show_metrics
    if sales_start_number:
        metrics = metrics.filter(sales__gte=int(sales_start_number))
    if sales_end_number:
        metrics = metrics.filter(sales__lte=int(sales_end_number))

    # Filter sales > 0 after initial aggregation in calculate_clv_show_metrics
    metrics = metrics.filter(sales__gt=0)

    return metrics


def calculateCashFlowMetrics(
    queryset: QuerySet[HomeCLVReport],
    group_by="month",
    show_by_network=None,
    show_by_pub=None,
):
    date_trunc = TruncMonth if group_by == "month" else TruncDay
    metrics = queryset.annotate(
        date=date_trunc("ancestor_date"), trans_date=TruncMonth("transaction_date")
    )

    # Define the grouping fields for .values() call.
    grouping_fields = [
        "clientID",
        "flow_brand_name",
        "date",
        "trans_date",
    ]

    if show_by_network:
        grouping_fields.append("network")
    if show_by_pub:
        grouping_fields.append("pub")

    # Apply .values() to define the grouping for the subsequent aggregations.
    grouped_metrics = metrics.values(*grouping_fields)

    # Apply all aggregate functions for each defined group.
    total_sales_subquery_annotations = get_total_sales_annotations(
        n_cycles=MAX_CYCLES, n_steps=MAX_STEPS, outer_ref_queryset=queryset
    )

    metrics = grouped_metrics.annotate(
        sales=Coalesce(Sum("total_unique_sale"), Value(0), output_field=IntegerField()),
        revenue=Coalesce(
            Sum("post_tax_order_total"), Value(0), output_field=FloatField()
        ),
        **total_sales_subquery_annotations,
        refuded_revenue=Sum("refunds_amount"),
        voided_revenue=Sum("voids_amount"),
        cb_revenue=Sum("chargebacks_amount"),
        cb_count=Sum("chargebacks"),
    )

    # Ordering should match the grouping
    order_by_fields = [
        "clientID",
        "flow_brand_name",
        "date",
        "trans_date",
    ]
    if show_by_network:
        order_by_fields.append("network")
    if show_by_pub:
        order_by_fields.append("pub")

    metrics = metrics.order_by(*order_by_fields)

    return metrics


def calculate_product_cost(x, product_details, n_cycles, n_steps):
    """
    Calculate the total product cost dynamically for any number of cycles and steps.

    Args:
        x (dict): A dictionary containing sales data for cycles and steps.
        product_details (dict): A dictionary containing product cost and shipping cost for each cycle and step.
        n_cycles (int): Number of cycles.
        n_steps (int): Number of steps per cycle.

    Returns:
        float: The total product cost.
    """
    product_cost = 0

    # Iterate through cycles and steps dynamically
    cycle_num = x["cycle_num"]
    step_num = x["flow_step_number"]
    count_sales = x["sales"]

    # Iterate through cycles and steps dynamically
    if product_details:
        product_cost = (
            product_details[(cycle_num, step_num)]["product_cost"] * count_sales
        )

    return float(product_cost)


def calculate_cpa_cost(x, product_details, n_cycles, n_steps):
    cpa_cost = 0
    cycle_num = x["cycle_num"]
    step_num = x["flow_step_number"]
    count_sales = x["sales"]

    # Iterate through cycles and steps dynamically
    if product_details:
        cpa_cost = product_details[(cycle_num, step_num)]["cpa_cost"] * count_sales

    return float(cpa_cost)


def calculate_shipping_cost(x, product_details, n_cycles, n_steps):
    shipping_cost = 0

    # Iterate through cycles and steps dynamically
    cycle_num = x["cycle_num"]
    step_num = x["flow_step_number"]
    count_sales = x["sales"]

    # Iterate through cycles and steps dynamically
    if product_details:
        shipping_cost = (
            product_details[(cycle_num, step_num)]["shipping_cost"] * count_sales
        )

    return float(shipping_cost)


def calculate_misc_cost(x, product_details, n_cycles, n_steps):
    misc_cost = 0

    # Iterate through cycles and steps dynamically
    cycle_num = x["cycle_num"]
    step_num = x["flow_step_number"]
    count_sales = x["sales"]

    # Iterate through cycles and steps dynamically
    if product_details:
        misc_cost = product_details[(cycle_num, step_num)]["misc_cost"] * count_sales

    return float(misc_cost)


def getProduct(x):
    """
    Calculate product cost based on sales data.
    This function is optimized to reduce database queries by using a global cache.
    """
    # Use a global cache to store product details
    # This cache is module-level and will persist between function calls
    # It will be garbage collected when the module is reloaded

    product_details = get_product_detail(x)
    # Calculate total product cost
    product_cost = calculate_product_cost(
        x=x, product_details=product_details, n_cycles=MAX_CYCLES, n_steps=MAX_STEPS
    )

    return product_cost


def get_product_cpa(x):
    product_details = get_product_detail(x)

    return calculate_cpa_cost(
        x=x, product_details=product_details, n_cycles=MAX_CYCLES, n_steps=MAX_STEPS
    )


def get_product_misc(x):
    product_details = get_product_detail(x)

    return calculate_misc_cost(
        x=x, product_details=product_details, n_cycles=MAX_CYCLES, n_steps=MAX_STEPS
    )


def get_product_shipping(x):
    product_details = get_product_detail(x)

    return calculate_shipping_cost(
        x=x, product_details=product_details, n_cycles=MAX_CYCLES, n_steps=MAX_STEPS
    )


def get_product_detail(x):
    if not hasattr(getProduct, "product_cache"):
        getProduct.product_cache = {}

    # Create a cache key based on flow brand and client
    cache_key = (x["flow_brand_name"], x["client"])
    product_details = {}

    # If we don't have this combination in the cache, fetch it
    if cache_key not in getProduct.product_cache:
        product = OfferProductDetail.objects.filter(
            offer_flow_step__flow__flow_brand_name=x["flow_brand_name"],
            offer_flow_step__flow__client__name=x["client"],
        )
        # Create a nested dictionary to store product details by cycle and step
        for p in product:
            cycle_step_key = (p.cycle_number, p.offer_flow_step.flow_step_number)
            product_details[cycle_step_key] = {
                "product_cost": p.product_cost or 0,
                "shipping_cost": p.shipping_cost or 0,
                "cpa_cost": p.cpa_cost,
                "misc_cost": p.misc_cost,
            }

        getProduct.product_cache[cache_key] = product_details

    # Get the cached product details
    product_details = getProduct.product_cache[cache_key]
    return product_details


def clear_product_cache():
    """
    Clear the product cache used by getProduct function.
    This should be called after processing is complete to free memory.
    """
    if hasattr(getProduct, "product_cache"):
        getProduct.product_cache.clear()


def get_unified_clv_data(
    request: HttpRequest, additional_value=None
) -> QuerySet[HomeCLVReport]:
    """
    Unified function to get CLV data for both CLV and Cash Flow views.

    Args:
        request: HTTP request containing filter parameters
        data_type: Type of data to return ("clv" or "cash_flow")
    """
    # Get common parameters
    show_by_date = request.GET.get("show_by_date")
    show_by_network = request.GET.get("show_by_network")
    show_by_pub = request.GET.get("show_by_pub")

    # Get date filters
    original_sale_start_date = request.GET.get("original_sale_start_date")
    original_sale_end_date = request.GET.get("original_sale_end_date")
    as_of_date_start_date = request.GET.get("as_of_date_start_date")
    as_of_date_end_date = request.GET.get("as_of_date_end_date")

    # Build base queryset
    queryset = HomeCLVReport.objects.select_related("flow_brand", "flow_step").filter(
        flow_brand_id__isnull=False,
        ancestor_date__isnull=False,
        flow_brand__status__in=["Active", "Pending"],
    )

    # Apply common filters
    filters = get_filter_from_request(request)
    queryset = queryset.filter(filters)

    # Apply date filters
    if original_sale_start_date:
        queryset = queryset.filter(
            ancestor_date__gte=parse_date(original_sale_start_date)
        )
    if original_sale_end_date:
        queryset = queryset.filter(
            ancestor_date__lte=parse_date(original_sale_end_date)
        )
    if as_of_date_start_date:
        queryset = queryset.filter(
            transaction_date__gte=parse_date(as_of_date_start_date)
        )
    if as_of_date_end_date:
        queryset = queryset.filter(
            transaction_date__lte=parse_date(as_of_date_end_date)
        )

    # Add common annotations
    queryset = queryset.annotate(
        sales=Coalesce(Sum("total_unique_sale"), Value(0), output_field=IntegerField()),
        flow_brand_name=F("flow_brand__flow_brand_name"),
        flow_step_number=F("flow_step__flow_step_number"),
    ).filter(sales__gt=0)

    metrics = calculate_clv_show_metrics(
        queryset,
        group_by="day" if show_by_date == "1" else "month",
        show_by_network=show_by_network,
        show_by_pub=show_by_pub,
        additional_value=additional_value,
    )

    return metrics


def make_unified_metrics(
    metrics: QuerySet,
    custom_fees: float,
    show_by_date: str,
    show_by_network: str,
    show_by_pub: str,
    data_type: str = "clv",
) -> Tuple[List[dict], dict]:
    """
    Unified function to process metrics for both CLV and Cash Flow views.
    """
    if not metrics:
        return [], {}

    # Convert QuerySet to DataFrame
    df = pd.DataFrame(list(metrics))

    # Ensure trans_date is properly formatted
    if "trans_date" in df.columns:
        df["trans_date"] = pd.to_datetime(df["trans_date"], errors="coerce")
    elif "as_of_date" in df.columns:
        df["trans_date"] = pd.to_datetime(df["as_of_date"], errors="coerce")
    else:
        df["trans_date"] = pd.Series([pd.NaT] * len(df), dtype="datetime64[ns]")

    df["trans_date"] = df["trans_date"].dt.strftime("%Y-%m").fillna("")

    # Create derived columns
    df["client"] = df["clientID"].apply(
        lambda x: ClientsInformation.objects.get(clientID=x).name
    )
    df["sale"] = df["sales"]

    # Handle date fields based on data type
    if data_type == "cash_flow":
        # For cash flow, use trans_date for both month and date
        df["month"] = df["trans_date"]
        df["date"] = ""
    else:
        # For CLV, use the original date handling
        df["month"] = df["date"].dt.strftime("%Y-%m")
        df["date"] = df["date"].dt.strftime("%d") if show_by_date == "1" else ""

    # Calculate financial metrics
    df["voided_revenue"] = -abs(df["voided_revenue"].fillna(0))
    df["refuded_revenue"] = -abs(df["refuded_revenue"].fillna(0))
    df["cb_revenue"] = -abs(df["cb_revenue"].fillna(0))
    df["cb_fees"] = np.where(df["cb_count"] > 0, df["cb_count"] * -35, 0)
    df["processing_fees"] = np.where(df["revenue"] > 0, df["revenue"] * -0.04, 0)
    df["call_center_fees"] = np.where(df["sales"] > 0, df["sales"] * -1.5, 0)
    df["custom_fee_calc"] = np.where(
        df["revenue"] > 0, df["revenue"] * (-1 * custom_fees / 100), 0
    )

    # Calculate costs
    df["product_cost"] = -df.apply(getProduct, axis=1)
    df["misc_cost"] = -df.apply(get_product_misc, axis=1)

    df["cpa_cost"] = -df.apply(get_product_cpa, axis=1)
    df["CPA"] = df["cpa_cost"]
    df["shipping_cost"] = -df.apply(get_product_shipping, axis=1)

    # Calculate percentages
    df["refuded_voids_percentage"] = np.where(
        df["revenue"] > 0,
        ((df["refuded_revenue"] + df["voided_revenue"]) / df["revenue"]) * 100,
        0,
    )
    df["cb_percentage"] = np.where(
        df["revenue"] > 0, (df["cb_revenue"] / df["revenue"]) * 100, 0
    )

    # Fill NaN values
    df.fillna(0, inplace=True)

    # Calculate cash flows
    df["cash_inflows"] = df["revenue"]
    df["cash_outflows"] = (
        df["CPA"]
        + df["refuded_revenue"]
        + df["voided_revenue"]
        + df["cb_revenue"]
        + df["cb_fees"]
        + df["processing_fees"]
        + df["call_center_fees"]
        + df["custom_fee_calc"]
        + df["product_cost"]
        + df["misc_cost"]
        + df["shipping_cost"]
    )

    # Calculate net profit and CLV
    df["net_profit"] = df["cash_inflows"] + df["cash_outflows"]
    df["clv"] = df.apply(
        lambda x: x["net_profit"] / x["sales"] if x["sales"] > 0 else 0, axis=1
    )
    df["net_profit_per_sales"] = df.apply(
        lambda x: x["net_profit"] / x["sales"] if x["sales"] > 0 else 0, axis=1
    )

    # Add display settings
    df["show_by_date"] = show_by_date
    df["show_by_network"] = show_by_network
    df["show_by_pub"] = show_by_pub

    # Define grouping columns based on data type
    if data_type == "cash_flow":
        # For cash flow, group by trans_date and flow_brand_name
        grouping_cols = ["trans_date", "flow_brand_name", "client"]
        if show_by_network == "1":
            grouping_cols.append("network")
        if show_by_pub == "1":
            grouping_cols.append("pub")
    else:
        # For CLV, use the original grouping logic
        grouping_cols = ["clientID", "flow_brand_name", "month", "date", "as_of_date"]
        if show_by_network == "1":
            grouping_cols.append("network")
        if show_by_pub == "1":
            grouping_cols.append("pub")

    # Define numeric columns to sum
    numeric_cols = [
        "sale",
        "sales",
        "revenue",
        "CPA",
        "refuded_revenue",
        "voided_revenue",
        "cb_revenue",
        "cb_fees",
        "processing_fees",
        "call_center_fees",
        "product_cost",
        "misc_cost",
        "shipping_cost",
        "custom_fee_calc",
        "net_profit",
        "cash_inflows",
        "cash_outflows",
    ]

    # Group the data
    grouped_df = df.groupby(grouping_cols, as_index=False)[numeric_cols].sum()

    # Recalculate derived metrics after grouping
    grouped_df["clv"] = grouped_df.apply(
        lambda x: x["net_profit"] / x["sales"] if x["sales"] > 0 else 0, axis=1
    )
    grouped_df["net_profit_per_sales"] = grouped_df.apply(
        lambda x: x["net_profit"] / x["sales"] if x["sales"] > 0 else 0, axis=1
    )

    # Recalculate percentages after grouping
    grouped_df["refuded_voids_percentage"] = np.where(
        grouped_df["revenue"] > 0,
        (
            (grouped_df["refuded_revenue"] + grouped_df["voided_revenue"])
            / grouped_df["revenue"]
        )
        * 100,
        0,
    )
    grouped_df["cb_percentage"] = np.where(
        grouped_df["revenue"] > 0,
        (grouped_df["cb_revenue"] / grouped_df["revenue"]) * 100,
        0,
    )

    # Add display settings to grouped data
    grouped_df["show_by_date"] = show_by_date
    grouped_df["show_by_network"] = show_by_network
    grouped_df["show_by_pub"] = show_by_pub

    # Add month and date fields to grouped data
    if data_type == "cash_flow":
        grouped_df["month"] = grouped_df["trans_date"]
        grouped_df["date"] = ""
    else:
        grouped_df["month"] = grouped_df["month"]
        grouped_df["date"] = grouped_df["date"]

    # Add client field to grouped data
    if data_type == "cash_flow":
        grouped_df["client"] = grouped_df["client"]
    else:
        grouped_df["client"] = grouped_df["clientID"].apply(
            lambda x: ClientsInformation.objects.get(clientID=x).name
        )

    # Select output fields based on data type
    if data_type == "cash_flow":
        output_fields = [
            "flow_brand_name",
            "client",
            "sales",
            "sale",
            "month",
            "clv",
            "date",
            "trans_date",
            "net_profit",
            "net_profit_per_sales",
            "cash_inflows",
            "cash_outflows",
            "show_by_date",
            "show_by_network",
            "show_by_pub",
        ]
    else:
        output_fields = [
            "flow_brand_name",
            "client",
            "sales",
            "sale",
            "month",
            "clv",
            "date",
            "revenue",
            "CPA",
            "refuded_revenue",
            "voided_revenue",
            "refuded_voids_percentage",
            "cb_revenue",
            "cb_percentage",
            "cb_fees",
            "processing_fees",
            "call_center_fees",
            "product_cost",
            "misc_cost",
            "shipping_cost",
            "custom_fee_calc",
            "net_profit",
            "show_by_date",
            "show_by_network",
            "show_by_pub",
        ]

    # Add network and pub fields if needed
    if show_by_network == "1":
        output_fields.insert(0, "network")
    if show_by_pub == "1":
        output_fields.insert(0, "pub")

    # Prepare final output
    output = grouped_df[output_fields].round(decimals=2)
    output["id"] = output.index + 1
    output_list = output.to_dict(orient="records")

    # Calculate totals
    if data_type == "cash_flow":
        # For cash flow, total should be grouped by trans_date
        total_df = df.groupby(["trans_date"], as_index=False)[numeric_cols].sum()
        total_df["clv"] = total_df.apply(
            lambda x: x["net_profit"] / x["sales"] if x["sales"] > 0 else 0, axis=1
        )
        total_df["net_profit_per_sales"] = total_df.apply(
            lambda x: x["net_profit"] / x["sales"] if x["sales"] > 0 else 0, axis=1
        )
        total_df["show_by_date"] = show_by_date
        total_df["show_by_network"] = show_by_network
        total_df["show_by_pub"] = show_by_pub
        total_df["month"] = total_df["trans_date"]
        total_df["date"] = ""
        total_dict = total_df.to_dict(orient="records")
    else:
        # For CLV, use overall totals
        total_dict = df[numeric_cols].sum().to_dict()
        total_dict["clv"] = (
            total_dict["net_profit"] / total_dict["sales"]
            if total_dict["sales"] > 0
            else 0
        )
        total_dict["net_profit_per_sales"] = (
            total_dict["net_profit"] / total_dict["sales"]
            if total_dict["sales"] > 0
            else 0
        )
        total_dict["refuded_voids_percentage"] = (
            (
                (total_dict["refuded_revenue"] + total_dict["voided_revenue"])
                / total_dict["revenue"]
            )
            * 100
            if total_dict["revenue"] > 0
            else 0
        )
        total_dict["cb_percentage"] = (
            (total_dict["cb_revenue"] / total_dict["revenue"]) * 100
            if total_dict["revenue"] > 0
            else 0
        )

    # Clear cache
    clear_product_cache()

    return output_list, total_dict


@extend_schema(
    parameters=[
        OpenApiParameter(name="clientID[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(
            name="flow_brand_name[]", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="card_brand[]", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(name="network[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="pub[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(
            name="original_sale_start_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="original_sale_end_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="as_of_date_start_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="as_of_date_end_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="sales_start_number", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="sales_end_number", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="custom_fees", required=False, allow_blank=True, type=float
        ),
        OpenApiParameter(
            name="show_by_date", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="show_by_network", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(name="show_by_pub", required=True, allow_blank=True, type=str),
        OpenApiParameter(name="page", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="limit", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="sortByVal", required=False, type=str, enum=[]),
        OpenApiParameter(
            name="orderByVal", required=False, type=str, enum=["ASC", "DESC"]
        ),
    ],
    responses={
        HTTP_200_OK: getCLVResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Data Overall",
    tags=["Customer Lifetime Value"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getCLV(request: HttpRequest) -> JsonResponse:
    try:
        custom_fees = float(request.GET.get("custom_fees", 7))
        show_by_date = request.GET.get("show_by_date")
        show_by_network = request.GET.get("show_by_network")
        show_by_pub = request.GET.get("show_by_pub")

        metrics = get_unified_clv_data(request)
        output, total = make_unified_metrics(
            metrics, custom_fees, show_by_date, show_by_network, show_by_pub, "clv"
        )

        sortByVal = request.GET.get("sortByVal", None)
        orderByVal = request.GET.get("orderByVal", "DESC")
        page = int(request.GET.get("page", 1))
        limit = int(request.GET.get("limit", 100))

        if sortByVal:
            output = sorted(
                output, key=lambda x: x[sortByVal], reverse=orderByVal == "DESC"
            )

        return JsonResponse(
            {
                "message": "Get Customer Lifetime Value successfully.",
                "default_view": {
                    "show_by_date": True if show_by_date == "1" else False,
                    "show_by_network": True if show_by_network == "1" else False,
                    "show_by_pub": True if show_by_pub == "1" else False,
                },
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total_page": len(output) // int(limit) + 1,
                    "total_item": len(output),
                },
                "total": total,
                "data": output[(page - 1) * limit : page * limit],
            },
            status=200,
        )
    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)}, status=400
        )


@extend_schema(
    parameters=[
        OpenApiParameter(name="clientID[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(
            name="flow_brand_name[]", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="card_brand[]", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(name="network[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="pub[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(
            name="original_sale_start_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="original_sale_end_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="as_of_date_start_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="as_of_date_end_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="sales_start_number", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="sales_end_number", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="custom_fees", required=False, allow_blank=True, type=float
        ),
        OpenApiParameter(
            name="show_by_date", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="show_by_network", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(name="show_by_pub", required=True, allow_blank=True, type=str),
        OpenApiParameter(name="page", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="limit", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="sortByVal", required=False, type=str, enum=[]),
        OpenApiParameter(
            name="orderByVal", required=False, type=str, enum=["ASC", "DESC"]
        ),
    ],
    responses={
        HTTP_200_OK: getCLVResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Data Overall",
    tags=["CLV Cash Flow"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getCashFlow(request: HttpRequest) -> JsonResponse:
    try:
        custom_fees = float(request.GET.get("custom_fees", 7))
        show_by_date = request.GET.get("show_by_date")
        show_by_network = request.GET.get("show_by_network")
        show_by_pub = request.GET.get("show_by_pub")

        metrics = get_unified_clv_data(request=request)
        output, total = make_unified_metrics(
            metrics,
            custom_fees,
            show_by_date,
            show_by_network,
            show_by_pub,
            "cash_flow",
        )

        sortByVal = request.GET.get("sortByVal", None)
        orderByVal = request.GET.get("orderByVal", "DESC")
        page = int(request.GET.get("page", 1))
        limit = int(request.GET.get("limit", 100))

        if sortByVal:
            output = sorted(
                output, key=lambda x: x[sortByVal], reverse=orderByVal == "DESC"
            )

        return JsonResponse(
            {
                "message": "Get Customer Lifetime Value successfully.",
                "default_view": {
                    "show_by_date": True if show_by_date == "1" else False,
                    "show_by_network": True if show_by_network == "1" else False,
                    "show_by_pub": True if show_by_pub == "1" else False,
                },
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total_page": len(output) // int(limit) + 1,
                    "total_item": len(output),
                },
                "total": total,
                "data": output[(page - 1) * limit : page * limit],
            },
            status=200,
        )
    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)}, status=400
        )


@extend_schema(
    parameters=[
        OpenApiParameter(
            name="card_brand[]", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(name="network[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="pub[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="clientID[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(
            name="original_sale_start_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="original_sale_end_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="as_of_date_start_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="as_of_date_end_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="sales_start_number", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="sales_end_number", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="flow_brand_name", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="month_of_flow_brand", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="custom_fees", required=False, allow_blank=True, type=float
        ),
        OpenApiParameter(
            name="show_by_date", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="show_by_network", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(name="show_by_pub", required=True, allow_blank=True, type=str),
        OpenApiParameter(
            name="date_choice", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="network_choice", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(name="pub_choice", required=False, allow_blank=True, type=str),
    ],
    responses={
        200: getClvToolTipsResponseSerializer,
        400: GetDataUnsuccessfulResponseSerializer,
        405: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Data Overview",
    tags=["Customer Lifetime Value"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getClvToolTips(request: HttpRequest) -> JsonResponse:
    if request.method != "GET":
        return JsonResponse(
            {"message": "Invalid request method"}, status=HTTP_405_METHOD_NOT_ALLOWED
        )
    try:
        flow_brand_name = request.GET.get("flow_brand_name")
        network_choice = request.GET.get("network_choice")
        pub_choice = request.GET.get("pub_choice")
        date_choice = request.GET.get("date_choice")

        month_of_flow_brand = request.GET.get("month_of_flow_brand")
        custom_fees = float(request.GET.get("custom_fees", 7))

        request.GET.get("show_by_date")
        show_by_network = request.GET.get("show_by_network")
        show_by_pub = request.GET.get("show_by_pub")

        metrics = get_clv_data(request, type_of_data="overview")

        month_date = datetime.strptime(month_of_flow_brand, "%Y-%m")
        start_date = month_date.replace(day=1)
        end_date = (start_date + relativedelta(months=1)) - timedelta(days=1)

        if network_choice:
            metrics = metrics.filter(network=network_choice)
        if pub_choice:
            metrics = metrics.filter(pub=pub_choice)
        if date_choice:
            start_date_choice = datetime.strptime(
                f"{month_of_flow_brand}-{date_choice}", "%Y-%m-%d"
            ).replace(hour=0, minute=0, second=0)
            end_date_choice = (
                start_date_choice + timedelta(days=1) - timedelta(seconds=1)
            )
            metrics = metrics.filter(
                date__gte=start_date_choice, date__lte=end_date_choice
            )

        metrics = metrics.filter(
            flow_brand_name=flow_brand_name,
            ancestor_date__gte=start_date,
            ancestor_date__lte=end_date,
        )

        chart_data, total = make_unified_metrics(
            metrics, custom_fees, "1", show_by_network, show_by_pub, "clv"
        )
        total_sales = total["sales"]
        total_net_profit = total["net_profit"]

        # Format the final response
        response_data = {
            "flow_brand_name": flow_brand_name,
            "month_of_original_sale_date": month_of_flow_brand,
            "total_sales": total_sales,
            "total_clv": round(total_net_profit / total_sales, 2)
            if total_sales > 0
            else 0,
            "data": chart_data,
        }

        return JsonResponse(
            {
                "message": "Get Customer Lifetime Value Overview Data successfully.",
                "data": response_data,
            },
            status=200,
        )
    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)}, status=400
        )


@extend_schema(
    parameters=[
        OpenApiParameter(
            name="show_by_date",
            description="Show by date of month",
            required=True,
            type=str,
        ),
        OpenApiParameter(
            name="show_by_network",
            description="Show By Network",
            required=True,
            type=str,
        ),
        OpenApiParameter(
            name="show_by_pub", description="Show By Pub", required=True, type=str
        ),
        OpenApiParameter(
            name="showing_filters[]",
            description="Is Show Filter",
            required=True,
            type=str,
        ),
        OpenApiParameter(
            name="lst_flow_brand_name[]",
            description="Flow Title",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="clientID[]", description="ClientID", required=False, type=str
        ),
    ],
    operation_id="Filter Meta Data",
    tags=["Customer Lifetime Value"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getClvFilterMetaData(request: HttpRequest) -> JsonResponse:
    if request.method != "GET":
        return JsonResponse(
            {"message": "Invalid request method"}, status=HTTP_405_METHOD_NOT_ALLOWED
        )
    try:
        showing_filters = request.GET.getlist(
            "showing_filters[]", ["original_sale", "as_of_date", "sales", "custom_fees"]
        )
        lst_flow_brand_name = request.GET.getlist("flow_brand_name[]", None)
        lst_clientID = request.GET.getlist("clientID[]", None)

        original_sale_start_date = request.GET.get("original_sale_start_date", None)
        original_sale_end_date = request.GET.get("original_sale_end_date", None)
        as_of_date_start_date = request.GET.get("as_of_date_start_date", None)
        as_of_date_end_date = request.GET.get("as_of_date_end_date", None)

        origin_sales_start_number = request.GET.get("origin_sales_start_number", None)
        origin_sales_end_number = request.GET.get("origin_sales_end_number", None)

        lst_clients, permissions = getClientView(request)
        if "SUPER-PARENT" in permissions.split("_"):
            lst_clientID = [lst_clients[0]["clientID"]]

        base_filters = Q(
            flow_brand_id__isnull=False,
            ancestor_date__isnull=False,
            flow_brand__status__in=["Active", "Pending"],
        )
        if lst_clientID:
            base_filters &= Q(clientID__in=lst_clientID)

        FLOFIN_ALL_DATA = (
            HomeCLVReport.objects.select_related("flow_brand", "flow_step")
            .filter(base_filters)
            .annotate(
                flow_brand_name=F("flow_brand__flow_brand_name"),
                flow_step_number=F("flow_step__flow_step_number"),
                sales=Coalesce(
                    Sum("total_unique_sale"), Value(0), output_field=IntegerField()
                ),
            )
            .filter(sales__gt=0)
        )

        flow_brand_names = (
            FLOFIN_ALL_DATA.order_by("flow_brand_name")
            .values_list("flow_brand_name", flat=True)
            .distinct()
        )

        if lst_flow_brand_name:
            FLOFIN_ALL_DATA = FLOFIN_ALL_DATA.filter(
                flow_brand_name__in=lst_flow_brand_name
            )

        card_brands = (
            FLOFIN_ALL_DATA.exclude(card_brand=None)
            .order_by("card_brand")
            .values_list("card_brand", flat=True)
            .distinct()
        )
        pubs = (
            FLOFIN_ALL_DATA.exclude(pub=None)
            .order_by("pub")
            .values_list("pub", flat=True)
            .distinct()
        )

        metrics = get_clv_data(request)

        networks = (
            metrics.exclude(network=None)
            .order_by("network")
            .values_list("network", flat=True)
            .distinct()
        )

        overall_max_min = metrics.aggregate(
            max_total=Max("sales"), min_total=Min("sales")
        )

        if not origin_sales_start_number:
            origin_sales_start_number = (
                float(overall_max_min["min_total"])
                if overall_max_min["min_total"]
                else 0
            )
        if not origin_sales_end_number:
            origin_sales_end_number = (
                float(overall_max_min["max_total"])
                if overall_max_min["max_total"]
                else 0
            )

        return JsonResponse(
            {
                "message": "Get Customer Lifetime Value Filter Value successfully.",
                "data": [
                    {
                        "label": "Flow Title",
                        "key": "flow_brand_name",
                        "isShow": False
                        if "flow_brand_name" not in showing_filters
                        else True,
                        "type": "SELECT_MULTI",
                        "metaData": [
                            {"label": brand_name, "value": brand_name}
                            for brand_name in flow_brand_names
                        ],
                    },
                    {
                        "label": "Card Brand",
                        "key": "card_brand",
                        "isShow": False
                        if "card_brand" not in showing_filters
                        else True,
                        "type": "SELECT_MULTI",
                        "metaData": [
                            {"label": card, "value": card} for card in card_brands
                        ],
                    },
                    {
                        "label": "Network",
                        "key": "network",
                        "isShow": False if "network" not in showing_filters else True,
                        "type": "SELECT_MULTI",
                        "metaData": [
                            {"label": network, "value": network} for network in networks
                        ],
                    },
                    {
                        "label": "Affiliate",
                        "key": "pub",
                        "isShow": False if "pub" not in showing_filters else True,
                        "type": "SELECT_MULTI",
                        "metaData": [{"label": pub, "value": pub} for pub in pubs],
                    },
                    {
                        "label": "Original Sale Date",
                        "key": "original_sale",
                        "isShow": False
                        if "original_sale" not in showing_filters
                        else True,
                        "type": "DATE_RANGE",
                        "metaData": {
                            "start_date": date.today() - timedelta(days=90)
                            if not original_sale_start_date
                            else parse_date(original_sale_start_date),
                            "end_date": date.today()
                            if not original_sale_end_date
                            else parse_date(original_sale_end_date),
                            "list_date": None,
                        },
                    },
                    {
                        "label": "As of Date",
                        "key": "as_of_date",
                        "isShow": False
                        if "as_of_date" not in showing_filters
                        else True,
                        "type": "DATE_RANGE",
                        "metaData": {
                            "start_date": date.today() - timedelta(days=90)
                            if not as_of_date_start_date
                            else parse_date(as_of_date_start_date),
                            "end_date": date.today()
                            if not as_of_date_end_date
                            else parse_date(as_of_date_end_date),
                            "list_date": None,
                        },
                    },
                    {
                        "label": "Sales",
                        "key": "sales",
                        "isShow": False if "sales" not in showing_filters else True,
                        "type": "NUMBER_RANGE",
                        "metaData": {
                            "start_number": int(origin_sales_start_number),
                            "end_number": int(origin_sales_end_number),
                        },
                    },
                    {
                        "label": "Custom Fees",
                        "key": "custom_fees",
                        "isShow": False
                        if "custom_fees" not in showing_filters
                        else True,
                        "type": "INPUT_PERCENT",
                        "metaData": None,
                    },
                ],
            }
        )
    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)}, status=400
        )


@extend_schema(
    parameters=[
        OpenApiParameter(name="clientID[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(
            name="flow_brand_name[]", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="card_brand[]", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(name="network[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="pub[]", required=False, allow_blank=True, type=str),
        OpenApiParameter(
            name="original_sale_start_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="original_sale_end_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="as_of_date_start_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="as_of_date_end_date", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="sales_start_number", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="sales_end_number", required=False, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="custom_fees", required=False, allow_blank=True, type=float
        ),
        OpenApiParameter(
            name="show_by_date", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(
            name="show_by_network", required=True, allow_blank=True, type=str
        ),
        OpenApiParameter(name="show_by_pub", required=True, allow_blank=True, type=str),
        OpenApiParameter(name="page", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="limit", required=False, allow_blank=True, type=str),
        OpenApiParameter(name="sortByVal", required=False, type=str, enum=[]),
        OpenApiParameter(
            name="orderByVal", required=False, type=str, enum=["ASC", "DESC"]
        ),
    ],
    responses={
        HTTP_200_OK: getCLVResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Data Overall",
    tags=["Customer Lifetime Value"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def downloadCLV(request: HttpRequest) -> HttpResponse:
    if request.method != "GET":
        return JsonResponse(
            {"message": "Invalid request method"}, status=HTTP_405_METHOD_NOT_ALLOWED
        )
    try:
        custom_fees = float(
            request.GET.get("custom_fees", 7)
        )  # Default to 7% if not provided
        show_by_date = request.GET.get("show_by_date")
        show_by_network = request.GET.get("show_by_network")
        show_by_pub = request.GET.get("show_by_pub")

        metrics = get_clv_data(request)
        data, _ = make_unified_metrics(
            metrics, custom_fees, show_by_date, show_by_network, show_by_pub, "clv"
        )

        header_to_key = {
            "Client": "client",
            "Flow Title": "flow_brand_name",
            "Network": "network",
            "Affiliate": "pub",
            "Month": "month",
            "Date": "date",
            "Sale": "sale",
            "Revenue": "revenue",
            "CPA": "CPA",
            "Refunded Revenue": "refuded_revenue",
            "Voided Revenue": "voided_revenue",
            "Refunds +Voids%": "refuded_voids_percentage",
            "CB Revenue": "cb_revenue",
            "CB %": "cb_percentage",
            "CB Fees": "cb_fees",
            "Processing Fees": "processing_fees",
            "Call Center Fees": "call_center_fees",
            "Product Cost": "product_cost",
            "Misc Cost": "misc_cost",
            "Shipping Cost": "shipping_cost",
            "Custom Fee Calculation": "custom_fee_calc",
            "Net Profit": "net_profit",
            "CLV": "clv",
        }
        lst_clients, permissions = getClientView(request)
        excel_file = StringIO()
        csv_writer = csv.writer(excel_file)
        if len(data) > 0:
            header_keys = list(header_to_key.keys())
            csv_writer.writerow(header_keys)
            for row in data:
                if "SUPER-PARENT" in permissions.split("_"):
                    row.pop("clientID", None)
                    row.pop("client", None)
                csv_writer.writerow(
                    [row.get(header_to_key[key], "") for key in header_keys]
                )

        else:
            return JsonResponse({"message": "No data found"}, status=HTTP_200_OK)

        data = excel_file.getvalue()

        response = HttpResponse(excel_file.getvalue(), content_type="text/csv")
        response["Content-Type"] = "application/octet-stream"
        response["Content-Disposition"] = 'attachment;filename="temp_file.csv"'
        excel_file.close()

        return response

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)}, status=400
        )
