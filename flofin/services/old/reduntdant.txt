class reportingHubReportRun:
    def __init__(
        self,
        start_date=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
        end_date=datetime.now().strftime("%Y-%m-%d"),
        client_login: ClientLoginInformation = None,
    ):

        self.LIST_ISSUERS = [
            "CITIBANK N.A.",
            "BANK OF AMERICA, N.A.",
            "CHASE BANK USA, N.A.",
            "BANK OF AMERICA, NATIONAL ASSOCIATION",
            "CITIBANK, N.A.",
            "FIFTH THIRD BANK, THE",
            "BANCORP BANK, THE",
            "BBVA USA",
            "BANK OF MISSOURI, THE",
            "CAPITAL ONE BANK (USA), NATIONAL ASSOCIATION",
        ]

        self.ANNOTATIONS_UPDATE = {
            "chargebacks": Count("order_update_type", filter=Q(order_update_type=3)),
            "refunds": Count("order_update_type", filter=Q(order_update_type=1)),
            "voids": Count("order_update_type", filter=Q(order_update_type=2)),
            "chargebacks_amount": Sum(
                Case(
                    When(order_update_type=3, then=F("order_import__price_point")),
                    default=0,
                    output_field=DecimalField(),
                )
            ),
            "refunds_amount": Sum(
                Case(
                    When(order_update_type=1, then=F("order_import__price_point")),
                    default=0,
                    output_field=DecimalField(),
                )
            ),
            "voids_amount": Sum(
                Case(
                    When(order_update_type=2, then=F("order_import__price_point")),
                    default=0,
                    output_field=DecimalField(),
                )
            ),
        }

        self.ANNOTATIONS_IMPORT = {
            "transactions": Sum("order_status"),
            "transactions_amount": Sum("price_point"),
        }

        self.import_fields = [
            "client_login__listClientsUsed",
            "report_date",
            "is_3ds_verified",
        ]
        self.update_fields = [
            "client_login__listClientsUsed",
            "report_date",
            "order_update_type",
        ]

        self.start_date = datetime.strptime(start_date, "%Y-%m-%d")
        self.end_date = datetime.strptime(end_date, "%Y-%m-%d")
        self.client_login = client_login

    def generateReportData(self, i, report_type):
        if "is_3ds_verified" in i:
            return {
                "clientID": i["client_login__listClientsUsed"],
                "report_date": i["report_date"],
                "is_3ds": i["is_3ds_verified"],
                "type_report": report_type,
                "defaults": {
                    "transactions": i.get("transactions", None),
                    "transactions_amount": i.get("transactions_amount", None),
                    "chargebacks": i.get("chargebacks", None),
                    "refunds": i.get("refunds", None),
                    "voids": i.get("voids", None),
                    "chargebacks_amount": i.get("chargebacks_amount", None),
                    "refunds_amount": i.get("refunds_amount", None),
                    "voids_amount": i.get("voids_amount", None),
                },
            }
        return {
            "clientID": i["client_login__listClientsUsed"],
            "report_date": i["report_date"],
            "type_report": report_type,
            "defaults": {
                "chargebacks": i.get("chargebacks", None),
                "refunds": i.get("refunds", None),
                "voids": i.get("voids", None),
                "chargebacks_amount": i.get("chargebacks_amount", None),
                "refunds_amount": i.get("refunds_amount", None),
                "voids_amount": i.get("voids_amount", None),
            },
        }

    def processReport(self, data, key_name, report_type, check_is_3ds=False, new_fields=None):
        update_objects = []
        create_objects = []
        existing_records = set()

        # Prepare a batch of records for updates or creation
        for i in data:
            report_data = self.generateReportData(i, report_type)

            if key_name == "total":
                filters = {
                    "clientID": report_data["clientID"],
                    "report_date": report_data["report_date"],
                }
            elif check_is_3ds:
                filters = {
                    "clientID": report_data["clientID"],
                    "report_date": report_data["report_date"],
                    "is_3ds": report_data["is_3ds"],
                    key_name: i[new_fields],
                    "type_report": report_data["type_report"],
                }
            else:
                filters = {
                    "clientID": report_data["clientID"],
                    "report_date": report_data["report_date"],
                    key_name: i[new_fields],
                    "type_report": report_data["type_report"],
                }

            # Check if the record already exists in the database
            key_tuple = tuple(filters.items())
            if key_tuple not in existing_records:
                obj, created = reportByMid.objects.filter(**filters).first(), False
                if obj is None:
                    obj = reportByMid(**filters, **report_data["defaults"])
                    create_objects.append(obj)
                    created = True
                if not created:
                    for field, value in report_data["defaults"].items():
                        setattr(obj, field, value)
                    update_objects.append(obj)

                existing_records.add(key_tuple)

        # Use bulk_create for new objects
        if create_objects:
            reportByMid.objects.bulk_create(create_objects)

        # Use bulk_update for existing objects that need updating
        if update_objects:
            fields_to_update = list(report_data["defaults"].keys())
            reportByMid.objects.bulk_update(update_objects, fields_to_update)

    def generate_order_imports(self):
        return OrderImport.objects.filter(
            order_status=1,
            transaction_date__gt="2024-01-01 00:00:00",
            is_test=False,
            price_point__gt=0,
            offer_product__offer_flow_step__flow__flow_brand_name__isnull=False,
        ).annotate(report_date=TruncDay("transaction_date"))

    def generate_order_updates(self):
        return OrderUpdate.objects.filter(
            update_status=1,
            order_import__isnull=False,
            order_import__order_status=1,
            order_import__transaction_date__gt="2024-01-01 00:00:00",
            order_import__is_test=False,
            order_import__price_point__gt=0,
            order_import__offer_product__offer_flow_step__flow__flow_brand_name__isnull=False,
        ).annotate(report_date=TruncDay("update_date"))

    # -------------------------------------------------------------------------------------------------------------------

    def getIssuerReports(self):
        order_imports_by_issuer = self.order_imports.values(*self.import_fields, "card_infor__card_issuer_name").annotate(
            card_issuer=Case(
                When(
                    card_infor__card_issuer_name__in=self.LIST_ISSUERS,
                    then=F("card_infor__card_issuer_name"),
                ),
                default=Value("Longtail"),
            ),
            **self.ANNOTATIONS_IMPORT,
        )

        order_updates_by_issuer = self.order_updates.values(*self.update_fields, "order_import__card_infor__card_issuer_name").annotate(
            card_issuer=Case(
                When(
                    order_import__card_infor__card_issuer_name__in=self.LIST_ISSUERS,
                    then=F("order_import__card_infor__card_issuer_name"),
                ),
                default=Value("Longtail"),
            ),
            **self.ANNOTATIONS_UPDATE,
        )

        print(f" ----> Start Issuer Reports, {order_imports_by_issuer.count()} - {order_updates_by_issuer.count()}")
        self.processReport(
            order_imports_by_issuer,
            "card_issuer",
            report_type=1,
            check_is_3ds=True,
            new_fields="card_issuer",
        )
        self.processReport(
            order_updates_by_issuer,
            "card_issuer",
            report_type=1,
            check_is_3ds=False,
            new_fields="card_issuer",
        )

    def getBrandReport(self):
        order_imports_by_flow_brand_name = self.order_imports.values(*self.import_fields, "offer_product__offer_flow_step__flow__flow_brand_name").annotate(
            flow_brand_name=F("offer_product__offer_flow_step__flow__flow_brand_name"),
            **self.ANNOTATIONS_IMPORT,
        )

        order_updates_by_flow_brand_name = self.order_updates.values(
            *self.update_fields,
            "order_import__offer_product__offer_flow_step__flow__flow_brand_name",
        ).annotate(
            flow_brand_name=F("order_import__offer_product__offer_flow_step__flow__flow_brand_name"),
            **self.ANNOTATIONS_UPDATE,
        )

        print(f" ----> Start Brand Reports, {order_imports_by_flow_brand_name.count()} - {order_updates_by_flow_brand_name.count()}")
        self.processReport(
            order_imports_by_flow_brand_name,
            "flow_brand_name",
            report_type=2,
            check_is_3ds=True,
            new_fields="flow_brand_name",
        )
        self.processReport(
            order_updates_by_flow_brand_name,
            "flow_brand_name",
            report_type=2,
            check_is_3ds=False,
            new_fields="flow_brand_name",
        )

    def getFlowStepNumberReport(self):
        order_imports_by_flow_step_number = self.order_imports.values(*self.import_fields, "offer_product__offer_flow_step__flow_step_number").annotate(
            flow_step_number=F("offer_product__offer_flow_step__flow_step_number"),
            **self.ANNOTATIONS_IMPORT,
        )

        order_updates_by_flow_step_number = self.order_updates.values(*self.update_fields, "order_import__offer_product__offer_flow_step__flow_step_number",).annotate(
            flow_step_number=F("order_import__offer_product__offer_flow_step__flow_step_number"),
            **self.ANNOTATIONS_UPDATE,
        )

        print(f" ----> Start Flow Step Reports, {order_imports_by_flow_step_number.count()} - {order_updates_by_flow_step_number.count()}")
        self.processReport(
            order_imports_by_flow_step_number,
            "flow_step_number",
            report_type=3,
            check_is_3ds=True,
            new_fields="flow_step_number",
        )
        self.processReport(
            order_updates_by_flow_step_number,
            "flow_step_number",
            report_type=3,
            check_is_3ds=False,
            new_fields="flow_step_number",
        )

    def getCardBrandReport(self):
        order_imports_by_card_brand = self.order_imports.values(*self.import_fields, "card_infor__card_brand").annotate(
            card_brand=F("card_infor__card_brand"), **self.ANNOTATIONS_IMPORT
        )

        order_updates_by_card_brand = self.order_updates.values(*self.update_fields, "order_import__card_infor__card_brand").annotate(
            card_brand=F("order_import__card_infor__card_brand"), **self.ANNOTATIONS_UPDATE
        )

        print(f" ----> Start Card Brand Reports, {order_imports_by_card_brand.count()} - {order_updates_by_card_brand.count()}")
        self.processReport(
            order_imports_by_card_brand,
            "card_brand",
            report_type=4,
            check_is_3ds=True,
            new_fields="card_brand",
        )
        self.processReport(
            order_updates_by_card_brand,
            "card_brand",
            report_type=4,
            check_is_3ds=False,
            new_fields="card_brand",
        )

    def getCardTypeReport(self):
        order_imports_by_card_type = self.order_imports.values(*self.import_fields, "card_infor__card_type").annotate(
            card_type=F("card_infor__card_type"), **self.ANNOTATIONS_IMPORT
        )

        order_updates_by_card_type = self.order_updates.values(*self.update_fields, "order_import__card_infor__card_type").annotate(
            card_type=F("order_import__card_infor__card_type"), **self.ANNOTATIONS_UPDATE
        )

        print(f" ----> Start Card Type Reports, {order_imports_by_card_type.count()} - {order_updates_by_card_type.count()}")
        self.processReport(
            order_imports_by_card_type,
            "card_type",
            report_type=5,
            check_is_3ds=True,
            new_fields="card_type",
        )
        self.processReport(
            order_updates_by_card_type,
            "card_type",
            report_type=5,
            check_is_3ds=False,
            new_fields="card_type",
        )

    def getPricePoint(self):
        order_imports_by_price_point = self.order_imports.values(*self.import_fields, "price_point").annotate(
            annotated_price_point=F("price_point"), **self.ANNOTATIONS_IMPORT
        )

        order_updates_by_price_point = self.order_updates.values(*self.update_fields, "order_import__price_point").annotate(
            annotated_price_point=F("order_import__price_point"), **self.ANNOTATIONS_UPDATE
        )

        print(f" ----> Start Price Point Reports, {order_imports_by_price_point.count()} - {order_updates_by_price_point.count()}")
        self.processReport(
            order_imports_by_price_point,
            "price_point",
            report_type=6,
            check_is_3ds=True,
            new_fields="annotated_price_point",
        )
        self.processReport(
            order_updates_by_price_point,
            "price_point",
            report_type=6,
            check_is_3ds=False,
            new_fields="annotated_price_point",
        )

    def getCycleNumberReport(self):
        order_imports_by_cycle_number = self.order_imports.values(*self.import_fields, "cycle_num_lookup").annotate(
            cycle_number=Case(
                When(cycle_num_lookup__gt=10, then=Value(11)),
                default=F("cycle_num_lookup"),
            ),
            **self.ANNOTATIONS_IMPORT,
        )

        order_updates_by_cycle_number = self.order_updates.values(*self.update_fields, "order_import__cycle_num_lookup").annotate(
            cycle_number=Case(
                When(order_import__cycle_num_lookup__gt=10, then=Value(11)),
                default=F("order_import__cycle_num_lookup"),
            ),
            **self.ANNOTATIONS_UPDATE,
        )

        print(f" ----> Start Billing Cycle Reports, {order_imports_by_cycle_number.count()} - {order_updates_by_cycle_number.count()}")
        self.processReport(
            order_imports_by_cycle_number,
            "cycle_number",
            report_type=7,
            check_is_3ds=True,
            new_fields="cycle_number",
        )
        self.processReport(
            order_updates_by_cycle_number,
            "cycle_number",
            report_type=7,
            check_is_3ds=False,
            new_fields="cycle_number",
        )

    def getMidReport(self):
        order_imports_by_mid = self.order_imports.values(*self.import_fields, "merchantId").annotate(annotated_merchantId=F("merchantId"), **self.ANNOTATIONS_IMPORT)

        order_updates_by_mid = self.order_updates.values(*self.update_fields, "order_import__merchantId").annotate(
            annotated_merchantId=F("order_import__merchantId"), **self.ANNOTATIONS_UPDATE
        )

        print(f" ----> Start Mid Reports, {order_imports_by_mid.count()} - {order_updates_by_mid.count()}")
        self.processReport(
            order_imports_by_mid,
            "merchantId",
            report_type=8,
            check_is_3ds=True,
            new_fields="annotated_merchantId",
        )
        self.processReport(
            order_updates_by_mid,
            "merchantId",
            report_type=8,
            check_is_3ds=False,
            new_fields="annotated_merchantId",
        )

    def getTotalReports(self):
        order_imports_total = self.order_imports.values(*self.import_fields).annotate(**self.ANNOTATIONS_IMPORT)

        order_updates_total = self.order_updates.values(*self.update_fields).annotate(**self.ANNOTATIONS_UPDATE)

        print(f" ----> Start Total Reports, {order_imports_total.count()} - {order_updates_total.count()}")
        self.processReport(order_imports_total, "total", report_type=0, check_is_3ds=True)
        self.processReport(order_updates_total, "total", report_type=0, check_is_3ds=False)

    def run(self):
        print(" -> Creating backup view for OrderImport and OrderUpdate.")
        self.order_imports = self.generate_order_imports()
        self.order_updates = self.generate_order_updates()

        self.getTotalReports()  # 0
        self.getIssuerReports()  # 1
        self.getBrandReport()  # 2
        self.getFlowStepNumberReport()  # 3
        self.getCardBrandReport()  # 4
        self.getCardTypeReport()  # 5
        self.getPricePoint()  # 6
        self.getCycleNumberReport()  # 7
        self.getMidReport()  # 8


class reportByMid(models.Model):  # reporting hub page

    id = models.AutoField(primary_key=True)

    clientID = models.CharField(max_length=255, null=True, blank=True)
    report_date = models.DateTimeField(default=timezone.now)

    card_issuer = models.CharField(max_length=255, null=True, blank=True)  # 1.Breakdown By Issuer (Card Issuer)
    flow_brand_name = models.CharField(max_length=255, null=True, blank=True)  # 2. Flow Title
    flow_step_number = models.IntegerField(null=True)  # 3. Flow Step: 1, 2, 3, etc.
    card_brand = models.CharField(max_length=255, null=True, blank=True)  # 4. Card Brand: Mastercard, Visa, etc.
    card_type = models.CharField(max_length=255, null=True, blank=True)  # 5. Card Type: Credit, Debit, etc.
    price_point = models.DecimalField(max_digits=12, decimal_places=2, null=True)  # 6. Price Point: $0.00, $1.00, etc.
    cycle_number = models.IntegerField(null=True)  # 7. Cycle: 1, 2, 3, etc.

    merchantId = models.CharField(max_length=255, null=True, blank=True)  # 8. MID: 123456, 654321, etc.
    # 1 is card_issuer, 2 is flow_brand_name, 3 is flow_step_number, 4 is card_brand, 5 is card_type, 6 is price_point, 7 is cycle, 8 is mid
    type_report = models.IntegerField(default=0)

    # 3ds
    is_3ds = models.IntegerField(default=0)  # 1 is 3DS Verified, 0 is all transactions

    # approved
    transactions = models.IntegerField(null=True)
    transactions_amount = models.FloatField(null=True)

    refunds = models.IntegerField(null=True)
    refunds_amount = models.FloatField(null=True)

    chargebacks = models.IntegerField(null=True)
    chargebacks_amount = models.FloatField(null=True)

    voids = models.IntegerField(null=True)
    voids_amount = models.FloatField(null=True)

    chargebacks = models.IntegerField(null=True)
    chargebacks_amount = models.FloatField(null=True)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "FloFin_Report_ReportingHub"
