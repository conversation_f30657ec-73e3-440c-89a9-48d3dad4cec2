from enum import Enum

from django.core.exceptions import FieldError
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.db.models import Q
from django.http import HttpRequest
from drf_spectacular.utils import OpenApiParameter


class EClientStatus(Enum):
    Active = "Active"
    Integration = "Integration"
    Onboarding = "Onboarding"
    Pre_ONB = "Pre-ONB"
    Closed = "Closed"
    Removed = "Removed"
    Paused = "Paused"


TABLE_PARAMS = [
    OpenApiParameter(name="sortByVal", required=False, type=str),
    OpenApiParameter(name="orderByVal", required=False, type=str),
    OpenApiParameter(name="page", required=False, type=int),
    OpenApiParameter(name="limit", required=False, type=int),
    OpenApiParameter(name="search_all", required=False, type=str),
    OpenApiParameter(name="search_key", required=False, type=str),
    OpenApiParameter(name="search_by", required=False, type=str),
]


def getParams(request: HttpRequest):
    sortByVal = request.GET.get("sortByVal", "client_name")
    orderByVal = request.GET.get("orderByVal", "DESC")
    search_key = request.GET.get("search_key", "")
    search_by = request.GET.get("search_by", "")
    search_all = request.GET.get("search_all", None)
    page = request.GET.get("page", 1)
    limit = request.GET.get("limit", 10)
    order_by_field = f"-{sortByVal}" if orderByVal == "DESC" else sortByVal

    search_key = search_key.split(",")
    search_by = search_by.split(",")

    return sortByVal, orderByVal, search_key, search_by, page, limit, order_by_field, search_all


def filterQueryAfter(request, all_order, order_by=None, is_download=False):
    _, _, search_key, search_by, page, limit, order_by_field, search_all = getParams(request)
    try:
        try:
            all_order = all_order.order_by(order_by_field)
        except AttributeError:
            all_order = all_order.order_by("-sales")
        except FieldError:
            if order_by:
                all_order = all_order.order_by(order_by)
            else:
                all_order = all_order.order_by("?")
    except Exception:
        pass

    if search_all and search_all != "" and all_order.count() > 0:
        try:
            lst_key = list(all_order[0].keys())
            query = Q()
            for key in lst_key:
                if "description" in key:
                    for des_key in ["flow_descriptor1", "flow_descriptor2", "flow_descriptor3", "flow_descriptor4", "flow_descriptor5"]:
                        query |= Q(**{f"{des_key}__icontains": search_all})
                else:
                    query |= Q(**{f"{key}__icontains": search_all})
            all_order = all_order.filter(query)
        except:
            all_order = [data for data in all_order if str(search_all).lower() in str(data.values()).lower()]

    if search_key != [""]:
        try:
            for key, value in zip(search_by, search_key):
                if "description" in key:
                    query = Q()
                    for des_key in ["flow_descriptor1", "flow_descriptor2", "flow_descriptor3", "flow_descriptor4", "flow_descriptor5"]:
                        query |= Q(**{f"{des_key}__icontains": value})
                    all_order = all_order.filter(query)
                else:
                    print(key, value, value.lower())
                    all_order = all_order.filter(**{f"{key}__icontains": value})
        except:
            if search_by and search_key:
                for i in range(len(search_by)):
                    all_order = [data for data in all_order if str(search_key[i]).lower() in str(data[search_by[i]]).lower()]
    if is_download:
        return all_order, None

    paginator = Paginator(all_order, limit)
    try:
        flows = paginator.page(page)
    except PageNotAnInteger:
        flows = paginator.page(1)
    except EmptyPage:
        flows = paginator.page(paginator.num_pages)

    paginator = {
        "page": flows.number,
        "limit": flows.paginator.per_page,
        "total_page": paginator.num_pages,
        "total_item": paginator.count,
    }
    return list(flows), paginator
