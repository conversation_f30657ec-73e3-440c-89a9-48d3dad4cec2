from datetime import datetime, timedelta

from django.db.models import <PERSON>, F, IntegerField, Value, When
from tqdm import tqdm

from data.models import ClientLoginInformation
from flofin.models import (
    CardBinLookup,
    OfferFlow,
    OfferProductDetail,
    OrderImport,
    OrderUpdate,
)


class UpdateOrderStep1:
    """
    This class is used to update the OrderImport records with the following fields:
    - offer_product
    - card_infor
    - order_import
    - g_updated

    """

    def __init__(
        self,
        start_date=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
        end_date=datetime.now().strftime("%Y-%m-%d"),
        client_login: ClientLoginInformation = None,
    ):

        self.start_date = datetime.strptime(start_date, "%Y-%m-%d")
        self.end_date = datetime.strptime(end_date, "%Y-%m-%d")
        self.chunk_size = 10000

        if client_login:
            self.client_login = client_login
        else:
            self.client_login = None

    # Function to fetch and update OrderImport records
    def updateOfferProduct(self):
        orders_import = OrderImport.objects.filter(
            offer_product__isnull=True,
            is_test=0,  # is_test is a flag to check if the order is a test order
            transaction_date__range=(self.start_date, self.end_date),
        )
        if self.client_login:
            orders_import = orders_import.filter(client_login__internalLoginID=self.client_login.internalLoginID)

        print(f"Update Offer Product: records: {len(orders_import)}")
        successful_updates = 0

        all_product = OfferProductDetail.objects.all()
        product_dict = {prod.unique_product_match: prod for prod in all_product}

        updates = []
        for order in tqdm(orders_import):
            prod_match = order.product_match
            if prod_match in product_dict:
                order.offer_product = product_dict[prod_match]
                order.updated_at = datetime.now()
                updates.append(order)
                successful_updates += 1

        if updates:
            for i in range(0, len(updates), self.chunk_size):
                chunk = updates[i : i + self.chunk_size]
                OrderImport.objects.bulk_update(chunk, ["offer_product", "updated_at"])

        print(f" ------> Succeeded: {successful_updates}/{len(orders_import)}")

    # Function to process client data and update card bin info
    def updateCard(self):

        orders_import = OrderImport.objects.filter(
            is_test=0,
            card_infor__isnull=True,
        )
        if self.client_login:
            orders_import = orders_import.filter(client_login__internalLoginID=self.client_login.internalLoginID)

        print(f"Updated card:  {len(orders_import)} records")

        successful_card_bin_lookups = 0

        all_card_bin = CardBinLookup.objects.all()
        card_bin_dict = {cbl.card_bin: cbl for cbl in all_card_bin}

        updates = []
        for order in tqdm(orders_import):
            card_bin_number = order.cc_bin
            if card_bin_number in card_bin_dict:
                order.card_infor = card_bin_dict[card_bin_number]
                order.updated_at = datetime.now()
                updates.append(order)
                successful_card_bin_lookups += 1

        if updates:
            for i in range(0, len(updates), self.chunk_size):
                chunk = updates[i : i + self.chunk_size]
                OrderImport.objects.bulk_update(chunk, ["card_infor", "updated_at"])

        print(f" ------> Succeeded: {successful_card_bin_lookups}/{len(orders_import)}")

    def updateOrderUpdate(self):
        # Fetching records that need to be updated
        orders_update = OrderUpdate.objects.filter(g_updated__isnull=True).select_related("client_login")
        if self.client_login:
            orders_update = orders_update.filter(client_login__internalLoginID=self.client_login.internalLoginID)
        print(f"Total records fetched: {orders_update.count()}")

        order_update_fails, order_update_success = 0, 0

        # Using subqueries to fetch only the relevant data from OrderImport in a single query
        oiu_data = OrderImport.objects.filter(
            order_status=1,
            client_login__in=orders_update.values_list("client_login", flat=True),
            product_match__in=orders_update.values_list("product_match", flat=True),
            order_id__in=orders_update.values_list("order_id", flat=True),
        ).values(
            "id",
            "offer_product__offer_flow_step__flow__flow_brand_name",
            "payment_network",
            "client_login__loginID",
            "product_match",
            "order_id",
        )

        # Creating a dictionary for fast lookup
        oiu_dict = {(oi["client_login__loginID"], oi["product_match"], oi["order_id"]): oi for oi in oiu_data}

        updates = []

        for ou in orders_update:
            key = (ou.client_login.loginID, ou.product_match, ou.order_id)

            if key in oiu_dict:
                oi_entry = oiu_dict[key]
                ou.order_import = OrderImport.objects.get(id=oi_entry["id"])
                ou.g_updated = 1
                ou.updated_at = datetime.now()
                updates.append(ou)
                order_update_success += 1
            else:
                order_update_fails += 1

        # Bulk update in chunks
        if updates:
            for i in range(0, len(updates), self.chunk_size):
                chunk = updates[i : i + self.chunk_size]
                OrderUpdate.objects.bulk_update(chunk, ["order_import_id", "g_updated", "updated_at"])

        print(f"Total successful updates: {order_update_success}")
        print(f"Total failed updates: {order_update_fails}")

    def run(self):
        # Step 1
        self.updateOfferProduct()
        self.updateCard()
        self.updateOrderUpdate()


class UpdateOrderStep2:
    def __init__(self):
        self.chunk_size = 10000
        self.lst_product_match = list(set(OfferFlow.objects.values_list("first_product_match", flat=True)))

    # Function to update Billing Cycle lookup for an order
    def updateCycleNumLookup(self, order: OrderImport):
        if order.offer_product.backlogged_cycle_flag == 1:  # Need to check this
            oicn_count = OrderImport.objects.filter(
                offer_product__offer_flow_step__flow_step_number=order.offer_product.offer_flow_step.flow_step_number,
                transaction_date__lt=order.transaction_date,
                order_status=1,
                client_login=order.client_login,
                customer_id=order.customer_id,
            ).count()
            order.cycle_num_lookup = oicn_count
        else:
            order.cycle_num_lookup = order.offer_product.cycle_number

    # Function to update ancestor ID and date for an order
    def updateAncestorId(self, order: OrderImport):
        if order.offer_product.offer_flow_step.flow_step_number == 1 and order.cycle_num_lookup == 0:
            order.ancestor_id = order.id
            order.ancestor_date = order.transaction_date
        else:
            oicna = (
                OrderImport.objects.filter(
                    offer_product__offer_flow_step__flow_step_number=1,
                    cycle_num_lookup=0,
                    offer_product__offer_flow_step__flow__flow_id=order.offer_product.offer_flow_step.flow.flow_id,
                    order_status=1,
                    client_login=order.client_login,
                    customer_id=order.customer_id,
                )
                .values("id", "transaction_date")
                .order_by("transaction_date")
                .first()
            )

            if oicna:
                order.ancestor_id = oicna["id"]
                order.ancestor_date = oicna["transaction_date"]

    # Function to update step parent ID and date for an order
    def updateStepParentId(self, order: OrderImport):
        if order.cycle_num_lookup == 0:
            order.step_parent_id = order.id
            order.step_parent_date = order.transaction_date
        else:
            try:
                oicnb = (
                    OrderImport.objects.filter(
                        cycle_num_lookup=0,
                        offer_product__offer_flow_step__flow_step_number=order.offer_product.offer_flow_step.flow_step_number,
                        offer_product__offer_flow_step__flow__flow_id=order.offer_product.offer_flow_step.flow.flow_id,
                        order_status=1,
                        client_login=order.client_login,
                        customer_id=order.customer_id,
                        transaction_date__lte=order.transaction_date,
                    )
                    .values("id", "transaction_date")
                    .first()
                )

                if oicnb:
                    order.step_parent_id = oicnb["id"]
                    order.step_parent_date = oicnb["transaction_date"]
            except IndexError:
                pass

    # Main function to update orders
    def updateOrders(self, start_date=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")):
        orders_import = OrderImport.objects.filter(
            offer_product__isnull=False,
            transaction_date__gte=start_date,
            # customer_id = 1500062
        ).select_related(
            "offer_product",
            "offer_product__offer_flow_step",
            "offer_product__offer_flow_step__flow",
        )
        print(f"Total records fetched: {orders_import.count()}")

        updates = []
        for order in tqdm(orders_import):
            self.updateCycleNumLookup(order)
            self.updateAncestorId(order)
            self.updateStepParentId(order)
            updates.append(order)

        if updates:
            for i in range(0, len(updates), self.chunk_size):
                chunk = updates[i : i + self.chunk_size]
                OrderImport.objects.bulk_update(
                    chunk,
                    [
                        "cycle_num_lookup",
                        "ancestor_id",
                        "ancestor_date",
                        "step_parent_id",
                        "step_parent_date",
                    ],
                )

        # print(orders_import.distinct('offer_product__offer_flow_step__flow_step_number', ''))

    def run(self):
        self.updateOrders()


class UpdateOrderImportStep3:
    def __init__(self):
        self.chunk_size = 10000

    def updateTransAttNumber(self, start_date):
        orders = OrderImport.objects.filter(
            offer_product__isnull=False,
            ancestor_id__isnull=False,
            step_parent_id__isnull=False,
            transaction_date__gte=start_date,
            trans_att_num__isnull=True,
        )
        # ).select_related(
        #     "offer_product",
        #     "offer_product__offer_flow_step",
        #     "offer_product__offer_flow_step__offer_flow_step",
        # )
        for order in tqdm(orders):
            try:
                oicnc_count = OrderImport.objects.filter(
                    ancestor_id=order.ancestor_id,
                    client_login=order.client_login,
                    customer_id=order.customer_id,
                    step_parent_id=order.step_parent_id,
                    offer_product__offer_flow_step__offer_flow_step_id=order.offer_product.offer_flow_step.offer_flow_step_id,
                    cycle_num_lookup=order.cycle_num_lookup,
                    transaction_date__lt=order.transaction_date,
                    order_status=0,
                ).count()

                order.trans_att_num = oicnc_count + 1

                if order.cycle_num_lookup > 0:
                    oicnd = OrderImport.objects.filter(
                        client_login=order.client_login,
                        customer_id=order.customer_id,
                        offer_product__offer_flow_step__flow_step_number=order.offer_product.offer_flow_step.flow_step_number,
                        cycle_num_lookup=order.cycle_num_lookup - 1,
                        order_status=1,
                    ).first()

                    if oicnd:
                        order.direct_parent_id = oicnd.id

            except Exception as e:
                print(f"Failed to update trans att and direct parent on: {order.id} - {e}")
                return False

            order.updated_at = datetime.now()
            order.save()

    def updateDelayHours(self, orders: list[OrderImport]):
        annotated_orders = orders.filter(delay_hours__isnull=True).annotate(
            rebill_delay_hours=F("offer_product__offer_flow_step__rebill_delay_hours"),
            cont_cycle_1_delay_hours=F("offer_product__offer_flow_step__cont_cycle_1_delay_hours"),
            cont_cycle_frequency=F("offer_product__offer_flow_step__cont_cycle_frequency"),
            continuity_enabled=F("offer_product__offer_flow_step__continuity_enabled"),
        )

        for order in annotated_orders:
            print(order.cycle_num_lookup, order.continuity_enabled)
            if order.cycle_num_lookup == 0 and order.continuity_enabled == 0:
                order.delay_hours = order.rebill_delay_hours
            elif order.cycle_num_lookup == 0 and order.continuity_enabled == 1:
                order.delay_hours = order.cont_cycle_1_delay_hours
            elif order.cycle_num_lookup > 0 and order.continuity_enabled == 1:
                order.delay_hours = order.cont_cycle_frequency
            else:
                order.delay_hours = 0

            # Update projected_rebill_date based on delay_hours
            if order.continuity_enabled == 1:
                order.projected_rebill_date = order.transaction_date + timedelta(hours=order.delay_hours)
            else:
                order.projected_rebill_date = None

            order.updated_at = datetime.now()
            self.updates.append(order)

    # def updateCpaAmount(self, orders: list[OrderImport]):
    #     cpa_dict = {(cpa.offer_flow_step.offer_flow_step_id, cpa.cpa_match_field): cpa for cpa in CostPerAcqFull.objects.all()}

    #     orders = orders.filter(
    #         cpa_amount_script__isnull=True,
    #         vendorid1__isnull=False,
    #     ).select_related("offer_product", "offer_product__offer_flow_step")

    #     for order in orders:
    #         cpa = cpa_dict.get(
    #             (
    #                 order.offer_product.offer_flow_step.offer_flow_step_id,
    #                 order.vendorid1,
    #             )
    #         )
    #         if cpa:
    #             if order.cycle_num_lookup == 0 and order.is_scrubbed == 0:
    #                 order.cpa_amount_script = cpa.cpa_amount
    #             else:
    #                 order.cpa_amount_script = 0
    #             order.updated_at = datetime.now()
    #             self.updates.append(order)

    def updateSaleFlags(self, orders):
        (
            orders.filter(is_unique_sale__isnull=True).update(
                is_unique_sale=Case(
                    When(id=F("ancestor_id"), order_status=1, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField(),
                ),
                is_attempt_sale=Case(
                    When(id=F("ancestor_id"), then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField(),
                ),
                initial_product_cost=Case(
                    When(
                        id=F("step_parent_id"),
                        order_status=1,
                        then=F("initial_product_cost"),
                    ),
                    default=Value(0),
                    output_field=IntegerField(),
                ),
                continuity_product_cost=Case(
                    When(id=F("step_parent_id"), order_status=1, then=Value(0)),
                    default=F("continuity_product_cost"),
                    output_field=IntegerField(),
                ),
            )
        )

    def run(self, start_date=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")):
        self.updates = []
        oif = OrderImport.objects.filter(
            offer_product__isnull=False,
            transaction_date__gte=start_date,
        )
        self.updateTransAttNumber(start_date)
        # self.updateCpaAmount(oif)
        self.updateSaleFlags(oif)
        self.updateDelayHours(oif)

        if self.updates:
            for i in range(0, len(self.updates), self.chunk_size):
                chunk = self.updates[i : i + self.chunk_size]
                OrderImport.objects.bulk_update(
                    chunk,
                    [
                        "cpa_amount_script",
                        "cpa_updated_script",
                        "delay_hours",
                        "projected_rebill_date",
                        "updated_at",
                    ],
                )
