import csv
from io import StringIO
import pandas as pd

from django.db.models import F, <PERSON><PERSON><PERSON>ield, IntegerField, Q, Sum, Value
from django.db.models.functions import Coalesce
from django.db.models.query import QuerySet
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.utils.dateparse import parse_date
from django.views.decorators.csrf import csrf_exempt
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework.decorators import api_view
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_405_METHOD_NOT_ALLOWED,
)

from flofin.models import HomeCLVReport
from flofin.serializers import (
    GetDataUnsuccessfulResponseSerializer,
    get3dsByMidResponseSerializer,
    get3dsResponseSerializer,
    getAnalysisBreakdownByIssuerResponseSerializer,
    getAnalysisChartViewResponseSerializer,
    getCardBrandResponseSerializer,
    getCardTypeResponseSerializer,
    getCycleResponseSerializer,
    getFlowBrandNameResponseSerializer,
    getFlowStepNumberResponseSerializer,
    getIssuerResponseSerializer,
    getPricePointResponseSerializer,
    inValidMethodResponseSerializer,
)
from flofin.services.utils import getClientView
from flofin.services.KpiCLV import get_clv_data, get_unified_clv_data
from user.utils.utils import require_login

REPORTING_HUB_PARAMETERS = [
    OpenApiParameter(
        name="original_sale_start_date",
        description="Original Sale Start Date - format: YYYY-MM-DD",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="original_sale_end_date",
        description="Original Sale End Date - format: YYYY-MM-DD",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="as_of_date_start_date",
        description="Transaction Start Date - format: YYYY-MM-DD",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="as_of_date_end_date",
        description="Transaction End Date - format: YYYY-MM-DD",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="flow_brand_name[]", description="Flow Title", required=False, type=str
    ),
    OpenApiParameter(
        name="lst_flow_brand_name[]",
        description="List Flow Title",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="card_brand[]", description="Card Brand", required=False, type=str
    ),
    OpenApiParameter(name="network[]", description="Network", required=False, type=str),
    OpenApiParameter(name="pub[]", description="Pub", required=False, type=str),
    OpenApiParameter(
        name="clientID[]", description="Client ID", required=False, type=str
    ),
    OpenApiParameter(
        name="cycle[]", description="Billing Cycle", required=False, type=str
    ),
    OpenApiParameter(
        name="card_type[]", description="Card Type", required=False, type=str
    ),
    OpenApiParameter(
        name="price_point[]", description="Price Point", required=False, type=str
    ),
    OpenApiParameter(
        name="flow_step_number[]", description="Flow Step", required=False, type=str
    ),
]


def get_chart_view_data(request: HttpRequest) -> list:
    total_report = get_unified_clv_data(request=request)

    total_report = (
        total_report.values("transaction_date")
        .annotate(
            date=F("transaction_date"),
            chargebacks=Sum("chargebacks"),
            chargebacks_amount=Sum("chargebacks_amount"),
            refunds_and_voids=Sum("refunds") + Sum("voids"),
            refunds_and_voids_amount=Sum("refunds_amount") + Sum("voids_amount"),
            transactions=Sum("transactions"),
            transactions_amount=Sum("transactions_amount"),
        )
        .order_by("transaction_date")
    )
    for record in total_report:
        record["chargebacks"] = (
            0 if record["chargebacks"] is None else record["chargebacks"]
        )
        record["chargebacks_amount"] = (
            0 if record["chargebacks_amount"] is None else record["chargebacks_amount"]
        )
        record["refunds_and_voids"] = (
            0 if record["refunds_and_voids"] is None else record["refunds_and_voids"]
        )
        record["refunds_and_voids_amount"] = (
            0
            if record["refunds_and_voids_amount"] is None
            else record["refunds_and_voids_amount"]
        )

        record["chargebacks_rate"] = (
            (record["chargebacks"] / record["transactions"]) * 100
            if bool(record["transactions"])
            else 0
        )
        record["refunds_and_voids_rate"] = (
            (record["refunds_and_voids"] / record["transactions"]) * 100
            if bool(record["transactions"])
            else 0
        )
        record["chargebacks_amount_rate"] = (
            (record["chargebacks_amount"] / record["transactions_amount"]) * 100
            if bool(record["transactions_amount"])
            else 0
        )
        record["refunds_and_voids_amount_rate"] = (
            (record["refunds_and_voids_amount"] / record["transactions_amount"]) * 100
            if bool(record["transactions_amount"])
            else 0
        )
    return list(total_report)


def get_summary_data(request: HttpRequest) -> tuple:
    report_data = get_unified_clv_data(request=request)

    count_voids = report_data.aggregate(
        count_voids=Coalesce(Sum("voids"), Value(0), output_field=IntegerField())
    )["count_voids"]

    voided_revenue_amount = report_data.aggregate(
        sum_voids_amount=Coalesce(
            Sum("voided_revenue"), Value(0), output_field=FloatField()
        ),
    )["sum_voids_amount"]

    count_refunds = report_data.aggregate(
        count_refunds=Coalesce(Sum("refunds"), Value(0), output_field=IntegerField())
    )["count_refunds"]
    refunded_revenue_amount = report_data.aggregate(
        sum_refunds_amount=Coalesce(
            Sum("refuded_revenue"), Value(0), output_field=FloatField()
        ),
    )["sum_refunds_amount"]

    count_transactions = report_data.aggregate(
        transactions=Coalesce(Sum("sales"), Value(0), output_field=IntegerField()),
    )["transactions"]
    transactions_amount = report_data.aggregate(
        transactions_amount=Coalesce(
            Sum("revenue"), Value(0), output_field=FloatField()
        ),
    )["transactions_amount"]

    count_chargebacks = report_data.aggregate(
        chargebacks=Coalesce(Sum("cb_count"), Value(0), output_field=IntegerField()),
    )["chargebacks"]
    chargebacks_amount = report_data.aggregate(
        chargebacks_amount=Coalesce(
            Sum("cb_revenue"), Value(0), output_field=FloatField()
        ),
    )["chargebacks_amount"]

    tmp_data = {
        "transactions": count_transactions,
        "chargebacks": count_chargebacks,
        "refunds_and_voids": count_refunds + count_voids,
        "transactions_amount": transactions_amount,
        "chargebacks_amount": chargebacks_amount,
        "refunds_and_voids_amount": refunded_revenue_amount + voided_revenue_amount,
    }

    summary = {
        "general_overview": {
            "total_chargebacks": {
                "count": count_chargebacks,
                "amount": chargebacks_amount,
                "transactions": count_transactions,
                "transactions_amount": transactions_amount,
                "count_percentage": count_chargebacks / count_transactions * 100
                if count_transactions > 0
                else 0,
                "amount_percentage": chargebacks_amount / transactions_amount * 100
                if transactions_amount > 0
                else 0,
            },
            "total_refunds_and_voids": {
                "count": count_refunds + count_voids,
                "amount": refunded_revenue_amount + voided_revenue_amount,
                "transactions": count_transactions,
                "transactions_amount": transactions_amount,
                "count_percentage": (count_refunds + count_voids)
                / count_transactions
                * 100
                if count_transactions > 0
                else 0,
                "amount_percentage": (refunded_revenue_amount + voided_revenue_amount)
                / transactions_amount
                * 100
                if transactions_amount > 0
                else 0,
            },
            "total_transactions": {
                "count": count_transactions,
                "amount": transactions_amount,
                "transactions": count_transactions,
                "transactions_amount": transactions_amount,
                "count_percentage": 100,
                "amount_percentage": 100,
            },
        }
    }
    return summary, tmp_data, report_data


def get_flow_step_number_metrics(request: HttpRequest) -> JsonResponse:
    total_data = get_unified_clv_data(request=request)
    response_data = get_response_metrics(total_data, "flow_step_number")
    return response_data


def get_card_brand_metrics(request: HttpRequest) -> JsonResponse:
    total_data = get_unified_clv_data(request=request)
    response_data = get_response_metrics(total_data, "card_brand")
    return response_data


def get_card_type_metrics(request: HttpRequest) -> JsonResponse:
    total_data = get_unified_clv_data(request=request)
    response_data = get_response_metrics(total_data, "card_type")
    return response_data


def make_price_point_or_cycle_metrics(total_report, metric_type) -> JsonResponse:
    metric_data = {}

    for record in total_report:
        count_sales = record["sales"]
        if count_sales is None:
            count_sales = 0
        count_refunds = record["refunds"]
        if count_refunds is None:
            count_refunds = 0
        count_voids = record["voids"]
        if count_voids is None:
            count_voids = 0
        count_chargebacks = record["cb_count"]
        if count_chargebacks is None:
            count_chargebacks = 0

        revenue, refuned_void_revenue, chargeback_revenue = extract_sale_types_amount(
            record
        )

        metric = record[metric_type]
        if metric is None:
            metric = "Other"

        if metric not in metric_data:
            metric_data[metric] = {
                "sales": count_sales,
                "refunds": count_refunds,
                "voids": count_voids,
                "chargebacks": count_chargebacks,
                "revenue": revenue,
                "refunded_voided_revenue": refuned_void_revenue,
                "chargeback_revenue": chargeback_revenue,
            }
        else:
            metric_data[metric]["sales"] += count_sales
            metric_data[metric]["refunds"] += count_refunds
            metric_data[metric]["voids"] += count_voids
            metric_data[metric]["chargebacks"] += count_chargebacks
            metric_data[metric]["revenue"] += revenue
            metric_data[metric]["refunded_voided_revenue"] += refuned_void_revenue
            metric_data[metric]["chargeback_revenue"] += chargeback_revenue

        total_report = []
        for metric in metric_data.keys():
            count_sales = metric_data[metric]["sales"]
            count_refunds = metric_data[metric]["refunds"]
            count_voids = metric_data[metric]["voids"]

            count_refunds_and_voids = count_refunds + count_voids

            count_chargebacks = metric_data[metric]["chargebacks"]
            revenue = metric_data[metric]["revenue"]
            refuned_void_revenue = metric_data[metric]["refunded_voided_revenue"]
            chargeback_revenue = metric_data[metric]["chargeback_revenue"]

            total_report.append(
                {
                    "name": metric,
                    "totals": {
                        "count": count_sales,
                        "transactions": count_sales,
                        "amount": revenue,
                        "transactions_amount": revenue,
                    },
                    "chargebacks": {
                        "count": count_chargebacks,
                        "transactions": count_sales,
                        "amount": chargeback_revenue,
                        "transactions_amount": revenue,
                        "count_percentage": count_chargebacks / count_sales
                        if count_sales != 0
                        else 0,
                        "amount_percentage": chargeback_revenue / revenue
                        if revenue != 0
                        else 0,
                    },
                    "refunds_and_voids": {
                        "count": count_refunds_and_voids,
                        "transactions": count_sales,
                        "amount": refuned_void_revenue,
                        "transactions_amount": revenue,
                        "count_percentage": count_refunds_and_voids / count_sales
                        if count_sales != 0
                        else 0,
                        "amount_percentage": refuned_void_revenue / revenue
                        if revenue != 0
                        else 0,
                    },
                }
            )

    return total_report


def get_price_point_metrics(request: HttpRequest) -> JsonResponse:
    total_report = get_unified_clv_data(request=request)
    total_report = total_report.order_by("price_point")

    total_report = make_price_point_or_cycle_metrics(total_report, "price_point")

    return total_report


def get_cycle_metrics(request: HttpRequest) -> JsonResponse:
    total_data = get_unified_clv_data(request=request)
    total_data = total_data.order_by("cycle_num")
    response_data = make_price_point_or_cycle_metrics(total_data, "cycle_num")
    return response_data


def get_card_issuer_metrics(request: HttpRequest) -> JsonResponse:
    total_data = get_unified_clv_data(request=request)
    response_data = get_response_metrics(total_data, "card_issuer")
    return response_data


def get_3ds_by_mid_metrics(request: HttpRequest) -> JsonResponse:
    total_data = get_unified_clv_data(request=request)
    response_data = get_response_metrics(total_data, "merchantId")
    return response_data


def extract_sale_types_amount(record):
    revenue = record["revenue"]
    refunded_revenue = record["refuded_revenue"]
    chargeback_revenue = record["cb_revenue"]
    voided_revenue = record["voided_revenue"]

    if not revenue:
        revenue = 0

    if not refunded_revenue:
        refunded_revenue = 0

    if not chargeback_revenue:
        chargeback_revenue = 0

    if not voided_revenue:
        voided_revenue = 0

    refuned_void_revenue = refunded_revenue + voided_revenue

    return revenue, refuned_void_revenue, chargeback_revenue


def get_response_metrics(total_data, metric_name):
    response_data = []
    metric_data = {}
    total_revenue = 0
    total_refunded_voided_revenue = 0
    total_chargeback_revenue = 0

    for record in total_data:
        element_name = record[metric_name]
        revenue, refuned_void_revenue, chargeback_revenue = extract_sale_types_amount(
            record
        )
        total_revenue += revenue
        total_refunded_voided_revenue += refuned_void_revenue
        total_chargeback_revenue += chargeback_revenue
        if element_name not in metric_data.keys():
            metric_data[element_name] = {
                "revenue": revenue,
                "refunded_voided_revenue": refuned_void_revenue,
                "chargeback_revenue": chargeback_revenue,
            }
        else:
            metric_data[element_name]["revenue"] += revenue
            metric_data[element_name]["refunded_voided_revenue"] += refuned_void_revenue
            metric_data[element_name]["chargeback_revenue"] += chargeback_revenue

    response_data.append(
        {
            "name": "Grand total",
            "totals": {
                "amount": total_revenue,
            },
            "chargebacks": {
                "amount": total_chargeback_revenue,
            },
            "refunds_and_voids": {
                "amount": total_refunded_voided_revenue,
            },
        }
    )

    for element_name in metric_data.keys():
        response_data.append(
            {
                "name": element_name,
                "totals": {
                    "amount": metric_data[element_name]["revenue"],
                },
                "chargebacks": {
                    "amount": metric_data[element_name]["chargeback_revenue"],
                },
                "refunds_and_voids": {
                    "amount": metric_data[element_name]["refunded_voided_revenue"],
                },
            }
        )
    return response_data


def get_flow_brand_name_metrics(request: HttpRequest) -> JsonResponse:
    total_data = get_unified_clv_data(request=request)
    response_data = get_response_metrics(total_data, "flow_brand_name")
    return response_data


def getTotalData(
    type_report_name="card_issuer",
    care_3d=False,
    request: HttpRequest = None,
):
    _, total_report_data = get_summary_data(request=request)

    sub_report = get_clv_data(request=request, additional_value=type_report_name)
    if care_3d:
        sub_report.filter(total_3ds__gt=0)

    total_sub_report_data = sub_report.values(type_report_name).annotate(
        transactions=Coalesce(
            Sum("transactions"), Value(0), output_field=IntegerField()
        ),
        chargebacks=Coalesce(Sum("chargebacks"), Value(0), output_field=IntegerField()),
        refunds_and_voids=Coalesce(
            Sum("refunds") + Sum("voids"), Value(0), output_field=IntegerField()
        ),
        transactions_amount=Coalesce(
            Sum("transactions_amount"), Value(0), output_field=FloatField()
        ),
        chargebacks_amount=Coalesce(
            Sum("chargebacks_amount"), Value(0), output_field=FloatField()
        ),
        refunds_and_voids_amount=Coalesce(
            Sum("refunds_amount") + Sum("voids_amount"),
            Value(0),
            output_field=FloatField(),
        ),
    )
    if type_report_name == "price_point" or type_report_name == "cycle_num":
        total_sub_report_data = total_sub_report_data.order_by(type_report_name)
    else:
        total_sub_report_data = total_sub_report_data.order_by("-transactions_amount")
    if type_report_name == "cycle_num":
        total_sub_report_data = total_sub_report_data.filter(cycle_num__isnull=False)

    total_response = {
        "name": "Grand total",
        "chargebacks": {
            "count": 0,
            "amount": 0,
            "transactions": 0,
            "transactions_amount": 0,
        },
        "refunds_and_voids": {
            "count": 0,
            "amount": 0,
            "transactions": 0,
            "transactions_amount": 0,
        },
        "totals": {
            "count": 0,
            "amount": 0,
            "transactions": 0,
            "transactions_amount": 0,
            "count_percentage": 100,
            "amount_percentage": 100,
        },
    }

    sub_response = []
    for record in total_sub_report_data:
        record_transactions = (
            record["transactions"] if bool(record["transactions"]) else 0
        )
        record_transactions_amount = (
            record["transactions_amount"] if bool(record["transactions_amount"]) else 0
        )
        record_chargebacks = record["chargebacks"] if bool(record["chargebacks"]) else 0
        record_chargebacks_amount = (
            record["chargebacks_amount"] if bool(record["chargebacks_amount"]) else 0
        )
        record_refunds_and_voids = (
            record["refunds_and_voids"] if bool(record["refunds_and_voids"]) else 0
        )
        record_refunds_and_voids_amount = (
            record["refunds_and_voids_amount"]
            if bool(record["refunds_and_voids_amount"])
            else 0
        )
        sub_response.append(
            {
                "name": record[type_report_name]
                if record[type_report_name] is not None
                else "Other",
                "totals": {
                    "count": record_transactions,
                    "transactions": record_transactions,
                    "amount": record_transactions_amount,
                    "transactions_amount": record_transactions_amount,
                    "count_percentage": (
                        record_transactions / total_report_data["transactions"]
                    )
                    * 100
                    if bool(total_report_data["transactions"])
                    else 0,
                    "amount_percentage": (
                        record_transactions_amount
                        / total_report_data["transactions_amount"]
                    )
                    * 100
                    if bool(total_report_data["transactions_amount"])
                    else 0,
                },
                "chargebacks": {
                    "count": record_chargebacks,
                    "transactions": record_transactions,
                    "amount": record_chargebacks_amount,
                    "transactions_amount": record_transactions_amount,
                    "count_percentage": (record_chargebacks / record_transactions) * 100
                    if bool(record_transactions)
                    else 0,
                    "amount_percentage": (
                        record_chargebacks_amount / record_transactions_amount
                    )
                    * 100
                    if bool(record_transactions_amount)
                    else 0,
                },
                "refunds_and_voids": {
                    "count": record_refunds_and_voids,
                    "transactions": record_transactions,
                    "amount": record_refunds_and_voids_amount,
                    "transactions_amount": record_transactions_amount,
                    "count_percentage": (record_refunds_and_voids / record_transactions)
                    * 100
                    if bool(record_transactions)
                    else 0,
                    "amount_percentage": (
                        record_refunds_and_voids_amount / record_transactions_amount
                    )
                    * 100
                    if bool(record_transactions_amount)
                    else 0,
                },
            }
        )
        total_response["chargebacks"]["count"] += record_chargebacks
        total_response["chargebacks"]["amount"] += record_chargebacks_amount
        total_response["chargebacks"]["transactions"] += record_transactions
        total_response["chargebacks"]["transactions_amount"] += (
            record_transactions_amount
        )
        total_response["refunds_and_voids"]["count"] += record_refunds_and_voids
        total_response["refunds_and_voids"]["amount"] += record_refunds_and_voids_amount
        total_response["refunds_and_voids"]["transactions"] += record_transactions
        total_response["refunds_and_voids"]["transactions_amount"] += (
            record_transactions_amount
        )
        total_response["totals"]["count"] += record_transactions
        total_response["totals"]["amount"] += record_transactions_amount
        total_response["totals"]["transactions"] += record_transactions
        total_response["totals"]["transactions_amount"] += record_transactions_amount

    total_response["chargebacks"]["count_percentage"] = (
        (total_response["chargebacks"]["count"] / total_response["totals"]["count"])
        * 100
        if bool(total_response["totals"]["count"])
        else 0
    )
    total_response["chargebacks"]["amount_percentage"] = (
        (total_response["chargebacks"]["amount"] / total_response["totals"]["amount"])
        * 100
        if bool(total_response["totals"]["amount"])
        else 0
    )
    total_response["refunds_and_voids"]["count_percentage"] = (
        (
            total_response["refunds_and_voids"]["count"]
            / total_response["totals"]["count"]
        )
        * 100
        if bool(total_response["totals"]["count"])
        else 0
    )
    total_response["refunds_and_voids"]["amount_percentage"] = (
        (
            total_response["refunds_and_voids"]["amount"]
            / total_response["totals"]["amount"]
        )
        * 100
        if bool(total_response["totals"]["amount"])
        else 0
    )

    if type_report_name != "price_point" and type_report_name != "cycle_num":
        sub_response.insert(0, total_response)

    return sub_response


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getAnalysisChartViewResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Marketing Source With Most Chargeback - Chart View",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getAnalysisChartView(request: HttpRequest) -> JsonResponse:
    try:
        response = get_chart_view_data(request)

        return JsonResponse(
            {
                "message": "Get Marketing Source With Most Chargeback - Chart View successfully.",
                "data": {
                    "chart_view": response,
                },
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getPricePointResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - Price Point",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get_price_point(request: HttpRequest) -> JsonResponse:
    try:
        price_point_metrics = get_price_point_metrics(request)

        return JsonResponse(
            {
                "message": "Get price point successfully.",
                "data": list(price_point_metrics),
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getCycleResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - Cycle",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get_cycle(request: HttpRequest) -> JsonResponse:
    try:
        cycle_metrics = get_cycle_metrics(request)

        return JsonResponse(
            {"message": "Get cycle successfully.", "data": list(cycle_metrics)},
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getFlowStepNumberResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - Flow Step",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get_flow_step_number(request: HttpRequest) -> JsonResponse:
    try:
        flow_step_metrics = get_flow_step_number_metrics(request)

        return JsonResponse(
            {
                "message": "Get Flow Step successfully.",
                "data": list(flow_step_metrics),
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getCardBrandResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - Card Brand",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get_card_brand(request: HttpRequest) -> JsonResponse:
    try:
        card_brand_metrics = get_card_brand_metrics(request)

        return JsonResponse(
            {
                "message": "Get Card Brand successfully.",
                "data": list(card_brand_metrics),
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getCardTypeResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - Card Type",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get_card_type(request: HttpRequest) -> JsonResponse:
    try:
        card_type_metrics = get_card_type_metrics(request)

        return JsonResponse(
            {"message": "Get Card Type successfully.", "data": list(card_type_metrics)},
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getIssuerResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - Issuer",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get_issuer(request: HttpRequest) -> JsonResponse:
    try:
        card_issuer_metrics = get_card_issuer_metrics(request)
        return JsonResponse(
            {"message": "Get Issuer successfully.", "data": list(card_issuer_metrics)},
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: get3dsResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - 3DS",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get3ds(request: HttpRequest) -> JsonResponse:
    try:
        sub_response = get_card_issuer_metrics(request)

        sub_response = sub_response[:3]
        for record in sub_response:
            if record["name"] == "Grand total":
                continue
            elif record["name"] == "Longtail":
                record["name"] = "3DS"
            elif record["name"] == "CHASE BANK USA, N.A.":
                record["name"] = "AVS"
            elif record["name"] == "CITIBANK N.A.":
                record["name"] = "CVV"
            elif record["name"] == "BANK OF AMERICA, N.A.":
                record["name"] = "CVM"
            elif record["name"] == "CAPITAL ONE BANK (USA), NATIONAL ASSOCIATION":
                record["name"] = "Other"
            elif record["name"] == "BANK OF AMERICA, NATIONAL ASSOCIATION":
                record["name"] = "Other"

        return JsonResponse(
            {"message": "Get 3ds successfully.", "data": list(sub_response)},
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: get3dsByMidResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - 3DS By Mid",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get3ds_by_mid(request: HttpRequest) -> JsonResponse:
    try:
        sub_response = get_3ds_by_mid_metrics(request)
        return JsonResponse(
            {"message": "Get 3ds By Mid successfully.", "data": list(sub_response)},
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getAnalysisBreakdownByIssuerResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Marketing Source With Most Chargeback - Breakdown By Issuer",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getAnalysisBreakdownByIssuer(request: HttpRequest) -> JsonResponse:
    try:
        summary_data, _, _ = get_summary_data(request=request)

        return JsonResponse(
            {
                "message": "Get Breakdown By Issuer successfully.",
                "data": summary_data,
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getFlowBrandNameResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - Flow Title",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def get_flow_brand_name(request: HttpRequest) -> JsonResponse:
    try:
        flow_brand_metrics = get_flow_brand_name_metrics(request=request)

        return JsonResponse(
            {
                "message": "Get Flow Title successfully.",
                "data": list(flow_brand_metrics),
            },
            status=HTTP_200_OK,
        )
    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS
    + [
        OpenApiParameter(
            name="type_report_name",
            description="Type Report Name",
            required=False,
            type=str,
            enum=[
                "flow_brand_name",
                "flow_step_number",
                "card_brand",
                "cycle",
                "card_type",
                "price_point",
                "issuer",
                "3ds",
                "3ds_by_mid",
            ],
        ),
    ],
    responses={
        HTTP_200_OK: getFlowBrandNameResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - Flow Title",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def downloadReport(request: HttpRequest) -> HttpResponse:
    try:
        type_report_name = request.GET.get("type_report_name")
        if type_report_name is None:
            return JsonResponse(
                {"message": "Type Report Name is required"},
                status=HTTP_400_BAD_REQUEST,
            )

        if type_report_name not in [
            "flow_brand_name",
            "flow_step_numbernumber",
            "card_brand",
            "cycle",
            "card_type",
            "price_point",
            "issuer",
            "3ds",
            "3ds_by_mid",
        ]:
            return JsonResponse(
                {"message": "Type Report Name is invalid"},
                status=HTTP_400_BAD_REQUEST,
            )

        if type_report_name == "flow_brand_name":
            care_3d = False
        elif type_report_name == "flow_step_numbernumber":
            type_report_name = "flow_step_number"
            care_3d = False
        elif type_report_name == "card_brand":
            care_3d = False
        elif type_report_name == "cycle":
            type_report_name = "cycle_num"
            care_3d = False
        elif type_report_name == "card_type":
            care_3d = False
        elif type_report_name == "price_point":
            care_3d = False
        elif type_report_name == "issuer":
            type_report_name = "card_issuer"
            care_3d = False
        elif type_report_name == "3ds":
            type_report_name = "card_issuer"
            care_3d = True
        elif type_report_name == "3ds_by_mid":
            type_report_name = "merchantId"
            care_3d = True
        else:
            return JsonResponse(
                {"message": "Type Report Name is invalid"},
                status=HTTP_400_BAD_REQUEST,
            )
        data = getTotalData(
            type_report_name=type_report_name,
            request=request,
            care_3d=care_3d,
        )

        excel_file = StringIO()
        csv_writer = csv.writer(excel_file)
        if len(data) > 0:
            csv_writer.writerow(
                [
                    "name",
                    "chargebacks_count",
                    "chargebacks_amount",
                    "chargebacks_count_percentage",
                    "chargebacks_amount_percentage",
                    "refunds_and_voids_count",
                    "refunds_and_voids_amount",
                    "refunds_and_voids_count_percentage",
                    "refunds_and_voids_amount_percentage",
                    "totals_transactions",
                    "totals_transactions_amount",
                    "totals_count_percentage",
                    "totals_amount_percentage",
                ]
            )
        else:
            return JsonResponse({"message": "No data found"}, status=HTTP_200_OK)
        for row in data:
            csv_writer.writerow(
                [
                    row["name"],
                    row["chargebacks"]["count"],
                    row["chargebacks"]["amount"],
                    row["chargebacks"]["count_percentage"],
                    row["chargebacks"]["amount_percentage"],
                    row["refunds_and_voids"]["count"],
                    row["refunds_and_voids"]["amount"],
                    row["refunds_and_voids"]["count_percentage"],
                    row["refunds_and_voids"]["amount_percentage"],
                    row["totals"]["transactions"],
                    row["totals"]["transactions_amount"],
                    row["totals"]["count_percentage"],
                    row["totals"]["amount_percentage"],
                ]
            )
        data = excel_file.getvalue()

        response = HttpResponse(excel_file.getvalue(), content_type="text/csv")
        response["Content-Type"] = "application/octet-stream"
        response["Content-Disposition"] = 'attachment;filename="temp_file.csv"'
        excel_file.close()

        return response

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=REPORTING_HUB_PARAMETERS,
    responses={
        HTTP_200_OK: getFlowBrandNameResponseSerializer,
        HTTP_400_BAD_REQUEST: GetDataUnsuccessfulResponseSerializer,
        HTTP_405_METHOD_NOT_ALLOWED: inValidMethodResponseSerializer,
    },
    auth=None,
    operation_id="Percentage Breakdown Metrics - Flow Title",
    tags=["Reporting Hub"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def downloadReportChartView(request: HttpRequest) -> HttpResponse:
    try:
        data = get_chart_view_data(request)
        excel_file = StringIO()
        csv_writer = csv.writer(excel_file)
        if len(data) > 0:
            csv_writer.writerow(
                [
                    "date",
                    "chargebacks",
                    "chargebacks_amount",
                    "refunds_and_voids",
                    "refunds_and_voids_amount",
                    "transactions",
                    "transactions_amount",
                    "chargebacks_rate",
                    "refunds_and_voids_rate",
                    "chargebacks_amount_rate",
                    "refunds_and_voids_amount_rate",
                ]
            )
        else:
            return JsonResponse({"message": "No data found"}, status=HTTP_200_OK)
        for row in data:
            csv_writer.writerow(
                [
                    row["date"],
                    row["chargebacks"],
                    row["chargebacks_amount"],
                    row["refunds_and_voids"],
                    row["refunds_and_voids_amount"],
                    row["transactions"],
                    row["transactions_amount"],
                    row["chargebacks_rate"],
                    row["refunds_and_voids_rate"],
                    row["chargebacks_amount_rate"],
                    row["refunds_and_voids_amount_rate"],
                ]
            )
        data = excel_file.getvalue()

        response = HttpResponse(excel_file.getvalue(), content_type="text/csv")
        response["Content-Type"] = "application/octet-stream"
        response["Content-Disposition"] = 'attachment;filename="temp_file.csv"'
        excel_file.close()

        return response

    except Exception as e:
        return JsonResponse(
            {"message": "Get data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )
