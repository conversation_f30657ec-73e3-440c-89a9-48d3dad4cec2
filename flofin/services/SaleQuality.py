import csv
import os
from datetime import datetime, timed<PERSON>ta
from io import String<PERSON>
from dotenv import load_dotenv

from django.db.models.query import QuerySet
from django.http import HttpRequest
from django.utils.dateparse import parse_date

import numpy as np
import pandas as pd
import xgboost as xgb
from django.db import transaction
from django.db.models import (
    Avg,
    <PERSON>,
    Char<PERSON>ield,
    Count,
    F,
    FloatField,
    IntegerField,
    Q,
    Value,
    When,
)
from django.db.models.functions import Cast, Concat
from django.db.models.query import QuerySet
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.utils.dateparse import parse_date
from django.views.decorators.csrf import csrf_exempt
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework.decorators import api_view
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST
from sklearn.impute import SimpleImputer
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import MinMaxScaler
from tqdm import tqdm

from data.models import ClientLoginInformation, ClientsInformation
from flofin.models import OrderImport, OrderUpdate
from flofin.services import TABLE_PARAMS, filterQueryAfter
from flofin.services.utils import getClientView
from user.utils.utils import require_login


load_dotenv()
ENVIRONMENT = os.getenv("ENVIRONMENT")


# API parameters for filtering
DAILY_SALES_RECAP_FILTER_PARAMS = [
    OpenApiParameter(
        name="flow_step_number[]",
        description="Flow Step - list (ex: [1,2,3])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="flow_brand_name[]",
        description="Flow Title - list (ex: ['abc','123'])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="card_brand[]",
        description="Card Brand - list (ex: ['visa','mastercard'])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="card_type[]",
        description="Card Type - list (ex: ['credit','debit'])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="clientID[]",
        description="Client ID - list (ex: [1,2,3])",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="start_date",
        description="Start Date - format: YYYY-MM-DD",
        required=False,
        type=str,
    ),
    OpenApiParameter(
        name="end_date",
        description="End Date - format: YYYY-MM-DD",
        required=False,
        type=str,
    ),
]


def getDataAfterFilter(
    request: HttpRequest, queryset: QuerySet[OrderImport]
) -> QuerySet[OrderImport]:
    """
    Apply filters to the queryset based on request parameters.

    Args:
        request: HTTP request containing filter parameters
        queryset: Base queryset to filter

    Returns:
        Filtered queryset
    """
    flow_brand_name = request.GET.getlist("flow_brand_name[]", None)
    flow_step_number = request.GET.getlist("flow_step_number[]", None)
    card_brand = request.GET.getlist("card_brand[]", None)
    card_type = request.GET.getlist("card_type[]", None)
    clientID = request.GET.getlist("clientID[]", None)
    start_date = request.GET.get("original_sale_start_date")
    end_date = request.GET.get("original_sale_end_date")
    cycle_num = request.GET.getlist("cycle_num[]", [])
    price_point = request.GET.getlist("price_point[]", [])
    network = request.GET.getlist("network[]", [])
    pub = request.GET.getlist("pub[]", [])

    lst_clients, permissions = getClientView(request)
    if "SUPER-PARENT" in permissions.split("_"):
        clientID = [lst_clients[0]["clientID"]]
        if clientID == ["demo_client"]:
            clientID = ["recVI1C8QhLuRmtXB"]

    if clientID:
        queryset = queryset.filter(client_login__listClientsUsed__in=clientID)
    if flow_step_number:
        queryset = queryset.filter(
            offer_product__offer_flow_step__flow_step_number__in=flow_step_number
        )
    if flow_brand_name:
        queryset = queryset.filter(flow_brand_name__in=flow_brand_name)
    if card_brand:
        queryset = queryset.filter(card_infor__card_brand__in=card_brand)
    if start_date:
        queryset = queryset.filter(transaction_date__gte=parse_date(start_date))
    if end_date:
        queryset = queryset.filter(transaction_date__lte=parse_date(end_date))
    if card_type:
        queryset = queryset.filter(card_infor__card_type__in=card_type)
    if cycle_num:
        queryset = queryset.filter(cycle_num_lookup__in=cycle_num)
    if price_point:
        queryset = queryset.filter(price_point__in=price_point)
    if network:
        queryset = queryset.filter(vendorid1__in=network)
    if pub:
        queryset = queryset.filter(vendorid2__in=pub)

    queryset = queryset.exclude(offer_product__offer_flow_step__flow__status="Deleted")
    return queryset


def get_order_import_queryset(request: HttpRequest) -> QuerySet[OrderImport]:
    """
    Retrieve the filtered queryset of OrderImport objects based on request parameters.

    Args:
        request: HTTP request containing filter parameters.

    Returns:
        Filtered queryset of OrderImport objects.
    """
    # Extract filter parameters from the request
    flow_step_number = request.GET.getlist("flow_step_number[]")
    card_brand = request.GET.getlist("card_brand[]")
    card_type = request.GET.getlist("card_type[]")
    client_id = request.GET.getlist("clientID[]")
    start_date = request.GET.get("original_sale_start_date")
    end_date = request.GET.get("original_sale_end_date")
    cycle_num = request.GET.getlist("cycle_num[]")
    price_point = request.GET.getlist("price_point[]")

    network = request.GET.getlist("network[]")
    pub = request.GET.getlist("pub[]")
    flow_brand_name = request.GET.getlist("flow_brand_name[]")

    # Retrieve client view and permissions
    lst_clients, permissions = getClientView(request)
    if "SUPER-PARENT" in permissions.split("_"):
        client_id = [lst_clients[0]["clientID"]]
        if client_id == ["demo_client"]:
            client_id = ["recVI1C8QhLuRmtXB"]

    # Define base filters
    base_filter = (
        Q(is_test=False)
        & Q(card_infor__isnull=False)
        & Q(vendorid1__isnull=False)
        & Q(score__isnull=False)
        & Q(retention__isnull=False)
        # & Q(retention_gt__isnull=False)
    )

    # Apply additional filters based on request parameters
    if client_id:
        base_filter &= Q(client_login__listClientsUsed__in=client_id)
    if flow_step_number:
        base_filter &= Q(
            offer_product__offer_flow_step__flow_step_number__in=flow_step_number
        )
    if card_brand:
        base_filter &= Q(card_infor__card_brand__in=card_brand)
    if start_date:
        base_filter &= Q(transaction_date__gte=parse_date(start_date))
    if end_date:
        base_filter &= Q(transaction_date__lte=parse_date(end_date))
    if card_type:
        base_filter &= Q(card_infor__card_type__in=card_type)
    if cycle_num:
        base_filter &= Q(cycle_num_lookup__in=cycle_num)
    if price_point:
        base_filter &= Q(price_point__in=price_point)
    if network:
        base_filter &= Q(vendorid1__in=network)
    if pub:
        base_filter &= Q(vendorid2__in=pub)

    # Optimize query by excluding unnecessary records and applying annotations
    order_imports = (
        OrderImport.objects.exclude(
            offer_product__offer_flow_step__flow__status="Deleted"
        )
        .filter(base_filter)
        .annotate(
            is_foreign=~Q(card_infor__issuer_country__iexact="united states"),
            cycle_number=F("cycle_num_lookup"),
            flow_brand_name=F("offer_product__offer_flow_step__flow__flow_brand_name"),
        )
        .select_related(
            "offer_product__offer_flow_step__flow_step_number",
            "card_infor_car_type",
        )
    )

    # Apply flow_brand_name filter if provided
    if flow_brand_name:
        order_imports = order_imports.filter(
            flow_brand_name__in=flow_brand_name, flow_brand_name__isnull=False
        )

    return order_imports


def get_all_order_imports(request: HttpRequest) -> QuerySet[OrderImport]:
    """
    Get all order imports with necessary annotations and filters.

    Args:
        request: HTTP request

    Returns:
        Filtered and annotated queryset of OrderImport objects
    """
    # Get the base queryset
    order_imports = get_order_import_queryset(request)

    lst_clients, permissions = getClientView(request)
    if "SUPER-PARENT" in permissions.split("_"):
        if lst_clients[0]["clientID"] == "demo_client":
            order_imports = order_imports.annotate(
                flow_brand_name=Case(
                    When(flow_brand_name="Ebook", then=Value("DemoProduct225")),
                    When(flow_brand_name="SS - 4 Bottle", then=Value("DemoProduct241")),
                    When(flow_brand_name="SS - 6 Bottle", then=Value("DemoProduct993")),
                    When(
                        flow_brand_name="SugarShield 2 Bottle",
                        then=Value("DemoProduct731"),
                    ),
                    When(flow_brand_name="KB - 6 Bottle", then=Value("DemoProduct743")),
                    When(flow_brand_name="KB - 2 Bottle", then=Value("DemoProduct367")),
                    When(flow_brand_name="KB 4 Bottle", then=Value("DemoProduct123")),
                    default=F("flow_brand_name"),
                    output_field=CharField(),
                ),
            )

    return order_imports


def getAllOrderUpdate(
    request: HttpRequest, all_order_imports: QuerySet[OrderImport]
) -> QuerySet[OrderUpdate]:
    """
    Get all order updates related to the given order imports.

    Args:
        request: HTTP request
        all_order_imports: Queryset of OrderImport objects

    Returns:
        Queryset of OrderUpdate objects
    """
    all_order_update = OrderUpdate.objects.filter(
        order_import__in=all_order_imports.values_list("id", flat=True)
    ).values_list("order_import", flat=True)

    return all_order_update


def scale_score(x):
    """
    Scale a score to a 0-100 range.

    Args:
        x: Score to scale

    Returns:
        Scaled score
    """
    return 100 * (x - 0) / (1 - 0)


def preprocess_data(df):
    """
    Preprocess data for model training.

    Args:
        df: DataFrame to preprocess

    Returns:
        Preprocessed DataFrame
    """
    df["transaction_date"] = pd.to_datetime(df["transaction_date"])

    df["year"] = df["transaction_date"].dt.year
    df["month"] = df["transaction_date"].dt.month
    df["day"] = df["transaction_date"].dt.day
    df["hour"] = df["transaction_date"].dt.hour
    df["minute"] = df["transaction_date"].dt.minute
    df["second"] = df["transaction_date"].dt.second
    df = df.drop(["transaction_date"], axis=1)

    df["vendorid1"].replace(
        df.vendorid1.unique(), range(0, len(df.vendorid1.unique())), inplace=True
    )

    df["price_point"].fillna(0, inplace=True)

    return df


def get_expect_retention_rate(retention_model, df):
    """
    Get expected retention rate using a trained model.

    Args:
        retention_model: Trained model
        df: DataFrame with features

    Returns:
        DataFrame with expected retention predictions
    """
    df = preprocess_data(df)

    df["expect_retention"] = retention_model.predict(df)
    return df


def get_fraud_score(fraud_model, df, root_vendorid1):
    """
    Get fraud score using a trained model.

    Args:
        fraud_model: Trained model
        df: DataFrame with features
        root_vendorid1: Original vendor ID

    Returns:
        DataFrame with fraud scores
    """
    df = preprocess_data(df)

    df["anomaly_score"] = fraud_model.score_samples(df)
    df["anomaly_score"] = df[["anomaly_score"]].apply(scale_score)
    df["anomaly_score"] = df["anomaly_score"].apply(lambda x: 100 - x)

    df["vendorid1"].replace(
        range(0, len(df.vendorid1.unique())), root_vendorid1, inplace=True
    )

    return df


def calculate_vendor_specific_retention(df):
    """
    Calculate retention rates specific to each vendor.

    Args:
        df: DataFrame with order data

    Returns:
        DataFrame with vendor-specific retention rates
    """
    # Create a copy of the original DataFrame to track the initial index
    df_with_original_index = df.reset_index(drop=False)

    # Create a function to calculate retention within a vendor group
    def calculate_retention_for_vendor(vendor_group):
        # Sort the vendor group by customer_id and cycle_number
        vendor_sorted = vendor_group.sort_values(["customer_id", "cycle_number"])

        # Group customers and their cycles
        customer_cycles = (
            vendor_sorted.groupby("customer_id")["cycle_number"]
            .apply(list)
            .reset_index()
        )

        # Determine retention for each customer
        retention_values = []
        for _, row in vendor_sorted.iterrows():
            customer = row["customer_id"]
            cycle = row["cycle_number"]

            if cycle is None:
                retention_values.append(0)
                continue

            # Find this customer's cycles
            customer_cycles_list = customer_cycles[
                customer_cycles["customer_id"] == customer
            ]["cycle_number"].values[0]

            # Check retention
            retention = 0
            for i in range(len(customer_cycles_list) - 1):
                if (
                    customer_cycles_list[i] == cycle
                    and customer_cycles_list[i + 1] == cycle + 1
                ):
                    retention = 1
                    break

            retention_values.append(retention)

        # Add retention column
        vendor_sorted["retention_gt"] = retention_values
        return vendor_sorted

    # Group by vendorid and apply retention calculation
    result_with_retention = df_with_original_index.groupby(
        "vendorid1", group_keys=False
    ).apply(calculate_retention_for_vendor)

    # Sort the result by the original index to restore the initial order
    result_sorted = result_with_retention.sort_values("index").drop(columns="index")

    return result_sorted


def set_fraud_label(order_import, order_fraud):
    """
    Set fraud labels based on order IDs.

    Args:
        order_import: DataFrame with order data
        order_fraud: Set of fraudulent order IDs

    Returns:
        DataFrame with fraud labels
    """
    order_import["fraud"] = order_import["id"].apply(
        lambda x: 1 if x in order_fraud else 0
    )

    return order_import


def UpdateFraudModel(
    start_date=(datetime.now() - timedelta(days=10)), end_date=datetime.now()
):
    """
    Update fraud model with recent data.

    Args:
        start_date: Start date for data collection
        end_date: End date for data collection
    """

    start_date = start_date.strftime("%Y-%m-%d")
    end_date = end_date.strftime("%Y-%m-%d")
    print(f"Start date: {start_date}, End date: {end_date}")
    ClientsInformation.objects.all()
    client_logins = ClientLoginInformation.objects.all()

    for client_login in client_logins:
        all_order_imports = OrderImport.objects.filter(
            client_login=client_login,
            transaction_date__gte=start_date,
            transaction_date__lte=end_date,
            is_test=False,
            # card_infor__isnull=False,
            vendorid1__isnull=False,
        )
        if all_order_imports.count() == 0:
            continue

        print(
            f"""Processing
                {client_login.id}
                {client_login.mainClientUsed}
                {client_login.loginPlatform}
                {client_login.loginType}
                - {all_order_imports.count()} orders
                - {datetime.now()}
            """
        )

        fields = [
            "id",
            "vendorid1",
            "customer_id",
            "price_point",
            "transaction_date",
            "cycle_num_lookup",
            "is_foreign",
        ]

        all_order_imports = all_order_imports.annotate(
            is_foreign=~Q(card_infor__issuer_country__iexact="united states"),
            cycle_number=F("cycle_num_lookup"),
            flow_brand_name=F("offer_product__offer_flow_step__flow__flow_brand_name"),
        )

        order_update = OrderUpdate.objects.filter(
            order_import__in=all_order_imports.values_list("id", flat=True)
        ).values_list("order_import", flat=True)

        order_update = set(order_update)
        df = pd.DataFrame.from_records(all_order_imports.values_list(*fields))

        df.rename(
            columns={
                0: fields[0],
                1: fields[1],
                2: fields[2],
                3: fields[3],
                4: fields[4],
                5: "cycle_number",
                6: fields[6],
            },
            inplace=True,
        )

        df["cycle_number"].fillna(0, inplace=True)

        df["vendorid1"] = df["vendorid1"].map(
            lambda x: "Standard)" if x == "standard)" else x
        )
        df = calculate_vendor_specific_retention(df)

        imputer = SimpleImputer(strategy="mean")
        df["price_point"] = imputer.fit_transform(df[["price_point"]])

        features_df = df[
            [
                "price_point",
                "transaction_date",
                "cycle_number",
                "is_foreign",
                "retention_gt",
            ]
        ]

        features_df["transaction_date"] = pd.to_datetime(
            features_df["transaction_date"]
        )

        features_df["year"] = features_df["transaction_date"].dt.year
        features_df["month"] = features_df["transaction_date"].dt.month
        features_df["day"] = features_df["transaction_date"].dt.day
        features_df["hour"] = features_df["transaction_date"].dt.hour
        features_df["minute"] = features_df["transaction_date"].dt.minute
        features_df["second"] = features_df["transaction_date"].dt.second
        features_df = features_df.drop(["transaction_date"], axis=1)

        ground_truth = features_df["retention_gt"]
        features_df.drop("retention_gt", axis=1, inplace=True)

        # Scale features
        scaler = MinMaxScaler(feature_range=(0, 100))
        features_df[features_df.columns] = scaler.fit_transform(
            features_df[features_df.columns]
        )

        # Train retention model
        retention_model = xgb.XGBClassifier()
        print(f"----> Train retention at {datetime.now()}")
        retention_model.fit(features_df, ground_truth)
        df["retention"] = retention_model.predict(features_df)

        # Train fraud model
        features_df["fraud"] = set_fraud_label(df, order_update)["fraud"]
        ground_truth = features_df["fraud"]
        features_df.drop("fraud", axis=1, inplace=True)

        # Impute missing values
        imputer = SimpleImputer(strategy="mean")
        features_df = pd.DataFrame(
            imputer.fit_transform(features_df), columns=features_df.columns
        )

        if np.sum(ground_truth) == 0:
            df["score"] = 0.0
        elif np.sum(ground_truth) == len(ground_truth):
            df["score"] = 100.0
        else:
            model = LogisticRegression()
            model.fit(features_df, ground_truth)
            predictions = model.predict_proba(features_df)
            df["score"] = predictions.T[1]
            df["score"] = df["score"].apply(scale_score).apply(lambda x: round(x, 3))

        # Update database
        print(f"----> Update db at {datetime.now()}")
        with transaction.atomic():
            for j in tqdm(range(len(df))):
                order = OrderImport.objects.get(id=df["id"].iloc[j])
                order.score = df["score"].iloc[j]
                order.retention = df["retention"].iloc[j]
                order.save()


def build_query(filters):
    where_clauses = [
        "NOT (ff.status = 'Deleted' AND ff.status IS NOT NULL)",
        "oi.is_test = 0",
        "oi.card_infor_id IS NOT NULL",
        "oi.vendorid1 IS NOT NULL",
        "oi.score IS NOT NULL",
        "oi.retention IS NOT NULL",
        "ff.flow_brand_name IS NOT NULL",
    ]

    if filters.get("listClientsUsed"):
        list_clients = ", ".join(f"'{c}'" for c in filters["listClientsUsed"])
        where_clauses.append(f"cl.listClientsUsed IN ({list_clients})")

    if filters.get("flow_step_number"):
        steps = ", ".join(str(s) for s in filters["flow_step_number"])
        where_clauses.append(f"ofs.flow_step_number IN ({steps})")

    if filters.get("card_brand"):
        brands = ", ".join(f"'{b}'" for b in filters["card_brand"])
        where_clauses.append(f"cb.card_brand IN ({brands})")

    if filters.get("transaction_date"):
        start, end = filters["transaction_date"]
        where_clauses.append(f"oi.transaction_date >= '{start}'")
        where_clauses.append(f"oi.transaction_date <= '{end}'")

    if filters.get("card_type"):
        types = ", ".join(f"'{t}'" for t in filters["card_type"])
        where_clauses.append(f"cb.card_type IN ({types})")

    if filters.get("price_point"):
        prices = ", ".join(str(p) for p in filters["price_point"])
        where_clauses.append(f"oi.price_point IN ({prices})")

    if filters.get("flow_brand_name"):
        names = ", ".join(f"'{n}'" for n in filters["flow_brand_name"])
        where_clauses.append(f"ff.flow_brand_name IN ({names})")

    where_clause = " AND\n        ".join(where_clauses)

    query = f"""
    SELECT
        MIN (oi.id) AS id,
        oi.vendorid1,
        oi.vendorid2,
        ff.flow_brand_name AS flow_brand_name,
        oi.vendorid1 AS TrafficSource,
        oi.vendorid2 AS Affiliate,
        ff.flow_brand_name AS FlowName,
        COUNT_BIG(
            CASE WHEN oi.cycle_num_lookup = 0 AND ofs.flow_step_number = 1 AND oi.order_status = 1
            THEN oi.order_status ELSE NULL END
        ) AS Sales,
        COUNT_BIG(
            CASE WHEN oi.cycle_num_lookup = 0 AND ofs.flow_step_number = 1 AND oi.order_status IS NOT NULL
            THEN oi.order_status ELSE NULL END
        ) AS SaleAttempts,
        ROUND(ISNULL((
            CASE WHEN COUNT_BIG(CASE WHEN oi.cycle_num_lookup = 0 AND ofs.flow_step_number = 1 AND oi.order_status IS NOT NULL
            THEN oi.order_status ELSE NULL END) = 0 THEN 0.0 ELSE
            CAST(COUNT_BIG(CASE WHEN oi.cycle_num_lookup = 0 AND ofs.flow_step_number = 1 AND oi.order_status = 1
            THEN oi.order_status ELSE NULL END) AS FLOAT) /
            CAST(COUNT_BIG(CASE WHEN oi.cycle_num_lookup = 0 AND ofs.flow_step_number = 1 AND oi.order_status IS NOT NULL
            THEN oi.order_status ELSE NULL END) AS FLOAT)
            END), 0.0), 3) AS InitialApprovalRate,
        ROUND(ISNULL((
            CASE WHEN COUNT_BIG(CASE WHEN oi.cycle_num_lookup = 0 AND ofs.flow_step_number = 1 AND oi.order_status IS NOT NULL
            THEN oi.order_status ELSE NULL END) = 0 THEN 0.0 ELSE
            CAST(COUNT_BIG(CASE WHEN oi.cycle_num_lookup = 0 AND NOT (UPPER(cb.issuer_country) = UPPER('united states')
            AND cb.issuer_country IS NOT NULL) AND ofs.flow_step_number = 1 AND oi.order_status IS NOT NULL
            THEN oi.order_status ELSE NULL END) AS FLOAT) /
            CAST(COUNT_BIG(CASE WHEN oi.cycle_num_lookup = 0 AND ofs.flow_step_number = 1 AND oi.order_status IS NOT NULL
            THEN oi.order_status ELSE NULL END) AS FLOAT)
            END), 0.0), 3) AS ForeignCardRatio,
        ROUND(ISNULL(AVG(CONVERT(FLOAT, COALESCE(oi.price_point, 0))), 0.0), 3) AS AvgAmount,
        ROUND(ISNULL(AVG(CONVERT(FLOAT, CASE WHEN oi.cycle_num_lookup > 0 AND oi.price_point IS NOT NULL
            THEN oi.price_point ELSE 0 END)), 0.0), 3) AS AvgRebillsAmount,
        ROUND(ISNULL(AVG(CONVERT(FLOAT, oi.retention)), 0.0), 3) AS ExpectedRebillsRate,
        ROUND(ISNULL(AVG(CONVERT(FLOAT, oi.retention_gt)), 0.0), 3) AS CurrentRebillsRate,
        ROUND(ISNULL(AVG(CONVERT(FLOAT, oi.score)), 0.0), 3) AS Score
    FROM FloFin_order_import oi
    LEFT OUTER JOIN FloFin_offer_product_detail opd ON oi.offer_product_id = opd.id
    LEFT OUTER JOIN FloFin_offer_flow_steps ofs ON opd.offer_flow_step_id = ofs.offer_flow_step_id
    LEFT OUTER JOIN FloFin_offer_flow ff ON ofs.flow_id = ff.flow_id
    INNER JOIN FloFin_card_bin_lookup cb ON oi.card_infor_id = cb.card_bin
    INNER JOIN DATA_ClientLogin cl ON oi.client_login_id = cl.id
    WHERE
        {where_clause}
    GROUP BY
        oi.vendorid1, oi.vendorid2, ff.flow_brand_name
    HAVING
        COUNT_BIG(
            CASE WHEN oi.cycle_num_lookup = 0 AND ofs.flow_step_number = 1 AND oi.order_status = 1
            THEN oi.order_status ELSE NULL END
        ) > 0
    ORDER BY SaleAttempts DESC
    OFFSET 0 ROWS;
    """

    return query


def get_request_filter(request: HttpRequest) -> dict:
    """
    Get request filter parameters with validation and sanitization.

    Args:
        request: HTTP request

    Returns:
        Dictionary of sanitized filter parameters
    """

    def sanitize_list(param_list):
        return [str(item).strip() for item in param_list if item]

    def sanitize_date(date_str):
        try:
            return str(parse_date(date_str)) if date_str else None
        except ValueError:
            return None

    filters = {
        "listClientsUsed": sanitize_list(request.GET.getlist("clientID[]", [])),
        "flow_step_number": sanitize_list(
            request.GET.getlist("flow_step_number[]", [])
        ),
        "card_brand": sanitize_list(request.GET.getlist("card_brand[]", [])),
        "transaction_date": [
            sanitize_date(request.GET.get("original_sale_start_date")),
            sanitize_date(request.GET.get("original_sale_end_date")),
        ],
        "card_type": sanitize_list(request.GET.getlist("card_type[]", [])),
        "price_point": [float(p) for p in request.GET.getlist("price_point[]", [])],
        "flow_brand_name": sanitize_list(request.GET.getlist("flow_brand_name[]", [])),
    }
    return filters


def get_fraud_table_data(request: HttpRequest, is_download=False):
    """
    Get fraud table data with aggregated metrics.

    Args:
        request: HTTP request
        is_download: Whether data is for download

    Returns:
        Tuple of (response data, paginator)
    """

    filters = get_request_filter(request)
    order_query = build_query(filters)
    order_imports_by_raw = OrderImport.objects.raw(order_query)

    is_demo = False
    lst_clients, permissions = getClientView(request)
    if "SUPER-PARENT" in permissions.split("_"):
        if lst_clients[0]["clientID"] == "demo_client":
            is_demo = True

    response_data = []

    for order in order_imports_by_raw:
        order_object = {}
        if is_demo:
            for key, value in order.__dict__.items():
                if key not in ["_state", "id"]:
                    if key == "TrafficSource":
                        if value == "Direct-Pub-306":
                            order_object["TrafficSource"] = "DirectSale"
                        elif value == "shopketobod.com – K2":
                            order_object["TrafficSource"] = "TestSite"
                        elif value == "getalphalabs.com -- 3ds":
                            order_object["TrafficSource"] = "DemoNetwork2"
                        elif value == "K2":
                            order_object["TrafficSource"] = "DemoNetwork1"
                        else:
                            order_object["TrafficSource"] = value
                    elif key == "Affiliate":
                        order_object["Affiliate"] = "DemoAff " + value if value else ""
                    else:
                        order_object[key] = value
                    response_data.append(order_object)
        else:
            for key, value in order.__dict__.items():
                if key not in ["_state", "id"]:
                    order_object[key] = value
            response_data.append(order_object)

    response_data, paginator = filterQueryAfter(
        request, response_data, is_download=is_download
    )

    return response_data, paginator


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS + TABLE_PARAMS,
    responses={
        HTTP_200_OK: "Get Traffic Source Table Success",
    },
    auth=None,
    operation_id="Fraud Reporting Report",
    tags=["Fraud Reporting"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getTrafficSourceTable(request: HttpRequest) -> JsonResponse:
    """
    API endpoint to get traffic source table data.

    Args:
        request: HTTP request

    Returns:
        JSON response with traffic source data
    """
    try:
        response_data, paginator = get_fraud_table_data(request)

        return JsonResponse(
            {
                "message": "Fraud Reporting data retrieved successfully.",
                "pagination": paginator,
                "data": response_data,
            },
            status=HTTP_200_OK,
        )
    except Exception as e:
        return JsonResponse(
            {"message": "Failed to retrieve Fraud Reporting data", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS + TABLE_PARAMS,
    responses={
        HTTP_200_OK: "Get Traffic Source Table Success",
    },
    auth=None,
    operation_id="Fraud Reporting Report",
    tags=["Fraud Reporting"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
@require_login
def downloadTrafficSourceTable(request: HttpRequest) -> HttpResponse:
    """
    API endpoint to download traffic source table data as CSV.

    Args:
        request: HTTP request

    Returns:
        HTTP response with CSV file
    """
    try:
        response_data, _ = get_fraud_table_data(request, is_download=True)
        excel_file = StringIO()
        csv_writer = csv.writer(excel_file)

        header_to_key = {
            "Flow Title": "flow_brand_name",
            "Network": "vendorid1",
            "Affiliate": "vendorid2",
            "Foreign Card Ratio": "ForeignCardRatio",
            "Sale Attempts": "SaleAttempts",
            "Sales": "Sales",
            "Initial Approval Rate": "InitialApprovalRate",
            "Expected Rebills Rate": "ExpectedRebillsRate",
            "Avg Amount": "AvgAmount",
            "Avg Rebills Amount": "AvgRebillsAmount",
            "Current Rebills Rate": "CurrentRebillsRate",
            "Fraud Score": "Score",
        }

        if len(response_data) > 0:
            header_keys = list(header_to_key.keys())
            csv_writer.writerow(header_keys)
            for row in response_data:
                csv_writer.writerow(
                    [row.get(header_to_key[key], "") for key in header_keys]
                )
        else:
            return JsonResponse({"message": "No data found"}, status=HTTP_200_OK)

        response_data = excel_file.getvalue()

        response = HttpResponse(excel_file.getvalue(), content_type="text/csv")
        response["Content-Type"] = "application/octet-stream"
        response["Content-Disposition"] = 'attachment;filename="temp_file.csv"'
        excel_file.close()

        return response

    except Exception as e:
        return JsonResponse(
            {"message": "Download data unsuccessfully", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS
    + [
        OpenApiParameter(
            name="traffic_source", description="TrafficSource", required=True, type=str
        ),
        OpenApiParameter(
            name="flow_brand_name", description="Flow Name", required=True, type=str
        ),
    ],
    responses={
        HTTP_200_OK: "Get Recommendation transactions Success",
    },
    auth=None,
    operation_id="Get Recommendation transactions",
    tags=["Fraud Reporting"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getRecommendationTransactions(request: HttpRequest) -> JsonResponse:
    """
    API endpoint to get recommendation transactions.

    Args:
        request: HTTP request

    Returns:
        JSON response with recommendation transactions
    """
    try:
        source = request.GET.get("traffic_source", None)
        flow = request.GET.get("flow_brand_name", None)
        if not source:
            return JsonResponse(
                {"message": "Please provide traffic_source"},
                status=HTTP_400_BAD_REQUEST,
            )
        if not flow:
            return JsonResponse(
                {"message": "Please provide flow_brand_name"},
                status=HTTP_400_BAD_REQUEST,
            )

        all_order_imports = get_all_order_imports(request)

        response_data = all_order_imports.filter(
            vendorid1__iexact=source, flow_brand_name__iexact=flow
        ).order_by("-score")[:5]

        response_data = pd.DataFrame(response_data.values())
        response_data = response_data[
            [
                "id",
                "order_id",
                "transaction_date",
                "price_point",
                "vendorid2",
                "card_infor_id",
                "card_last4",
                "mid",
                "customer_id",
                "retention",
                "score",
                "client_login_id",
            ]
        ]
        response_data[["price_point", "score"]] = response_data[
            ["price_point", "score"]
        ].apply(lambda x: round(x, 2))
        response_data["retention"] = response_data["retention"].apply(lambda x: int(x))
        response_data.rename(columns={"order_id": "transaction_id"}, inplace=True)

        response_data = response_data.to_dict(orient="records")
        for record in response_data:
            refund_status = "Not Refunded"
            blacklist_status = "Not blacklisted"
            record["transaction_date"] = record["transaction_date"].strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            order = OrderImport.objects.filter(id=record["id"]).first()
            if order:
                record["affiliate"] = order.vendorid2
            else:
                record["affiliate"] = None
            record["refund_status"] = refund_status
            record["blacklist_status"] = blacklist_status

        return JsonResponse(
            {
                "message": "Fraud Reporting data retrieved successfully.",
                "data": response_data,
            },
            status=HTTP_200_OK,
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Failed to retrieve Fraud Reporting data", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=[
        OpenApiParameter(
            name="id", description="Transaction ID", required=True, type=int
        ),
        OpenApiParameter(
            name="action", description="Action to perform", required=True, type=str
        ),
    ],
    responses={
        HTTP_200_OK: "Get Recommendation transactions Success",
    },
    auth=None,
    operation_id="Get Recommendation transactions",
    tags=["Fraud Reporting"],
)
@csrf_exempt
@api_view(["POST"])
@require_login
def postActionTransaction(request: HttpRequest) -> JsonResponse:
    """
    API endpoint to perform actions on transactions.

    Args:
        request: HTTP request

    Returns:
        JSON response with action result
    """
    try:
        data = request.data
        id = data.get("id", None)
        action = data.get("action", None)

        if not id or not action:
            return JsonResponse(
                {"message": "Please provide id and action"}, status=HTTP_400_BAD_REQUEST
            )
        if action not in ["refund", "blacklist"]:
            return JsonResponse(
                {"message": "Invalid action, please provide refund or blacklist"},
                status=HTTP_400_BAD_REQUEST,
            )
        order = OrderImport.objects.filter(id=id).first()
        if not order:
            return JsonResponse(
                {"message": "Transaction not found"}, status=HTTP_400_BAD_REQUEST
            )

        if action == "refund":
            order.refunded = 1
        else:
            order.blacklisted = 1
        order.save()
        return JsonResponse(
            {"message": "Action completed successfully"}, status=HTTP_200_OK
        )

    except Exception as e:
        return JsonResponse(
            {"message": "Failed to retrieve Fraud Reporting data", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    parameters=DAILY_SALES_RECAP_FILTER_PARAMS,
    responses={
        HTTP_200_OK: "Get Fraud Overview Success",
    },
    auth=None,
    operation_id="Get Fraud Overview",
    tags=["Fraud Reporting"],
)
@csrf_exempt
@api_view(["GET"])
@require_login
def getFraudOverview(request: HttpRequest) -> JsonResponse:
    """
    API endpoint to get fraud overview data.

    Args:
        request: HTTP request

    Returns:
        JSON response with fraud overview data
    """
    try:
        all_order_imports = get_all_order_imports(request)

        def calculate_metric(all_order_imports, metric):
            all_order_imports = all_order_imports.annotate(TrafficSource=F("vendorid1"))
            if ENVIRONMENT == "SANDBOX":
                all_order_imports = all_order_imports.annotate(
                    TrafficSource=Case(
                        When(TrafficSource="Direct-Pub-306", then=Value("DirectSale")),
                        When(
                            TrafficSource="shopketobod.com – K2", then=Value("TestSite")
                        ),
                        When(
                            TrafficSource="getalphalabs.com -- 3ds",
                            then=Value("DemoNetwork2"),
                        ),
                        When(TrafficSource="K2", then=Value("DemoNetwork1")),
                        default=F("TrafficSource"),
                        output_field=CharField(),
                    ),
                )
            result = all_order_imports.values("TrafficSource").annotate(
                avg_metric=Avg(metric)
            )
            formatted_result = [
                {"name": entry["TrafficSource"], "score": round(entry["avg_metric"], 2)}
                for entry in result
                if (entry["avg_metric"] is not None and entry["avg_metric"] > 0)
            ]
            return formatted_result, [entry["score"] for entry in formatted_result]

        retention_df, retention_scores = calculate_metric(
            all_order_imports, "retention"
        )
        score_df, score_scores = calculate_metric(all_order_imports, "score")

        return JsonResponse(
            {
                "message": "Fraud Reporting data retrieved successfully.",
                "data": {
                    "subscription_retention_forecast": {
                        "total": round(np.mean(retention_scores), 3)
                        if retention_scores
                        else 0,
                        "data": retention_df,
                    },
                    "traffic_source_score": {
                        "total": round(np.mean(score_scores), 3) if score_scores else 0,
                        "data": score_df,
                    },
                },
            },
            status=HTTP_200_OK,
        )
    except Exception as e:
        return JsonResponse(
            {"message": "Failed to retrieve Fraud Reporting data", "exception": str(e)},
            status=HTTP_400_BAD_REQUEST,
        )
