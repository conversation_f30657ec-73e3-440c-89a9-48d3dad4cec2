import os
import smtplib
from datetime import datetime
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText

from dotenv import load_dotenv

# ----------------------------------- Main Functions -----------------------------------#

load_dotenv()
SMTP_SERVER = os.getenv("SMTP_SERVER")
SMTP_PORT = int(os.getenv("SMTP_PORT"))
SMTP_USERNAME = os.getenv("SMTP_USERNAME")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")
SMTP_SENDERMAIL = os.getenv("SMTP_SENDERMAIL")
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def sendEmailAlertThreshold(data_message: dict = None, lst_recipients: list = None, subject: str = None):

    if subject is None:
        subject = f"[FLOFIN] Alert Threshold for {data_message['alert_name']} has been reached!"

    # logo_path = f'{BASE_DIR}/static/logo.png'

    html_file_path = os.path.join(BASE_DIR, "flofin", "source", "html", "email.html")
    with open(html_file_path, "r") as file:
        html = file.read()

    # Read SVG content from file
    svg_file_path = os.path.join(BASE_DIR, "static", "logo.svg")
    with open(svg_file_path, "r") as file:
        svg_content = file.read()

    # Insert SVG content into HTML
    html = html.replace("<!-- SVG_PLACEHOLDER -->", svg_content)
    html = html.replace("Client-Name", str(data_message["client"]))
    html = html.replace("Alert-Name", str(data_message["alert_name"]))
    html = html.replace("Alert-ID", str(data_message["alert_id"]))
    html = html.replace("Datetime-Now", datetime.now().strftime("%Y-%m-%d"))

    html = html.replace("Data-Service1", str(data_message["dimension"]))
    html = html.replace("Data-Service2", str(data_message["dimension_value"]))
    html = html.replace("Data-Service3", str(data_message["metric"]))
    html = html.replace("Data-Service4", str(data_message["condition"]))
    html = html.replace("Data-Service5", str(data_message["current_value"]))
    html = html.replace("Data-Service6", str(data_message["threshold"]))
    html = html.replace("Data-Service7", str(data_message["frequency"]))

    for recipient in lst_recipients:
        msg = MIMEMultipart("related")
        msg["From"] = SMTP_SENDERMAIL
        msg["To"] = recipient
        msg["Subject"] = subject

        msg.attach(MIMEText(html, "html"))

        # with open(logo_path, 'rb') as f:
        #     logo = MIMEImage(f.read(), _subtype=os.path.splitext(logo_path)[1][1:])
        #     logo.add_header('Content-ID', '<logo>')
        #     logo.add_header('Content-Disposition', 'attachment', filename=os.path.basename(logo_path))
        #     msg.attach(logo)

        try:
            with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as server:
                server.login(SMTP_SENDERMAIL, SMTP_PASSWORD)
                server.send_message(msg)
            print(f"Email sent for {recipient} have client {data_message['client']} successfully!")
        except Exception as e:
            print(f"Failed to send email: {e}")
