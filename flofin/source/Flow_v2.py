from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from datetime import datetime, timedelta

from tqdm import tqdm

from data.models import ClientLoginInformation
from django.db import connection
from django.db.models import Q, F
from flofin.models import (
    CardBinLookup,
    OfferFlow,
    OfferProductDetail,
    OrderImport,
    OrderUpdate,
)


class UpdateOrderStep1:
    """
    This class is used to update the OrderImport records with the following fields:
    - card_infor
    - order_import
    - g_updated

    """

    def __init__(
        self,
        start_date=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
        end_date=datetime.now().strftime("%Y-%m-%d"),
        client_login: ClientLoginInformation = None,
    ):
        self.start_date = datetime.strptime(start_date, "%Y-%m-%d")
        self.end_date = datetime.strptime(end_date, "%Y-%m-%d")
        self.chunk_size = 10000
        self.client_login = client_login

    def updateCard(self):
        if connection.connection is not None and not connection.is_usable():
            connection.close()
            connection.connect()

        orders_import = OrderImport.objects.filter(
            # is_test=0,
            card_infor__isnull=True,
            transaction_date__gte=self.start_date,
            transaction_date__lte=self.end_date,
        )

        print("Total records fetched: ", orders_import.count())
        if self.client_login:
            orders_import = orders_import.filter(
                client_login__internalLoginID=self.client_login.internalLoginID
            )

        print(f"Updated card:  {orders_import.count()} records")

        successful_card_bin_lookups = 0

        all_card_bin = CardBinLookup.objects.all()
        card_bin_dict = {cbl.card_bin: cbl for cbl in all_card_bin}

        updates = []
        for order in tqdm(orders_import):
            card_bin_number = order.cc_bin
            if card_bin_number in card_bin_dict:
                order.card_infor = card_bin_dict[card_bin_number]
                order.updated_at = datetime.now()
                updates.append(order)
                successful_card_bin_lookups += 1

        if updates:
            for i in tqdm(range(0, len(updates), self.chunk_size)):
                chunk = updates[i : i + self.chunk_size]
                OrderImport.objects.bulk_update(chunk, ["card_infor", "updated_at"])

        print(f" ------> Succeeded: {successful_card_bin_lookups}/{len(orders_import)}")

    def updateOrderUpdate(self):
        order_update_base_filter = (
            # Q(g_updated__isnull=True)
            # Q(update_date__gte=self.start_date)
            # & Q(update_date__lte=self.end_date)
            Q(order_import_id__isnull=True)
        )

        order_import_base_filter = Q(order_status=1)

        if self.client_login:
            print(f"Filtering orders for client login: {self.client_login.id}")
            order_update_base_filter &= Q(client_login_id=self.client_login.id)
            order_import_base_filter &= Q(client_login_id=self.client_login.id)

        orders_update = OrderUpdate.objects.filter(
            order_update_base_filter
        ).select_related("client_login")

        print("Total records fetched: ", orders_update.count())
        orders_import = OrderImport.objects.filter(order_import_base_filter)

        print(f"Total records fetched: {orders_import.count()}")

        order_update_success = 0
        updates = []
        for order in tqdm(orders_update):
            try:
                crm_type = order.crm_type

                print(
                    f"Processing order: {order.order_id} with CRM type: {crm_type}, product match: {order.product_match}"
                )

                ## crm_type was updated when implement get STICKY, in previous version it was not used
                if crm_type == "STICKY":
                    original_order_import = orders_import.filter(
                        order_id=order.order_id,
                        product_match=order.product_match,
                    )
                    print(
                        f"Sticky CRM type detected for order {order.order_id}, looking for original order import."
                    )
                    if original_order_import.exists():
                        order.order_import_id = original_order_import.first().id
                        print(
                            f"Found original order import for order {order.order_id}, setting order_import_id."
                        )
                else:
                    parent_id = order.parent_id
                    if parent_id:
                        original_order_import = orders_import.filter(
                            sub_order_id=order.order_id,
                            product_match=order.product_match,
                        )
                    else:
                        original_order_import = orders_import.filter(
                            order_id=order.order_id, product_match=order.product_match
                        )
                    if original_order_import.exists():
                        order.order_import_id = original_order_import.first().id

                order.g_updated = datetime.now()
                order.updated_at = datetime.now()
                updates.append(order)
                order_update_success += 1
                if order_update_success % 1000 == 0:
                    OrderUpdate.objects.bulk_update(
                        updates, ["order_import", "g_updated", "updated_at"]
                    )
                    updates = []
            except Exception as e:
                print("Error when update order update", order.order_id, e)

        OrderUpdate.objects.bulk_update(
            updates, ["order_import", "g_updated", "updated_at"]
        )
        print(f" ------> Succeeded: {order_update_success}/{len(orders_update)}")

    def run(self):
        self.updateCard()
        self.updateOrderUpdate()


class UpdateOrderStep2:
    def __init__(
        self,
        start_date=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
        end_date=datetime.now().strftime("%Y-%m-%d"),
        client_login: ClientLoginInformation = None,
        lst_flow: list[OfferFlow] = None,
    ):
        self.start_date = datetime.strptime(start_date, "%Y-%m-%d")
        self.end_date = datetime.strptime(end_date, "%Y-%m-%d")
        self.chunk_size = 10000
        self.client_login = client_login
        self.lst_flow = lst_flow

        if lst_flow:
            OFFER_PRODUCT_DETAIL = OfferProductDetail.objects.filter(
                offer_flow_step__flow__in=lst_flow,
                offer_flow_step__flow__client_login=client_login,
            )
        else:
            OFFER_PRODUCT_DETAIL = OfferProductDetail.objects.filter(
                offer_flow_step__flow__client_login=client_login
            )

        self.lst_all_product_match = OFFER_PRODUCT_DETAIL.values(
            "product_match"
        ).distinct()
        self.lst_all_product_match = [
            product["product_match"] for product in self.lst_all_product_match
        ]

    def assign_offer_product_to_order(self):
        related_orders = self._get_related_orders()
        customer_ids = self._get_customer_ids(related_orders)
        processing_flows = self._get_processing_flows()

        print(f"Number of flows to process: {len(processing_flows)}")

        for flow in processing_flows:
            print(f"Processing flow: {flow}")
            offer_product_details = self._get_offer_product_details(flow)
            last_step_number = offer_product_details.last().step_number
            for i in range(1, last_step_number + 1):
                step_products = self._get_step_products(offer_product_details, i)
                step_cycle_numbers = self._get_step_cycle_numbers(
                    offer_product_details, i
                )
                self._process_customers_for_step(
                    related_orders,
                    customer_ids,
                    step_products,
                    offer_product_details,
                    i,
                    step_cycle_numbers,
                )
            print(f"Finished processing flow: {flow}")

        print("Offer products assigned to orders successfully.")

    def _get_related_orders(self):
        related_order_filter = Q(
            transaction_date__gte=self.start_date,
            transaction_date__lte=self.end_date,
            product_match__in=self.lst_all_product_match,
            offer_product__isnull=True,
        )
        if self.client_login:
            related_order_filter &= Q(
                client_login__internalLoginID=self.client_login.internalLoginID
            )
        related_orders = OrderImport.objects.filter(related_order_filter).order_by(
            "transaction_date"
        )
        print(f"Related number of orders: {related_orders.count()}")
        return related_orders

    def _get_customer_ids(self, related_orders):
        customer_ids = related_orders.values_list("customer_id", flat=True).distinct()
        print(f"Number of unique customers: {len(customer_ids)}")
        return customer_ids

    def _get_processing_flows(self):
        if self.lst_flow:
            return self.lst_flow
        return OfferFlow.objects.filter(client_login=self.client_login, status="Active")

    def _get_offer_product_details(self, flow):
        return (
            OfferProductDetail.objects.filter(
                offer_flow_step__flow=flow,
            )
            .annotate(
                step_number=F("offer_flow_step__flow_step_number"),
            )
            .order_by("step_number", "cycle_number")
        )

    def _get_step_products(self, offer_product_details, step_number):
        return (
            offer_product_details.filter(step_number=step_number)
            .values("product_match")
            .distinct()
        )

    def _get_step_cycle_numbers(self, offer_product_details, step_number):
        return list(
            offer_product_details.filter(step_number=step_number).values_list(
                "cycle_number", flat=True
            )
        )

    def _process_single_customer_for_step(
        self,
        customer_id,
        step_products,
        step_number,
        step_cycle_numbers,
    ):
        # Close any existing connections to ensure thread safety
        if connection.connection is not None and not connection.is_usable():
            connection.close()
            connection.connect()

        try:
            # Re-query the data in this thread to avoid thread safety issues
            related_order_filter = Q(
                transaction_date__gte=self.start_date,
                transaction_date__lte=self.end_date,
                product_match__in=self.lst_all_product_match,
                offer_product__isnull=True,
                customer_id=customer_id,
            ) & Q(product_match__in=step_products)

            if self.client_login:
                related_order_filter &= Q(
                    client_login__internalLoginID=self.client_login.internalLoginID
                )

            orders = OrderImport.objects.filter(related_order_filter).order_by(
                "transaction_date"
            )
            orders = list(orders)

            if not orders:
                return

            # Get offer product details for this thread
            if self.lst_flow:
                offer_product_details = OfferProductDetail.objects.filter(
                    offer_flow_step__flow__in=self.lst_flow,
                    offer_flow_step__flow__client_login=self.client_login,
                )
            else:
                offer_product_details = OfferProductDetail.objects.filter(
                    offer_flow_step__flow__client_login=self.client_login
                )

            offer_product_details = offer_product_details.filter(
                offer_flow_step__flow_step_number=step_number,
            ).order_by("cycle_number")

            for idx, order in enumerate(orders):
                if idx < len(step_cycle_numbers):
                    cycle_number = step_cycle_numbers[idx]
                else:
                    cycle_number = step_cycle_numbers[-1]

                order.cycle_num_lookup = cycle_number
                order.offer_product = offer_product_details.filter(
                    cycle_number=cycle_number,
                    product_match=order.product_match,
                ).first()
                order.ancestor_id = orders[0].ancestor_id
                order.ancestor_date = orders[0].transaction_date
                order.ancestor_vendorid1 = orders[0].vendorid1
                order.ancestor_vendorid2 = orders[0].vendorid2
                order.save()

        except Exception as e:
            print(f"Error processing customer {customer_id}: {e}")
            # Re-raise to be caught by the main thread
            raise

    def _process_customers_for_step(
        self,
        related_orders,
        customer_ids,
        step_products,
        offer_product_details,
        step_number,
        step_cycle_numbers,
    ):
        # Convert customer_ids to list if it's a QuerySet
        customer_ids_list = list(customer_ids)

        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                executor.submit(
                    self._process_single_customer_for_step,
                    customer_id,
                    step_products,
                    step_number,
                    step_cycle_numbers,
                )
                for customer_id in customer_ids_list
            ]

            completed = 0
            errors = 0

            for future in tqdm(
                as_completed(futures),
                total=len(futures),
                desc=f"Processing step {step_number}",
            ):
                try:
                    future.result()  # This will raise any exception from the worker thread
                    completed += 1
                except Exception as e:
                    errors += 1
                    print(f"Task failed with error: {e}")

            print(
                f"Step {step_number} completed: {completed} successful, {errors} errors"
            )

    def run(self):
        self.assign_offer_product_to_order()
