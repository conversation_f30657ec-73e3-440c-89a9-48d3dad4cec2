import time

import requests
from tqdm import tqdm

from flofin.models import CardBinLookup, OrderImport


class CardBinProcessor:
    def __init__(self):
        self.allUnknownBins = self.fetchUnknownBins()
        self.succeeded = 0
        self.failed = 0

    def fetchUnknownBins(self):
        return list(
            OrderImport.objects.filter(card_infor__isnull=True).values_list("cc_bin", flat=True).distinct().order_by("-cc_bin")  # Sort by cc_bin in descending order
        )

    def fetchBinInfo(self, binlookup):
        api_url = f"https://data.handyapi.com/bin/{binlookup}"
        response = requests.get(api_url)
        return response.json() if response.status_code == 200 else None

    def processBin(self, binlookup):
        if CardBinLookup.objects.filter(card_bin=binlookup).exists():
            # print(f"BIN already exists: {binlookup}")
            return

        # print("Processing BIN:", binlookup)
        ccinfo = self.fetchBinInfo(binlookup)

        if ccinfo:
            card_bin = binlookup
            if card_bin is None:
                self.failed += 1
                return

            card_issuer_name = ccinfo.get("Issuer", "UNKNOWN")
            prepaid_card = int(ccinfo.get("prepaid", False))
            issuer_country = ccinfo.get("Country", [])
            if len(issuer_country) > 0:
                issuer_country = issuer_country.get("Name", "UNKNOWN").upper()

            card_brand = ccinfo.get("Scheme", "").upper() or (
                "VISA" if str(card_bin).startswith("4") else "MASTERCARD" if str(card_bin).startswith("5") else "DISCOVER" if str(card_bin).startswith("6") else "UNKNOWN"
            )
            card_type = ccinfo.get("Type", "UNKNOWN").upper()
            card_subtype = ccinfo.get("Scheme", "UNKNOWN").upper()

            if CardBinLookup.objects.create(
                card_bin=card_bin,
                card_issuer_name=card_issuer_name,
                prepaid_card=prepaid_card,
                issuer_country=issuer_country[:40],  # Truncate the value to fit the column size
                card_brand=card_brand,
                card_type=card_type,
                card_subtype=card_subtype,
                # end_col=0,
            ):
                self.succeeded += 1
                # print(f"Saved successfully: {self.succeeded}")
        else:
            self.failed += 1
            # print(f"Failed to fetch data for BIN: {binlookup}")

        time.sleep(3)
        # print(f"Progress: {self.succeeded + self.failed} out of {len(self.allUnknownBins)}")

    def run(self):
        print("Bins fetched:", len(self.allUnknownBins))
        for binlookup in tqdm(self.allUnknownBins):
            self.processBin(binlookup)
        print(f"Total succeeded: {self.succeeded}, Total failed: {self.failed}")
