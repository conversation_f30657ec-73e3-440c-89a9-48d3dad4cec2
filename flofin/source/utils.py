from datetime import datetime

from flofin.models import OfferFlow
from flofin.source.Flow_v2 import UpdateOrderStep1, UpdateOrderStep2
from flofin.source.SummaryTable import HomeCLVReportRun


def run_data_flow(client_login, start_date, end_date, lst_flow: list[OfferFlow] = None):
    if not lst_flow:
        print(f"Starting Step 1 at {datetime.now()}")
        UpdateOrderStep1(
            client_login=client_login, start_date=start_date, end_date=end_date
        ).run()
        print(f"Finished Step 1 at {datetime.now()}")
        print("--------------------")

    print(f"Starting Step 2 at {datetime.now()}")
    UpdateOrderStep2(
        client_login=client_login,
        start_date=start_date,
        end_date=end_date,
        lst_flow=lst_flow,
    ).run()
    print(f"Finished Step 2 at {datetime.now()}")
    print("--------------------")

    print(f"Starting Running REPORT Data at {datetime.now()}")
    HomeCLVReportRun(
        client_login=client_login,
        start_date=start_date,
        end_date=end_date,
        lst_flow=lst_flow,
    ).run()

    if lst_flow:
        OfferFlow.objects.filter(
            flow_id__in=[flow.flow_id for flow in lst_flow]
        ).update(status="Active")
    else:
        OfferFlow.objects.filter(client_login=client_login, status="Pending").update(
            status="Active"
        )

    print(f"Finished Running REPORT Data at {datetime.now()}")
