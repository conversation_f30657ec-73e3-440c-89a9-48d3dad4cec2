from datetime import datetime, timedelta

from django.core.exceptions import MultipleObjectsReturned
from django.db import transaction
from django.db.models import (
    Case,
    Count,
    DecimalField,
    F,
    IntegerField,
    Q,
    Sum,
    Value,
    When,
)
from django.db.models.functions import <PERSON><PERSON>ce, Concat, TruncDay
from tqdm import tqdm

from data.models import ClientLoginInformation
from flofin.models import HomeCLVReport, OfferFlow, OrderImport, OrderUpdate


class HomeCLVReportRun:
    def __init__(
        self,
        start_date=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
        end_date=datetime.now().strftime("%Y-%m-%d"),
        client_login: ClientLoginInformation = None,
        chunk_size=10000,
        is_reset_db=False,
        lst_flow: list[OfferFlow] = None,
    ):
        self.start_date = datetime.strptime(start_date, "%Y-%m-%d")
        self.end_date = datetime.strptime(end_date, "%Y-%m-%d")
        self.client_login = client_login
        self.lst_flow = lst_flow
        self.LIST_ISSUERS = [
            "CITIBANK N.A.",
            "BANK OF AMERICA, N.A.",
            "CHASE BANK USA, N.A.",
            "BANK OF AMERICA, NATIONAL ASSOCIATION",
            # "CITIBANK, N.A.",
            "FIFTH THIRD BANK, THE",
            # "BANCORP BANK, THE",
            # "BBVA USA",
            # "BANK OF MISSOURI, THE",
            "CAPITAL ONE BANK (USA), NATIONAL ASSOCIATION",
        ]
        self.CARD_BRANDS = ["VISA", "MASTERCARD", "DISCOVER", "AMEX"]
        self.CARD_TYPES = ["CREDIT", "DEBIT"]
        self.chunk_size = chunk_size
        self.is_reset_db = is_reset_db

    def makeOrderImportData(self):
        base_filter = Q(transaction_date__gte=self.start_date) & Q(
            transaction_date__lte=self.end_date
        )
        if self.client_login:
            base_filter &= Q(client_login_id=self.client_login.id)

        if self.lst_flow:
            base_filter &= Q(offer_product__offer_flow_step__flow__in=self.lst_flow)

        orders_import = OrderImport.objects.filter(base_filter)

        print(
            f"Filtering OrderImport data from {self.start_date} to {self.end_date}. with {self.client_login} and flows {self.lst_flow}"
        )

        print(f"OrdersImport data pre annotation: {orders_import.count()}")

        order_import_data = (
            orders_import.annotate(
                transaction_date_date=TruncDay("transaction_date"),
                ancestor_date_date=TruncDay("ancestor_date"),
                card_issuer=Case(
                    When(
                        card_infor__card_issuer_name__in=self.LIST_ISSUERS,
                        then=F("card_infor__card_issuer_name"),
                    ),
                    default=Value("Longtail"),
                ),
                is_foreign_card=Case(
                    When(
                        card_infor__issuer_country__iexact="united states",
                        then=Value(False),
                    ),
                ),
                card_brand_name=Case(
                    When(
                        card_infor__card_brand__in=self.CARD_BRANDS,
                        then=F("card_infor__card_brand"),
                    ),
                    default=Value("Other"),
                ),
                card_type_name=Case(
                    When(
                        card_infor__card_type__in=self.CARD_TYPES,
                        then=F("card_infor__card_type"),
                    ),
                    default=Value("Unknown"),
                ),
            )
            .values(
                "client_login__listClientsUsed",
                "client_login__mainClientUsed",
                "transaction_date_date",
                "ancestor_date_date",
                "ancestor_vendorid1",
                "ancestor_vendorid2",
                "offer_product__offer_flow_step__flow",
                "offer_product__offer_flow_step",
                "card_brand_name",
                "card_type_name",
                "card_issuer",
                "price_point",
                "cycle_num_lookup",
                "mid",
            )
            .annotate(
                network=F("ancestor_vendorid1"),
                pub=Concat(F("ancestor_vendorid1"), F("ancestor_vendorid2")),
                count_order=Count("id"),
                total_amount=Sum(
                    Case(
                        When(
                            order_status=1,
                            ancestor_date_date__isnull=False,
                            is_test=0,
                            then=F("price_point"),
                        ),
                        default=0,
                        output_field=DecimalField(),
                    )
                ),
                total_sales=Coalesce(
                    Sum("order_status", output_field=IntegerField(), default=0),
                    Value(0, output_field=IntegerField()),
                ),
                total_unique_sale=Sum(
                    Case(
                        When(
                            cycle_num_lookup=0,
                            offer_product__offer_flow_step__flow_step_number=1,
                            order_status=1,
                            is_test=0,
                            then=1,
                        ),
                        default=0,
                    ),
                    output_field=IntegerField(),
                    default=0,
                ),
                transactions=Sum("order_status"),
                transactions_amount=Sum("price_point"),
                total_3ds=Coalesce(
                    Sum("is_3ds_verified", output_field=IntegerField()),
                    Value(0, output_field=IntegerField()),
                ),
                total_foreign_card=Coalesce(
                    Sum(
                        Case(
                            When(is_foreign_card=True, then=1),
                            default=0,
                            output_field=IntegerField(),
                        )
                    ),
                    Value(0, output_field=IntegerField()),
                ),
            )
        )

        print(f"OrderImport data: {order_import_data.count()}")

        if self.is_reset_db:
            HomeCLVReport.objects.all().delete()
            for i in tqdm(range(0, len(order_import_data), self.chunk_size)):
                HomeCLVReport.objects.bulk_create(
                    [
                        HomeCLVReport(
                            clientID=data["client_login__listClientsUsed"],
                            client=data["client_login__mainClientUsed"],
                            ancestor_date=data["ancestor_date_date"],
                            transaction_date=data["transaction_date_date"],
                            network=data["network"],
                            pub=data["pub"],
                            merchantId=data["mid"],
                            cycle_num=data["cycle_num_lookup"],
                            flow_brand_id=data["offer_product__offer_flow_step__flow"],
                            flow_step_id=data["offer_product__offer_flow_step"],
                            card_brand=data["card_brand_name"],
                            card_type=data["card_type_name"],
                            card_issuer=data["card_issuer"],
                            total_foreign_card=data["total_foreign_card"],
                            price_point=data["price_point"],
                            count_order=data["count_order"],
                            post_tax_order_total=data["total_amount"],
                            total_sales=data["total_sales"],
                            total_unique_sale=data["total_unique_sale"],
                            total_3ds=data["total_3ds"],
                            transactions=data["transactions"],
                            transactions_amount=data["transactions_amount"],
                        )
                        for data in order_import_data[i : i + self.chunk_size]
                    ]
                )
            return

        for order in tqdm(order_import_data):
            try:
                HomeCLVReport.objects.update_or_create(
                    clientID=order["client_login__listClientsUsed"],
                    client=order["client_login__mainClientUsed"],
                    ancestor_date=order["ancestor_date_date"],
                    transaction_date=order["transaction_date_date"],
                    network=order["network"],
                    pub=order["pub"],
                    merchantId=order["mid"],
                    cycle_num=order["cycle_num_lookup"],
                    flow_brand_id=order["offer_product__offer_flow_step__flow"],
                    flow_step_id=order["offer_product__offer_flow_step"],
                    card_brand=order["card_brand_name"],
                    card_type=order["card_type_name"],
                    card_issuer=order["card_issuer"],
                    price_point=order["price_point"],
                    defaults={
                        "total_foreign_card": order["total_foreign_card"],
                        "count_order": order["count_order"],
                        "post_tax_order_total": order["total_amount"],
                        "total_sales": order["total_sales"],
                        "total_unique_sale": order["total_unique_sale"],
                        "total_3ds": order["total_3ds"],
                        "transactions": order["transactions"],
                        "transactions_amount": order["transactions_amount"],
                    },
                )
            # raise self.model.MultipleObjectsReturned
            except MultipleObjectsReturned:
                HomeCLVReport.objects.filter(
                    clientID=order["client_login__listClientsUsed"],
                    client=order["client_login__mainClientUsed"],
                    ancestor_date=order["ancestor_date_date"],
                    transaction_date=order["transaction_date_date"],
                    network=order["network"],
                    pub=order["pub"],
                    merchantId=order["mid"],
                    cycle_num=order["cycle_num_lookup"],
                    flow_brand_id=order["offer_product__offer_flow_step__flow"],
                    flow_step_id=order["offer_product__offer_flow_step"],
                    card_brand=order["card_brand_name"],
                    card_type=order["card_type_name"],
                    card_issuer=order["card_issuer"],
                    price_point=order["price_point"],
                ).delete()

        print(f"{len(order_import_data)} records copied to HomeCLVReport.")

    def makeOrderUpdateData(self):
        order_update_data = OrderUpdate.objects.filter(
            order_import__order_status=1,
            order_import__is_test=0,
        )
        if self.client_login:
            order_update_data = order_update_data.filter(
                order_import__client_login=self.client_login
            )
        if self.lst_flow:
            order_update_data = order_update_data.filter(
                order_import__offer_product__offer_flow_step__flow__in=self.lst_flow
            )

        print("OrderUpdate data pre-annotation:", order_update_data.count())

        order_update_data = (
            order_update_data.annotate(
                transaction_date_date=TruncDay("order_import__transaction_date"),
                ancestor_date_date=TruncDay("order_import__ancestor_date"),
                card_issuer=Case(
                    When(
                        order_import__card_infor__card_issuer_name__in=self.LIST_ISSUERS,
                        then=F("order_import__card_infor__card_issuer_name"),
                    ),
                    default=Value("Longtail"),
                ),
                is_foreign_card=Case(
                    When(
                        order_import__card_infor__issuer_country__iexact="united states",
                        then=Value(False),
                    ),
                ),
                card_brand_name=Case(
                    When(
                        order_import__card_infor__card_brand__in=self.CARD_BRANDS,
                        then=F("order_import__card_infor__card_brand"),
                    ),
                    default=Value("Other"),
                ),
                card_type_name=Case(
                    When(
                        order_import__card_infor__card_type__in=self.CARD_TYPES,
                        then=F("order_import__card_infor__card_type"),
                    ),
                    default=Value("Unknown"),
                ),
            )
            .values(
                "client_login__listClientsUsed",
                "client_login__mainClientUsed",
                "transaction_date_date",
                "ancestor_date_date",
                "order_import__offer_product__offer_flow_step__flow",
                "order_import__offer_product__offer_flow_step",
                "card_brand_name",
                "card_type_name",
                "order_import__mid",
                "card_issuer",
                "order_import__price_point",
                "order_import__cycle_num_lookup",
                "order_import__ancestor_vendorid1",
                "order_import__ancestor_vendorid2",
                "order_import__is_3ds_verified",
            )
            .annotate(
                network=F("order_import__ancestor_vendorid1"),
                pub=Concat(
                    F("order_import__ancestor_vendorid1"),
                    F("order_import__ancestor_vendorid2"),
                ),
            )
            .annotate(
                total_foreign_card=Coalesce(
                    Sum(
                        Case(
                            When(is_foreign_card=True, then=1),
                            default=0,
                            output_field=IntegerField(),
                        )
                    ),
                    Value(0, output_field=IntegerField()),
                ),
                total_3ds=Coalesce(
                    Sum("order_import__is_3ds_verified", output_field=IntegerField()),
                    Value(0, output_field=IntegerField()),
                ),
                refunds=Count("order_update_type", filter=Q(order_update_type=1)),
                voids=Count("order_update_type", filter=Q(order_update_type=2)),
                chargebacks=Count("order_update_type", filter=Q(order_update_type=3)),
                refunds_amount=Sum(
                    Case(
                        When(order_update_type=1, then=F("revenue_update")),
                        default=0,
                        output_field=DecimalField(),
                    )
                ),
                voids_amount=Sum(
                    Case(
                        When(order_update_type=2, then=F("revenue_update")),
                        default=0,
                        output_field=DecimalField(),
                    )
                ),
                chargebacks_amount=Sum(
                    Case(
                        When(order_update_type=3, then=F("revenue_update")),
                        default=0,
                        output_field=DecimalField(),
                    )
                ),
            )
        )

        print(f"OrderUpdate data: {order_update_data.count()}")

        if self.is_reset_db:
            for i in tqdm(range(0, len(order_update_data), self.chunk_size)):
                HomeCLVReport.objects.bulk_create(
                    [
                        HomeCLVReport(
                            clientID=data["client_login__listClientsUsed"],
                            client=data["client_login__mainClientUsed"],
                            transaction_date=data["transaction_date_date"],
                            ancestor_date=data["ancestor_date_date"],
                            network=data["network"],
                            pub=data["pub"],
                            flow_brand_id=data[
                                "order_import__offer_product__offer_flow_step__flow"
                            ],
                            flow_step_id=data[
                                "order_import__offer_product__offer_flow_step"
                            ],
                            card_brand=data["card_brand_name"],
                            card_type=data["card_type_name"],
                            card_issuer=data["card_issuer"],
                            total_foreign_card=data["total_foreign_card"],
                            price_point=data["order_import__price_point"],
                            merchantId=data["order_import__mid"],
                            cycle_num=data["order_import__cycle_num_lookup"],
                            total_3ds=data["total_3ds"],
                            refunds=data["refunds"],
                            voids=data["voids"],
                            chargebacks=data["chargebacks"],
                            refunds_amount=data["refunds_amount"],
                            voids_amount=data["voids_amount"],
                            chargebacks_amount=data["chargebacks_amount"],
                        )
                        for data in order_update_data[i : i + self.chunk_size]
                    ]
                )
            return

        for order in tqdm(order_update_data):
            try:
                HomeCLVReport.objects.update_or_create(
                    clientID=order["client_login__listClientsUsed"],
                    client=order["client_login__mainClientUsed"],
                    transaction_date=order["transaction_date_date"],
                    ancestor_date=order["ancestor_date_date"],
                    network=order["network"],
                    pub=order["pub"],
                    flow_brand_id=order[
                        "order_import__offer_product__offer_flow_step__flow"
                    ],
                    flow_step_id=order["order_import__offer_product__offer_flow_step"],
                    card_brand=order["card_brand_name"],
                    card_type=order["card_type_name"],
                    card_issuer=order["card_issuer"],
                    price_point=order["order_import__price_point"],
                    merchantId=order["order_import__mid"],
                    cycle_num=order["order_import__cycle_num_lookup"],
                    defaults={
                        "total_foreign_card": order["total_foreign_card"],
                        "total_3ds": order["total_3ds"],
                        "refunds": order["refunds"],
                        "voids": order["voids"],
                        "chargebacks": order["chargebacks"],
                        "refunds_amount": order["refunds_amount"],
                        "voids_amount": order["voids_amount"],
                        "chargebacks_amount": order["chargebacks_amount"],
                    },
                )
            except MultipleObjectsReturned:
                HomeCLVReport.objects.filter(
                    clientID=order["client_login__listClientsUsed"],
                    client=order["client_login__mainClientUsed"],
                    transaction_date=order["transaction_date_date"],
                    ancestor_date=order["ancestor_date_date"],
                    network=order["network"],
                    pub=order["pub"],
                    flow_brand_id=order[
                        "order_import__offer_product__offer_flow_step__flow"
                    ],
                    flow_step_id=order["order_import__offer_product__offer_flow_step"],
                    card_brand=order["card_brand_name"],
                    card_type=order["card_type_name"],
                    card_issuer=order["card_issuer"],
                    price_point=order["order_import__price_point"],
                    merchantId=order["order_import__mid"],
                    cycle_num=order["order_import__cycle_num_lookup"],
                ).delete()

        print(f"{len(order_update_data)} records copied to HomeCLVReport.")

    def run(self):
        print("Running HomeCLVReportRun at", datetime.now())
        with transaction.atomic():
            self.makeOrderImportData()
            self.makeOrderUpdateData()
        print("Finished Running HomeCLVReportRun at", datetime.now())
