from datetime import datetime, timedelta
import math
from dotenv import load_dotenv
import os

from django.core.management.base import BaseCommand
from django.db.models import Count
import requests
from tqdm import tqdm

from data.airtable import updateAirTable
from data.models import ClientLoginInformation, MerchantsInformation
from flofin.crm import knk, sticky
from flofin.models import OrderImport, OrderImportTemp, OrderUpdate
from flofin.services.SaleQuality import UpdateFraudModel
from flofin.source.CardBin import CardBinProcessor
from flofin.source.utils import run_data_flow
from flofin.services.ThresholdManage import triggerAlertThresholdJob

load_dotenv()

STICKY_BASE_URL = os.getenv("STICKY_BASE_URL")
STICKY_API_USER_NAME = os.getenv("STICKY_API_USER_NAME")
STICKY_API_PASSWORD = os.getenv("STICKY_API_PASSWORD")


def copy_ois():
    orders = OrderImport.objects.filter(
        is_test=False,
        order_status=1,
        transaction_date__range=[datetime.now() - timedelta(days=60), datetime.now()],
    ).values(
        "id",
        "client_login_id",
        "client_login__listClientsUsed",
        "is_test",
        "cycle_num_lookup",
        "transaction_date",
        "order_status",
        "payment_network",
        "merchantId",
        "mid",
        "offer_product__offer_flow_step",
        "offer_product__offer_flow_step__flow",
        "card_infor__card_brand",
        "card_infor__card_type",
    )
    order_temp_instances = []
    all_merchant_ids = MerchantsInformation.objects.all().values_list(
        "clientID", "GWID", "MID"
    )
    all_merchant_ids_dict = {}
    for client_id, gwid, mid in all_merchant_ids:
        if client_id not in all_merchant_ids_dict:
            all_merchant_ids_dict[client_id] = {gwid: mid}
        else:
            all_merchant_ids_dict[client_id][gwid] = mid

    def get_merchant_id(mid_crm, merchantId_crm, clientID):
        if merchantId_crm not in all_merchant_ids_dict[clientID]:
            # return mid from CRM
            return mid_crm
        else:
            # return mid from Airtable
            return all_merchant_ids_dict[clientID][merchantId_crm]

    for order_dict in orders:
        order_dict = {
            key: order_dict[key]
            for key in [
                "id",
                "client_login_id",
                "client_login__listClientsUsed",
                "is_test",
                "cycle_num_lookup",
                "transaction_date",
                "order_status",
                "payment_network",
                "merchantId",
                "mid",
                "offer_product__offer_flow_step",
                "offer_product__offer_flow_step__flow",
                "card_infor__card_brand",
                "card_infor__card_type",
            ]
        }
        order_dict["flow_step_id"] = order_dict.pop("offer_product__offer_flow_step")
        order_dict["flow_brand_id"] = order_dict.pop(
            "offer_product__offer_flow_step__flow"
        )
        order_dict["card_brand"] = order_dict.pop("card_infor__card_brand")
        order_dict["card_type"] = order_dict.pop("card_infor__card_type")
        order_dict["merchantId"] = get_merchant_id(
            order_dict["mid"],
            order_dict["merchantId"],
            order_dict["client_login__listClientsUsed"],
        )
        order_dict.pop("client_login__listClientsUsed")
        order_dict.pop("mid")
        order_temp_instances.append(OrderImportTemp(**order_dict))

    OrderImportTemp.objects.all().delete()
    batch_size = 1000
    for i in tqdm(range(0, len(order_temp_instances), batch_size)):
        try:
            OrderImportTemp.objects.bulk_create(
                order_temp_instances[i : i + batch_size]
            )
        except Exception as e:
            print(e)
            continue


def getKnk(start_date, end_date):
    client_logins = ClientLoginInformation.objects.filter(
        mainClientUsed="Artevo Marketing Inc",
        loginPlatform="Konnektive",
        loginType="API",
    )
    for client_login in client_logins:
        knk.Step1GetDataFromKnk(client_login=client_login).getDataKnk(
            start_date=start_date, end_date=end_date
        )
        run_data_flow(
            client_login=client_login, start_date=start_date, end_date=end_date
        )


def update_mid_for_sticky_orders():
    """
    Update MID for Sticky orders in OrderImport table.
    """
    print("Updating MID for Sticky orders in OrderImport table...")

    # Get all Sticky orders from OrderImport
    sticky_orders = (
        OrderImport.objects.filter(client_login_id=5841, is_test=False)
        .values("order_id")
        .distinct()
    )
    sticky_orders = [order["order_id"] for order in sticky_orders]
    total_page = math.ceil(len(sticky_orders) / 500)
    page = 0
    print(f"Total pages to process: {total_page}")
    while page < total_page:
        print(f"Processing page {page + 1}/{total_page}...")
        r = requests.post(
            STICKY_BASE_URL,
            auth=(STICKY_API_USER_NAME, STICKY_API_PASSWORD),
            json={"order_id": sticky_orders[(page * 500) : (page + 1) * 500]},
        )
        orders = r.json()
        list_orders = orders.get("data", {})
        if not list_orders:
            print(f"No orders found for page {page + 1}")
            page += 1
            continue
        for order_id, order in list_orders.items():
            gateway_id = order.get("gateway_id")
            if not gateway_id:
                print(f"Order {order_id} does not have GATEWAY_ID.")
                continue

            merchant_info = MerchantsInformation.objects.filter(
                client="Highquest Growth", CRMID1=gateway_id
            )
            if not merchant_info.exists():
                print(f"Merchant not found for GATEWAY_ID: {gateway_id}")
                continue

            merchant = merchant_info.first()
            mid = merchant.MID
            merchant_id = merchant.id
            OrderImport.objects.filter(
                order_id=order_id, client_login_id=5841, is_test=False
            ).update(
                mid=mid,
                merchantId=merchant_id,
            )
        page = page + 1

    print("MID update completed.")


def getSticky(start_date, end_date):
    client_logins = ClientLoginInformation.objects.filter(
        mainClientUsed="Highquest Growth",
        loginPlatform="Sticky",
        loginType="API",
        crmNumber=1,
    )
    print(f"Total Sticky client logins: {client_logins.count()}")
    for client_login in client_logins:
        sticky.Step1GetDataFromSticky(client_login=client_login).get_sticky_data(
            start_date=start_date, end_date=end_date
        )
        run_data_flow(
            client_login=client_login, start_date=start_date, end_date=end_date
        )


def refactor(start_date, end_date):
    print("Starting Refactor Duplicate Records")

    # Find all duplicate records
    all_duplicate_record = (
        OrderImport.objects.filter(
            transaction_date__gte=start_date, transaction_date__lte=end_date
        )
        .values(
            "order_id",
            "client_login__internalLoginID",
            "transaction_date",
            "sub_order_id",
            "order_status",
        )
        .annotate(order_id_count=Count("order_id"))
        .filter(order_id_count__gt=1)
    )

    print("Total Duplicate Records: ", all_duplicate_record.count())

    for record in tqdm(all_duplicate_record):
        duplicate_records = OrderImport.objects.filter(
            order_id=record["order_id"],
            client_login__internalLoginID=record["client_login__internalLoginID"],
            transaction_date=record["transaction_date"],
            sub_order_id=record["sub_order_id"],
            order_status=record["order_status"],
        ).order_by("id")

        # Keep the first record and delete the rest
        first_record = duplicate_records.first()
        duplicates_to_delete = duplicate_records.exclude(id=first_record.id)
        duplicates_to_delete.delete()

    print("*** Done ***")
    
def remove_dupliacate_order_update(start_date, end_date):
    print("Starting Refactor Duplicate Records")

    # Find all duplicate records
    all_duplicate_record = (
        OrderUpdate.objects.filter(
            update_date__gte=start_date, update_date__lte=end_date
        )
        .values(
            "order_id",
            "client_login__internalLoginID",
            "update_date",
            "sub_order_id",
            "order_update_type",
            "update_status",
            "product_match"
        )
        .annotate(order_id_count=Count("order_id"))
        .filter(order_id_count__gt=1)
    )

    print("Total Duplicate Records: ", all_duplicate_record.count())

    for record in tqdm(all_duplicate_record):
        duplicate_records = OrderUpdate.objects.filter(
            order_id=record["order_id"],
            client_login__internalLoginID=record["client_login__internalLoginID"],
            update_date=record["update_date"],
            sub_order_id=record["sub_order_id"],
            update_status=record["update_status"],
            order_update_type=record["order_update_type"],
            product_match=record["product_match"]
        ).order_by("id")

        # Keep the first record and delete the rest
        first_record = duplicate_records.first()
        duplicates_to_delete = duplicate_records.exclude(id=first_record.id)
        duplicates_to_delete.delete()

    print("*** Done ***")



class Command(BaseCommand):
    help = "test"

    def add_arguments(self, parser):
        # Add arguments
        parser.add_argument("action", type=str)
        parser.add_argument(
            "start_date",
            type=str,
            nargs="?",
            default=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
        )
        parser.add_argument(
            "end_date", type=str, nargs="?", default=datetime.now().strftime("%Y-%m-%d")
        )

    def handle(self, *args, **options):
        action = options["action"]

        start_date = options["start_date"]
        end_date = options["end_date"]
        print(
            f"Start job at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | Action: {action} | Start Date: {start_date} | End Date: {end_date}"
        )

        actions_dict = {
            ### CRM Luis
            "update_airtable": lambda: updateAirTable(),
            "card_bin": lambda: CardBinProcessor().run(),
            "copy_order_import": lambda: copy_ois(),
            "get_knk": lambda: getKnk(start_date=start_date, end_date=end_date),
            "get_sticky": lambda: getSticky(start_date=start_date, end_date=end_date),
            "refactor": lambda: refactor(start_date=start_date, end_date=end_date),
            "remove_dupliacate_order_update": lambda: remove_dupliacate_order_update(start_date=start_date, end_date=end_date),
            "update_fraud": lambda: UpdateFraudModel(
                start_date=(datetime.now() - timedelta(days=15)),
                end_date=datetime.now(),
            ),
            "trigger_alert_threshold": lambda: triggerAlertThresholdJob(),
            "update_mid_for_sticky_orders": lambda: update_mid_for_sticky_orders(),
        }

        if action in actions_dict:
            actions_dict[action]()
        else:
            self.stdout.write(self.style.ERROR("Action not exists"))
