# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

stages:
  - deploy

variables:
  PROD_BRANCH: main
  STAG_BRANCH: staging

deploy_staging:
  stage: deploy
  trigger:
    include: gitlab-ci-dev.yml
  rules:
    - if: '$CI_COMMIT_BRANCH == $STAG_BRANCH && $CI_PIPELINE_SOURCE == "push"'
      when: always
    - when: never

deploy_production:
  stage: deploy
  trigger:
    include: gitlab-ci-prod.yml
  rules:
    - if: '$CI_COMMIT_BRANCH == $PROD_BRANCH && $CI_PIPELINE_SOURCE == "push"'
      when: always
    - when: never
