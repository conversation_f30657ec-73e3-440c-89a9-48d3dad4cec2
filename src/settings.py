"""
Django settings for src project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
import warnings
from pathlib import Path

from dotenv import find_dotenv, load_dotenv

warnings.filterwarnings("ignore")

dotenv_file = find_dotenv()
load_dotenv(dotenv_file, override=True)

# add sentry
# import sentry_sdk
# sentry_sdk.init(
#     dsn=os.getenv("SENTRY_DSN"),
#     # Set traces_sample_rate to 1.0 to capture 100%
#     # of transactions for performance monitoring.
#     traces_sample_rate=1.0,
#     # Set profiles_sample_rate to 1.0 to profile 100%
#     # of sampled transactions.
#     # We recommend adjusting this value in production.
#     profiles_sample_rate=1.0,
# )


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-qq%b#0-1h#p$-3wn7t&1d2+n-a3-1+08zc)!o#8(ra&3xlt3ef"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["*"]

# #Staging
CRONJOBS = [
     ('20 * * * *', 'data.cron.Airtable.updateAirTable',  f'>> {os.path.join(BASE_DIR, "data/cron/log/Airtable.log")}'),
    ('0 * * * *', 'flofin.cron.threshold.trigger_alert_threshold', f'>> {os.path.join(BASE_DIR, "flofin/cron/log/threshold/alert.log")}')
 ]
CRONTAB_COMMENT = 'flofin-cron'

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "WARNING",
    },
    "loggers": {
        "django.server": {
            "handlers": ["console"],
            "level": "CRITICAL",
            "propagate": False,
        },
    },
}

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "user.apps.UserConfig",
    "data.apps.DataConfig",
    "flofin.apps.FlofinConfig",
    "rest_framework",
    "corsheaders",
    "drf_spectacular",
    "django_crontab",
    "django_cron",
    "django_celery_results",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

CORS_ALLOWED_ORIGINS = str(os.getenv("CORS_ALLOWED_ORIGINS")).split(",")
CORS_ORIGIN_WHITELIST = ["http://localhost:3000"]
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "accept-control-allow-methods",
    "access-control-allow-origin",
    "authorization",
    "content-security-policy",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]

CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]

ROOT_URLCONF = "src.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR, BASE_DIR.joinpath("templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "src.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": os.getenv("DB_ENGINE"),
        "NAME": os.getenv("DB_NAME"),
        "HOST": os.getenv("DB_HOST"),
        "PORT": os.getenv("DB_PORT"),
        "USER": os.getenv("DB_USER"),
        "PASSWORD": os.getenv("DB_PASSWORD"),
        "OPTIONS": {
            "driver": os.getenv("DB_DRIVER", "ODBC Driver 17 for SQL Server"),
            "isolation_level": "READ COMMITTED",
            "extra_params": "MARS_Connection=Yes;Encrypt=yes;TrustServerCertificate=yes",
        },
    },
    # "chargeback": {
    #     "ENGINE": os.getenv("CHARGEBACK_DB_ENGINE"),
    #     "NAME": os.getenv("CHARGEBACK_DB_NAME"),
    #     "HOST": os.getenv("CHARGEBACK_DB_HOST"),
    #     "PORT": os.getenv("CHARGEBACK_DB_PORT"),
    #     "USER": os.getenv("CHARGEBACK_DB_USER"),
    #     "PASSWORD": os.getenv("CHARGEBACK_DB_PASSWORD"),
    #     "OPTIONS": {
    #         "driver": os.getenv("CHARGEBACK_DB_DRIVER", "ODBC Driver 17 for SQL Server"),
    #         "isolation_level": "READ COMMITTED",
    #         "extra_params": "MARS_Connection=Yes;Encrypt=yes;TrustServerCertificate=yes",
    #     },
    # }
}
SESSION_COOKIE_AGE = 60
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

DATABASE_CONNECTION_POOLING = False

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{os.getenv('REDIS_HOST')}:{int(os.getenv('REDIS_PORT'))}/{os.getenv('REDIS_DB')}",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": f"{os.getenv('REDIS_PASSWORD')}",
        },
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    # other DRF settings here
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework.authentication.TokenAuthentication",
        "rest_framework.authentication.BasicAuthentication",
    ),
}

SPECTACULAR_SETTINGS = {
    "DESCRIPTION": """This is a FloFinals docs api. You can find more about the API at [RCVR API](https://api.flofin.myrcvr.com/).""",
    "TITLE": "CHARGE-BACK",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": False,
    "SERVE_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.TokenAuthentication"
    ],
    "SERVERS": [
        {"url": "https://api.flofin.myrcvr.com/", "description": "Production server"},
        {"url": "https://api.staging.flofin.myrcvr.com/", "description": "Demo server"},
        {"url": "http://localhost:8007/", "description": "Local server"},
    ],
    "CONTACT": {"name": "Phong Nguyen", "email": "", "url": ""},
    "LOGO": {
        "url": "https://github.com/ngoxuanphong/ENV/blob/main/src/Base/Durak/docs/Example.png",
        "backgroundColor": "#ffffff",
    },
    "LICENSE": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"},
    "TAGS": [
        {
            "name": "Authentication",
            "description": (
                "RCVR user Basic Authentication to get access token and refresh token "
                "when call Sign-in API then use access token to call other APIs"
            ),
        },
        {"name": "User", "description": "User operations"},
        # {"name": "Dashboard", "description": "Data operations"},
        # {"name": "Chargeback", "description": "Chargeback operations"},
        # {"name": "Alert", "description": "Alert operations"},
        # {"name": "Data", "description": """Data operations"""},
        # {"name": "Webhook", "description": """Webhook operations"""},
    ],
}


SWAGGER_SETTINGS = {
    "SECURITY_DEFINITIONS": {
        "api_key": {"type": "apiKey", "in": "header", "name": "Authorization"}
    },
    "DOC_EXPANSION": None,
    "APIS_SORTER": None,
    "OPERATIONS_SORTER": None,
    "JSON_EDITOR": False,
    "SHOW_REQUEST_HEADERS": False,
    "SUPPORTED_SUBMIT_METHODS": ["get", "post", "put", "delete", "patch"],
    "VALIDATOR_URL": "",
}

REDOC_SETTINGS = {
    "LAZY_RENDERING": False,
    "HIDE_HOSTNAME": False,
    "EXPAND_RESPONSES": "all",
    "PATH_IN_MIDDLE_PANEL": False,
    "SCROLL_Y_OFFSET": 50,
    "SCROLL_X_OFFSET": 50,
    "SHOW_EXTENSIONS": False,
    "SHOW_COMMON_EXTENSIONS": False,
    "REQUIRED_PROPS_FIRST": False,
    "SORT_PROPS_ALPHABETICALLY": False,
    "NO_AUTO_AUTH": False,
    "HIDE_DOWNLOAD_BUTTON": False,
    "HIDE_TOC": False,
    "HIDE_NAVIGATION_BAR": False,
    "NAVIGATION_BAR_BACKGROUND": "#ffffff",
    "NAVIGATION_BAR_COLOR": "#000000",
    "FOOTER": "",
    "FOOTER_MODE": "sticky",
    "DISABLE_SEARCH": False,
    "DISABLE_FILTER": False,
    "DISABLE_SORT": False,
    "THEME": "dark",
    "PATH_IN_MIDDLE_PANEL": False,
    "EXPAND_DEFAULT_SERVER_VARIABLES": False,
    "RESPONSES_TRAY": False,
    "SHOW_EXTENSIONS": False,
    "SHOW_COMMON_EXTENSIONS": False,
    "EXTENSIONS": [],
    "REQUIRED_PROPS_FIRST": False,
    "SORT_PROPS_ALPHABETICALLY": False,
    "NO_AUTO_AUTH": False,
    "HIDE_DOWNLOAD_BUTTON": False,
    "HIDE_TOC": False,
    "HIDE_NAVIGATION_BAR": False,
    "NAVIGATION_BAR_BACKGROUND": "#ffffff",
    "NAVIGATION_BAR_COLOR": "#000000",
    "FOOTER": "",
    "FOOTER_MODE": "sticky",
    "DISABLE_SEARCH": False,
    "DISABLE_FILTER": False,
    "DISABLE_SORT": False,
    "THEME": "dark",
    "PATH_IN_MIDDLE_PANEL": False,
    "EXPAND_DEFAULT_SERVER_VARIABLES": False,
    "RESPONSES_TRAY": False,
}

CELERY_BROKER_URL = f"redis://:{os.getenv('REDIS_PASSWORD')}@{os.getenv('REDIS_HOST')}:{os.getenv('REDIS_PORT')}/0"
CELERY_RESULT_BACKEND = f"redis://:{os.getenv('REDIS_PASSWORD')}@{os.getenv('REDIS_HOST')}:{os.getenv('REDIS_PORT')}/0"
