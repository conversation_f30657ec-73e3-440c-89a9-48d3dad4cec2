from datetime import datetime

from django.db import models
from django.utils import timezone


# ### ------------------------------------ Client, Merchant, Login Information ------------------------------------ ###
class ClientLoginInformation(models.Model):
    id = models.AutoField(primary_key=True)
    loginID = models.Char<PERSON>ield(max_length=128, null=True)
    internalLoginID = models.CharField(max_length=128, null=True)
    loginType = models.CharField(max_length=128, null=True)
    loginPlatform = models.CharField(max_length=128, null=True)
    crmNumber = models.CharField(max_length=128, null=True)

    validation = models.IntegerField(
        null=True
    )  # 0: not validated, 1: validated, 2: removed
    URL = models.Char<PERSON>ield(max_length=128, null=True)
    username = models.Char<PERSON><PERSON>(max_length=128, null=True)
    password = models.CharField(max_length=128, null=True)
    API_key = models.CharField(max_length=1000, null=True)
    sublytics_user_id = models.CharField(max_length=128, null=True)

    mainClientUsed = models.CharField(max_length=1024, null=True)
    clientIDMidigator = models.CharField(max_length=1024, null=True)
    clientIDVerifiDirect = models.CharField(max_length=1024, null=True)
    listClientsUsed = models.CharField(max_length=1024, null=True)

    api_status = models.IntegerField(
        null=False, default=1
    )  # 0: not running, 1: running
    cronjob_status = models.IntegerField(
        null=False, default=0
    )  # 0: not running, 1: running
    customer_cronjob_status = models.IntegerField(
        null=False, default=1
    )  # 0: not running, 1: running
    transaction_cronjob_status = models.IntegerField(
        null=False, default=1
    )  # 0: not running, 1: running
    chargeback_cronjob_status = models.IntegerField(
        null=False, default=1
    )  # 0: not running, 1: running
    ethoca_cronjob_status = models.IntegerField(
        null=False, default=1
    )  # 0: not running, 1: running
    rdr_cronjob_status = models.IntegerField(
        null=False, default=1
    )  # 0: not running, 1: running
    mcx_cronjob_status = models.IntegerField(
        null=False, default=1
    )  # 0: not running, 1: running
    cancel_subscription = models.IntegerField(
        null=False, default=0
    )  # 0: not running, 1: running

    cronjob_updated_by = models.CharField(max_length=128, null=True)
    created_utc = models.DateTimeField(default=timezone.now, null=True)
    updated_utc = models.DateTimeField(default=timezone.now, null=True)

    active = models.IntegerField(null=True, default=1)

    class Meta:
        db_table = "DATA_ClientLogin"


class ClientsInformation(models.Model):
    id = models.AutoField(primary_key=True)
    clientID = models.CharField(max_length=128, null=True)
    clientIDCBHelp = models.CharField(max_length=128, null=True)
    name = models.CharField(max_length=128, null=True)
    status = models.CharField(max_length=128, null=True)

    creationTime = models.DateTimeField(null=True)
    employeeEmails = models.CharField(max_length=1024, null=True)
    serviceLevel = models.CharField(max_length=128, null=True)
    internalTeam = models.CharField(max_length=128, null=True)
    chargebackService = models.CharField(max_length=500, null=True)

    created_utc = models.DateTimeField(default=timezone.now, null=True)
    updated_utc = models.DateTimeField(default=timezone.now, null=True)

    CB_active_date = models.DateTimeField(
        default=datetime(2024, 6, 1, 0, 0, 0), null=True
    )
    RDR_active_date = models.DateTimeField(
        default=datetime(2024, 6, 1, 0, 0, 0), null=True
    )
    Ethoca_active_date = models.DateTimeField(
        default=datetime(2024, 6, 1, 0, 0, 0), null=True
    )
    MCX_active_date = models.DateTimeField(
        default=datetime(2024, 10, 1, 0, 0, 0), null=True
    )

    verifi_CID = models.CharField(max_length=128, null=True)

    class Meta:
        db_table = "DATA_Client"


class MerchantsInformation(models.Model):
    id = models.AutoField(primary_key=True)
    merchantRecID = models.CharField(max_length=128, null=True)
    MID = models.CharField(max_length=512, null=True)
    alias = models.CharField(max_length=128, null=True)
    status = models.CharField(max_length=128, null=True)
    CRMID1 = models.CharField(max_length=128, null=True)
    CRMID2 = models.CharField(max_length=128, null=True)
    CRMID3 = models.CharField(max_length=128, null=True)
    CRMID4 = models.CharField(max_length=128, null=True)
    aliasSticky = models.CharField(max_length=128, null=True)
    aliasKonnektive = models.CharField(max_length=128, null=True)
    client = models.CharField(max_length=128, null=True)
    clientID = models.CharField(max_length=256, null=True)

    GWID = models.CharField(max_length=128, null=True)
    corp = models.CharField(max_length=256, null=True)
    processor = models.CharField(max_length=256, null=True)
    created_date = models.DateTimeField(null=True)
    descriptor = models.CharField(max_length=256, null=True)
    descriptor_cap = models.CharField(max_length=256, null=True)

    CAID = models.CharField(max_length=128, null=True)
    DBA = models.CharField(max_length=128, null=True)
    MCC = models.CharField(max_length=128, null=True)
    ARN = models.CharField(max_length=500, null=True)

    descriptor_number = models.CharField(max_length=128, null=True)
    descriptor_test = models.CharField(max_length=128, null=True)
    descriptor_alt = models.CharField(max_length=128, null=True)
    descriptor_alt2 = models.CharField(max_length=128, null=True)
    descriptor_alt3 = models.CharField(max_length=128, null=True)
    descriptor_alt4 = models.CharField(max_length=128, null=True)

    processor_URL = models.CharField(max_length=1000, null=True)
    processor_username = models.CharField(max_length=128, null=True)
    processor_password = models.CharField(max_length=128, null=True)
    processor_internal_login_id = models.CharField(max_length=128, null=True)

    processor_status = models.IntegerField(null=True)
    status_note = models.CharField(max_length=128, null=True)
    DWP_note = models.CharField(max_length=512, null=True)
    gateway_status = models.IntegerField(null=True)
    transacting = models.IntegerField(null=True)

    processor_api_key = models.CharField(max_length=1024, blank=True, null=True)
    processor_api_secret = models.CharField(max_length=1024, blank=True, null=True)

    sftp_paysafe_status = models.BooleanField(null=True, default=False)
    sftp_quantum_status = models.BooleanField(null=True, default=False)
    scraper_status = models.BooleanField(null=True, default=False)
    cbhelp_status = models.BooleanField(null=True, default=False)
    manual_status = models.BooleanField(null=True, default=False)
    processor_api_status = models.BooleanField(null=True, default=False)

    rdr_status = models.CharField(max_length=128, null=True)
    ethoca_status = models.CharField(max_length=128, null=True)
    ethoca_enrollment_date = models.DateTimeField(null=True)
    rdr_enrollment_date = models.DateTimeField(null=True)
    mcx_enrollment_date = models.DateTimeField(null=True)
    alert_tracker_note = models.CharField(max_length=1024, null=True)

    products = models.CharField(max_length=1024, null=True)

    created_utc = models.DateTimeField(default=timezone.now, null=True)
    updated_utc = models.DateTimeField(null=True)
    closed_date = models.DateTimeField(null=True)
    cb_reporting = models.BooleanField(null=True, default=False)
    latest_transaction_time = models.DateTimeField(null=True)

    class Meta:
        db_table = "DATA_Merchant"
