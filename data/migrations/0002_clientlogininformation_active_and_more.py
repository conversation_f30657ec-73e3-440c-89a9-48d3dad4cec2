# Generated by Django 5.0.8 on 2024-12-05 04:16

import datetime

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="clientlogininformation",
            name="active",
            field=models.IntegerField(default=1, null=True),
        ),
        migrations.AddField(
            model_name="clientlogininformation",
            name="mcx_cronjob_status",
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name="clientsinformation",
            name="MCX_active_date",
            field=models.DateTimeField(default=datetime.datetime(2024, 10, 1, 0, 0), null=True),
        ),
        migrations.AddField(
            model_name="clientsinformation",
            name="chargebackService",
            field=models.CharField(max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="clientsinformation",
            name="verifi_CID",
            field=models.Char<PERSON>ield(max_length=128, null=True),
        ),
        migrations.AddField(
            model_name="merchantsinformation",
            name="alert_tracker_note",
            field=models.CharField(max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name="merchantsinformation",
            name="cb_reporting",
            field=models.BooleanField(default=False, null=True),
        ),
        migrations.AddField(
            model_name="merchantsinformation",
            name="ethoca_enrollment_date",
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name="merchantsinformation",
            name="ethoca_status",
            field=models.CharField(max_length=128, null=True),
        ),
        migrations.AddField(
            model_name="merchantsinformation",
            name="latest_transaction_time",
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name="merchantsinformation",
            name="mcx_enrollment_date",
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name="merchantsinformation",
            name="products",
            field=models.CharField(max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name="merchantsinformation",
            name="rdr_enrollment_date",
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name="merchantsinformation",
            name="rdr_status",
            field=models.CharField(max_length=128, null=True),
        ),
        migrations.AlterField(
            model_name="merchantsinformation",
            name="MID",
            field=models.CharField(max_length=512, null=True),
        ),
    ]
