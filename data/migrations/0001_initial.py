# Generated by Django 5.0.8 on 2024-08-19 04:26

import datetime

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ClientLoginInformation",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("loginID", models.Char<PERSON><PERSON>(max_length=128, null=True)),
                ("internalLoginID", models.Char<PERSON>ield(max_length=128, null=True)),
                ("loginType", models.CharField(max_length=128, null=True)),
                ("loginPlatform", models.CharField(max_length=128, null=True)),
                ("crmNumber", models.Char<PERSON>ield(max_length=128, null=True)),
                ("validation", models.IntegerField(null=True)),
                ("URL", models.Char<PERSON>ield(max_length=128, null=True)),
                ("username", models.<PERSON><PERSON><PERSON><PERSON>(max_length=128, null=True)),
                ("password", models.Char<PERSON>ield(max_length=128, null=True)),
                ("API_key", models.CharField(max_length=1000, null=True)),
                ("sublytics_user_id", models.CharField(max_length=128, null=True)),
                ("mainClientUsed", models.CharField(max_length=1024, null=True)),
                ("clientIDMidigator", models.CharField(max_length=1024, null=True)),
                ("clientIDVerifiDirect", models.CharField(max_length=1024, null=True)),
                ("listClientsUsed", models.CharField(max_length=1024, null=True)),
                ("api_status", models.IntegerField(default=1)),
                ("cronjob_status", models.IntegerField(default=0)),
                ("customer_cronjob_status", models.IntegerField(default=1)),
                ("transaction_cronjob_status", models.IntegerField(default=1)),
                ("chargeback_cronjob_status", models.IntegerField(default=1)),
                ("ethoca_cronjob_status", models.IntegerField(default=1)),
                ("rdr_cronjob_status", models.IntegerField(default=1)),
                ("cancel_subscription", models.IntegerField(default=0)),
                ("cronjob_updated_by", models.CharField(max_length=128, null=True)),
                (
                    "created_utc",
                    models.DateTimeField(default=django.utils.timezone.now, null=True),
                ),
                (
                    "updated_utc",
                    models.DateTimeField(default=django.utils.timezone.now, null=True),
                ),
            ],
            options={
                "db_table": "DATA_ClientLogin",
            },
        ),
        migrations.CreateModel(
            name="ClientsInformation",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("clientID", models.CharField(max_length=128, null=True)),
                ("clientIDCBHelp", models.CharField(max_length=128, null=True)),
                ("name", models.CharField(max_length=128, null=True)),
                ("status", models.CharField(max_length=128, null=True)),
                ("creationTime", models.DateTimeField(null=True)),
                ("employeeEmails", models.CharField(max_length=1024, null=True)),
                ("serviceLevel", models.CharField(max_length=128, null=True)),
                ("internalTeam", models.CharField(max_length=128, null=True)),
                (
                    "created_utc",
                    models.DateTimeField(default=django.utils.timezone.now, null=True),
                ),
                (
                    "updated_utc",
                    models.DateTimeField(default=django.utils.timezone.now, null=True),
                ),
                (
                    "CB_active_date",
                    models.DateTimeField(default=datetime.datetime(2024, 6, 1, 0, 0), null=True),
                ),
                (
                    "RDR_active_date",
                    models.DateTimeField(default=datetime.datetime(2024, 6, 1, 0, 0), null=True),
                ),
                (
                    "Ethoca_active_date",
                    models.DateTimeField(default=datetime.datetime(2024, 6, 1, 0, 0), null=True),
                ),
            ],
            options={
                "db_table": "DATA_Client",
            },
        ),
        migrations.CreateModel(
            name="MerchantsInformation",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("merchantRecID", models.CharField(max_length=128, null=True)),
                ("MID", models.CharField(max_length=128, null=True)),
                ("alias", models.CharField(max_length=128, null=True)),
                ("status", models.CharField(max_length=128, null=True)),
                ("CRMID1", models.CharField(max_length=128, null=True)),
                ("CRMID2", models.CharField(max_length=128, null=True)),
                ("CRMID3", models.CharField(max_length=128, null=True)),
                ("CRMID4", models.CharField(max_length=128, null=True)),
                ("aliasSticky", models.CharField(max_length=128, null=True)),
                ("aliasKonnektive", models.CharField(max_length=128, null=True)),
                ("client", models.CharField(max_length=128, null=True)),
                ("clientID", models.CharField(max_length=256, null=True)),
                ("GWID", models.CharField(max_length=128, null=True)),
                ("corp", models.CharField(max_length=256, null=True)),
                ("processor", models.CharField(max_length=256, null=True)),
                ("created_date", models.DateTimeField(null=True)),
                ("descriptor", models.CharField(max_length=256, null=True)),
                ("descriptor_cap", models.CharField(max_length=256, null=True)),
                ("CAID", models.CharField(max_length=128, null=True)),
                ("DBA", models.CharField(max_length=128, null=True)),
                ("MCC", models.CharField(max_length=128, null=True)),
                ("ARN", models.CharField(max_length=500, null=True)),
                ("descriptor_number", models.CharField(max_length=128, null=True)),
                ("descriptor_test", models.CharField(max_length=128, null=True)),
                ("descriptor_alt", models.CharField(max_length=128, null=True)),
                ("descriptor_alt2", models.CharField(max_length=128, null=True)),
                ("descriptor_alt3", models.CharField(max_length=128, null=True)),
                ("descriptor_alt4", models.CharField(max_length=128, null=True)),
                ("processor_URL", models.CharField(max_length=1000, null=True)),
                ("processor_username", models.CharField(max_length=128, null=True)),
                ("processor_password", models.CharField(max_length=128, null=True)),
                (
                    "processor_internal_login_id",
                    models.CharField(max_length=128, null=True),
                ),
                ("processor_status", models.IntegerField(null=True)),
                ("status_note", models.CharField(max_length=128, null=True)),
                ("DWP_note", models.CharField(max_length=512, null=True)),
                ("gateway_status", models.IntegerField(null=True)),
                ("transacting", models.IntegerField(null=True)),
                ("processor_api_key", models.CharField(max_length=1000, null=True)),
                ("processor_api_secret", models.CharField(max_length=1000, null=True)),
                ("sftp_paysafe_status", models.BooleanField(default=False, null=True)),
                ("sftp_quantum_status", models.BooleanField(default=False, null=True)),
                ("scraper_status", models.BooleanField(default=False, null=True)),
                ("cbhelp_status", models.BooleanField(default=False, null=True)),
                ("manual_status", models.BooleanField(default=False, null=True)),
                ("processor_api_status", models.BooleanField(default=False, null=True)),
                (
                    "created_utc",
                    models.DateTimeField(default=django.utils.timezone.now, null=True),
                ),
                ("updated_utc", models.DateTimeField(null=True)),
                ("closed_date", models.DateTimeField(null=True)),
            ],
            options={
                "db_table": "DATA_Merchant",
            },
        ),
    ]
