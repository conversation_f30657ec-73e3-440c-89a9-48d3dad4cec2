import os
from datetime import datetime

from django.utils import timezone
from dotenv import find_dotenv, load_dotenv
from pyairtable import Api, Table

from data.models import ClientLoginInformation, ClientsInformation, MerchantsInformation

# -------------------------------------------- Main Functions --------------------------------------------

load_dotenv(find_dotenv())

AIRTABLE_PERSONAL_ACCESS_TOKEN = os.getenv("AIRTABLE_PERSONAL_ACCESS_TOKEN")
AIRTABLE_DATABASE_ID = os.getenv("AIRTABLE_DATABASE_ID")
API = Api(AIRTABLE_PERSONAL_ACCESS_TOKEN)


def updateInternalLoginID():

    crms_ids = (
        ClientLoginInformation.objects.filter(loginType="API", internalLoginID__isnull=True, username__isnull=False, URL__isnull=False, password__isnull=False)
        .exclude(loginPlatform="29 Next")
        .values("username", "password", "URL")
        .distinct()
    )

    for crm_id in crms_ids:
        crm = ClientLoginInformation.objects.filter(
            loginType="API", username=crm_id["username"], password=crm_id["password"], URL=crm_id["URL"], internalLoginID__isnull=True
        ).first()

        ext_crm = ClientLoginInformation.objects.filter(
            loginType="API", username=crm_id["username"], password=crm_id["password"], URL=crm_id["URL"], internalLoginID__isnull=False
        ).first()
        if ext_crm is not None:
            ClientLoginInformation.objects.filter(
                username=crm.username, password=crm.password, loginType=crm.loginType, loginPlatform=crm.loginPlatform, URL=crm.URL, internalLoginID__isnull=True
            ).update(internalLoginID=ext_crm.internalLoginID, updated_utc=datetime.now())
        else:
            ClientLoginInformation.objects.filter(
                username=crm.username, password=crm.password, loginType=crm.loginType, loginPlatform=crm.loginPlatform, URL=crm.URL, internalLoginID__isnull=True
            ).update(internalLoginID=crm.loginID, updated_utc=datetime.now())

    crms_ids = (
        ClientLoginInformation.objects.filter(loginType="API", internalLoginID__isnull=True, API_key__isnull=False, URL__isnull=False)
        .exclude(loginPlatform="29 Next")
        .values("API_key", "URL")
        .distinct()
    )

    for crm_id in crms_ids:
        crm = ClientLoginInformation.objects.filter(loginType="API", API_key=crm_id["API_key"], URL=crm_id["URL"], internalLoginID__isnull=True).first()

        ext_crm = ClientLoginInformation.objects.filter(loginType="API", API_key=crm_id["API_key"], URL=crm_id["URL"], internalLoginID__isnull=False).first()
        if ext_crm is not None:
            ClientLoginInformation.objects.filter(
                API_key=crm.API_key, loginType=crm.loginType, loginPlatform=crm.loginPlatform, URL=crm.URL, internalLoginID__isnull=True
            ).update(internalLoginID=ext_crm.internalLoginID, updated_utc=datetime.now())
        else:
            ClientLoginInformation.objects.filter(
                API_key=crm.API_key, loginType=crm.loginType, loginPlatform=crm.loginPlatform, URL=crm.URL, internalLoginID__isnull=True
            ).update(internalLoginID=crm.loginID, updated_utc=datetime.now())


def getAlertStausData():
    try:
        table = Table(AIRTABLE_PERSONAL_ACCESS_TOKEN, AIRTABLE_DATABASE_ID, "RCVR Master")
        merchants = table.all(fields=["MID", "RDR Status", "Ethoca Status"])

        update_count = 0
        for mc in merchants:
            fields = mc.get("fields", {})
            mid = fields.get("MID")
            if mid:
                updated = MerchantsInformation.objects.filter(MID=mid).update(
                    rdr_status=fields.get("RDR Status"), ethoca_status=fields.get("Ethoca Status"), updated_utc=timezone.now()
                )
                update_count += updated

        print(f"===> Successfully update {update_count} merchants' alert status")
        return True

    except Exception as e:
        print(f"Error getting and updating alert status from Airtable: {e}")
        return False


def getClientsData():
    table = API.table(AIRTABLE_DATABASE_ID, "Clients")
    clients = table.all(view="RCVR_dev_view", sort=["Client Name"])

    lst_client_removed = (
        ClientsInformation.objects.all()
        .exclude(
            clientID__in=[cl["id"] for cl in clients],
        )
        .exclude(status="Removed")
    )
    for cl in lst_client_removed:
        print("Removed: ", cl.clientID, cl.name)
        cl.status = "Removed"
        cl.save()

    lst_clientID = ClientsInformation.objects.all().values_list("clientID", flat=True)
    lst_client_new = [cl for cl in clients if cl["id"] not in lst_clientID]
    if len(lst_client_new) > 0:
        print(f"Import {len(lst_client_new)} New Clients")

    for cl in clients:
        if "fields" in cl.keys():
            fields = cl["fields"]
            ClientsInformation.objects.update_or_create(
                clientID=str(cl["id"]),
                defaults={
                    "status": fields["Account Status"] if "Account Status" in fields.keys() else None,
                    "clientID": cl["id"] if "id" in cl.keys() else None,
                    "chargebackService": fields.get("Chargeback Service Level"),
                    "name": fields["Client Name"] if "Client Name" in fields.keys() else None,
                    "creationTime": fields["Creation Time"] if "Creation Time" in fields.keys() else None,
                    "employeeEmails": "\n".join(fields["Employee Emails"]) if "Employee Emails" in fields.keys() else None,
                    "serviceLevel": fields["Service Level"] if "Service Level" in fields.keys() else None,
                    "internalTeam": fields["Internal Team"] if "Internal Team" in fields.keys() else None,
                    "updated_utc": timezone.now(),
                    "verifi_CID": fields["Verifi CID"] if "Verifi CID" in fields.keys() else None,
                },
            )
        else:
            ClientsInformation.objects.create(
                clientID=cl["id"] if "id" in cl.keys() else None,
                name=None,
                status=None,
            )

    return True


def getClientsLoginData():
    table = API.table(AIRTABLE_DATABASE_ID, "Client CRM Logins")
    client_logins = table.all(view="RCVR_dev_view", sort=["Client Formula"])

    lst_client_login_removed = (
        ClientLoginInformation.objects.all()
        .exclude(
            loginID__in=[cl["id"] for cl in client_logins],
        )
        .exclude(validation=2)
    )
    for cl in lst_client_login_removed:
        print("Removed Account: ", cl.loginID, cl.username)
        cl.validation = 2
        cl.save()

    lst_client_loginID = ClientLoginInformation.objects.all().values_list("loginID", flat=True)
    new_apis = 0
    new_crms = 0
    for cl in client_logins:
        fields = cl["fields"]
        if cl.get("id") not in lst_client_loginID:
            if fields.get("Platform", "") == "API":
                new_apis += 1
            else:
                new_crms += 1
        try:
            # if fields['Username'] is None or fields['Password'] is None or fields['URL'] is None:
            #     continue
            defaults = {
                "loginType": fields["Type"] if "Type" in fields.keys() else None,
                "loginPlatform": fields["Platform"] if "Platform" in fields.keys() else None,
                "crmNumber": fields["CRM Number"] if "CRM Number" in fields.keys() else None,
                "URL": fields["URL"] if "URL" in fields.keys() else None,
                "validation": int(fields["Validation"] == "OK") if "Validation" in fields.keys() else None,
                "username": str(fields.get("Username")).replace(" ", "").replace("\t", "").replace("\n", "") if fields.get("Username") else None,
                "password": str(fields.get("Password")).replace(" ", "").replace("\t", "").replace("\n", "") if fields.get("Password") else None,
                "API_key": fields["API Key"] if "API Key" in fields.keys() else None,
                "mainClientUsed": fields["Client Formula"] if "Client Formula" in fields.keys() else None,
                "clientIDMidigator": fields["Client ID MIdigator"] if "Client ID MIdigator" in fields.keys() else None,
                "listClientsUsed": "\n".join(fields["Client"]) if "Client" in fields.keys() else None,
                "clientIDVerifiDirect": fields["Verifi CID"] if "Verifi CID" in fields.keys() else None,
                "updated_utc": timezone.now(),
            }
            ClientLoginInformation.objects.update_or_create(loginID=str(cl["id"]), defaults=defaults)
        except Exception as e:
            print(e)
            continue
    if new_crms + new_apis > 0:
        print(f"Import {new_crms} New CRM and {new_apis} New API Accounts")
    updateInternalLoginID()

    return True


def getMerchantsData():
    table = API.table(AIRTABLE_DATABASE_ID, "RCVR Master")
    fields = [
        "MID",
        "Descriptor",
        "Corp",
        "ISO/Processor",
        "Descriptor Caps",
        "⚡️ Created",
        "MID Alias",
        "RCVR Status",
        "CRM Account ID 1",
        "CRM Account ID 2",
        "CRM Account ID 3",
        "CRM Account ID 4",
        # 'MID Alias (Sticky)', 'MID Alias (Konnektive)',
        "Client",
        "⚡️ Client (for Automations)",
        "CAID",
        "DBA",
        "MCC",
        "ARN",
        "numbertestdescriptor (without space)",
        "test descriptor",
        "Descriptor Alt",
        "Descriptor Alt2",
        "Descriptor Alt 3",
        "Descriptor Alt 4",
        "Processor Portal URL",
        "Processor Portal Username",
        "Processor Portal Password",
        # 'API Key (Consumer)',
        "API Key (Secret)",
        "Closed Date",
        "Products",
        "Ethoca Enrollment Confirm Date",
        "RDR Submitted Date",
    ]
    try:
        merchants = table.all(view="RCVR_dev_view", sort=["⚡️ Client (for Automations)"], fields=fields)
    except:
        merchants = table.all(view="RCVR_dev_view", sort=["⚡️ Client (for Automations)"])

    lst_merchantID = MerchantsInformation.objects.all().values_list("merchantRecID", flat=True)
    lst_merchant_new = [mc for mc in merchants if mc["id"] not in lst_merchantID]
    if len(lst_merchant_new) > 0:
        print(f"===> Import {len(lst_merchant_new)} New Merchants")

    for mc in merchants:
        fields = mc["fields"]
        defaults = {
            "MID": fields["MID"] if "MID" in fields.keys() else None,
            "alias": (fields["MID Alias"] if "MID Alias" in fields.keys() else None)
            or (fields["MID Alias (Sticky)"] if "MID Alias (Sticky)" in fields.keys() else None)
            or (fields["MID Alias (Konnektive)"] if "MID Alias (Konnektive)" in fields.keys() else None),
            "status": fields["RCVR Status"] if "RCVR Status" in fields.keys() else None,
            "CRMID1": (fields["CRM Account ID 1"] if fields["CRM Account ID 1"].isdigit() else None) if "CRM Account ID 1" in fields.keys() else None,
            "CRMID2": (fields["CRM Account ID 2"] if fields["CRM Account ID 2"].isdigit() else None) if "CRM Account ID 2" in fields.keys() else None,
            "CRMID3": (fields["CRM Account ID 3"] if fields["CRM Account ID 3"].isdigit() else None) if "CRM Account ID 3" in fields.keys() else None,
            "CRMID4": (fields["CRM Account ID 4"] if fields["CRM Account ID 4"].isdigit() else None) if "CRM Account ID 4" in fields.keys() else None,
            # 'aliasSticky': fields['MID Alias (Sticky)'] if 'MID Alias (Sticky)' in fields.keys() else None,
            # 'aliasKonnektive': fields['MID Alias (Konnektive)'] if 'MID Alias (Konnektive)' in fields.keys() else None,
            "client": fields["⚡️ Client (for Automations)"] if "⚡️ Client (for Automations)" in fields.keys() else None,
            "clientID": fields["Client"][0] if "Client" in fields.keys() else None,
            "corp": fields["Corp"] if "Corp" in fields.keys() else None,
            "processor": fields["ISO/Processor"] if "ISO/Processor" in fields.keys() else None,
            "created_date": fields["⚡️ Created"] if "⚡️ Created" in fields.keys() else None,
            "GWID": "/".join(fields[f"CRM Account ID {i}"] for i in range(1, 5) if f"CRM Account ID {i}" in fields.keys() and fields[f"CRM Account ID {i}"] is not None),
            "descriptor_cap": fields["Descriptor Caps"] if "Descriptor Caps" in fields.keys() else None,
            "descriptor": fields["Descriptor"] if "Descriptor" in fields.keys() else None,
            "CAID": str(fields["CAID"]).replace(" ", "").replace("\t", "").replace("\n", "") if "CAID" in fields.keys() else None,
            "DBA": fields["DBA"] if "DBA" in fields.keys() else None,
            "MCC": fields["MCC"] if "MCC" in fields.keys() else None,
            "ARN": str(fields["ARN"]).replace(" ", "").replace("\t", ",").replace("\n", ",") if "ARN" in fields.keys() else None,
            "descriptor_number": fields["numbertestdescriptor (without space)"] if "numbertestdescriptor (without space)" in fields.keys() else None,
            "descriptor_test": fields["test descriptor"] if "test descriptor" in fields.keys() else None,
            "descriptor_alt": fields["Descriptor Alt"] if "Descriptor Alt" in fields.keys() else None,
            "descriptor_alt2": fields["Descriptor Alt2"] if "Descriptor Alt2" in fields.keys() else None,
            "descriptor_alt3": fields["Descriptor Alt 3"] if "Descriptor Alt 3" in fields.keys() else None,
            "descriptor_alt4": fields["Descriptor Alt 4"] if "Descriptor Alt 4" in fields.keys() else None,
            "processor_URL": fields["Processor Portal URL"] if "Processor Portal URL" in fields.keys() else None,
            "processor_username": fields["Processor Portal Username"] if "Processor Portal Username" in fields.keys() else None,
            "processor_password": fields["Processor Portal Password"] if "Processor Portal Password" in fields.keys() else None,
            # 'processor_internal_login_id': fields['Processor ID'] if 'Processor ID' in fields.keys() else None,
            "processor_api_key": fields["API Key (Consumer)"] if "API Key (Consumer)" in fields.keys() else None,
            "processor_api_secret": fields["API Key (Secret)"] if "API Key (Secret)" in fields.keys() else None,
            "products": fields.get("Products"),
            "ethoca_enrollment_date": fields["Ethoca Enrollment Confirm Date"] if "Ethoca Enrollment Confirm Date" in fields.keys() else None,
            "rdr_enrollment_date": fields["RDR Submitted Date"] if "RDR Submitted Date" in fields.keys() else None,
            # 'mcx_enrollment_date': fields['MCX Enrollment Date'] if 'MCX Enrollment Date' in fields.keys() else None, #Update later
            "updated_utc": timezone.now(),
            "closed_date": fields["Closed Date"] if "Closed Date" in fields.keys() else None,
        }
        MerchantsInformation.objects.update_or_create(merchantRecID=str(mc["id"]), defaults=defaults)
    return True


def updateAirTable():
    today = datetime.now()
    print(f">>>> DateTime: {today.day}/{today.month}/{today.year} - {today.hour}:{today.minute}:{today.second}")
    print("=====================================================================================================")
    print("Getting Clients Data")
    getClientsData()
    print("=====================================================================================================")
    print("Getting Clients Login Data")
    getClientsLoginData()
    print("=====================================================================================================")
    print("Getting Merchants Data")
    getMerchantsData()
    print("=====================================================================================================")
