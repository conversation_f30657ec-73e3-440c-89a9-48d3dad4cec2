from django.db import models
from django.utils import timezone

from data.models import ClientsInformation


class GlobalConfig(models.Model):
    id = models.AutoField(primary_key=True)
    config_key = models.CharField(max_length=128, null=True)
    config_value = models.TextField(null=True)

    class Meta:
        db_table = "global_config"


class Roles(models.Model):
    id = models.AutoField(primary_key=True)
    role_name = models.CharField(max_length=128, null=True)
    role_description = models.TextField(null=True)

    class Meta:
        db_table = "Draft_user_role"


class Campaign(models.Model):
    id = models.AutoField(primary_key=True)
    campaign_name = models.CharField(max_length=256, null=True)
    campaign_description = models.TextField(null=True)
    created_utc = models.DateTimeField(default=timezone.now)
    updated_utc = models.DateTimeField(null=True)

    class Meta:
        db_table = "Draft_user_campaign_dba"


class Levels(models.Model):
    id = models.AutoField(primary_key=True)
    level_name = models.CharField(max_length=128, null=True)
    level_description = models.TextField(null=True)

    class Meta:
        db_table = "Draft_user_level"


class Users(models.Model):

    first_name = models.CharField(max_length=256, null=True)
    last_name = models.CharField(max_length=256, null=True)
    id = models.AutoField(primary_key=True)
    user_name = models.CharField(max_length=256, null=True)
    pwd_sha256 = models.CharField(max_length=64, null=True)
    email = models.CharField(max_length=256, null=True)
    gender = models.CharField(max_length=64, null=True)
    created_utc = models.DateTimeField(default=timezone.now)
    updated_utc = models.DateTimeField(null=True)
    status_code = models.CharField(max_length=50, null=True)
    last_login = models.DateTimeField(null=True)
    sign_up_type = models.CharField(max_length=50, null=True)
    sign_up_social_data = models.TextField(null=True)
    user_social_id = models.TextField(null=True)
    first_login = models.BooleanField(default=False)
    production = models.BooleanField(default=1)
    sandbox = models.BooleanField(default=1)

    api_key = models.TextField(null=True)

    level = models.ForeignKey(Levels, on_delete=models.CASCADE)
    role = models.ForeignKey(Roles, on_delete=models.CASCADE)
    # campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, null=True)
    passcode = models.CharField(max_length=64, null=True)
    status_account = models.CharField(max_length=64, null=True)
    company = models.CharField(max_length=64, null=True)
    phone = models.CharField(max_length=15, blank=True, null=True)
    chargeback_id = models.IntegerField(null=True)

    class Meta:
        db_table = "Draft_user"


class ClientParent(models.Model):
    id = models.AutoField(primary_key=True)
    client = models.ForeignKey(ClientsInformation, on_delete=models.CASCADE)
    user = models.ForeignKey(Users, on_delete=models.CASCADE)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, null=True)

    class Meta:
        db_table = "Draft_user_client_parent"


class StatusAccountLog(models.Model):

    id = models.AutoField(primary_key=True)
    user_name = models.CharField(null=True, max_length=256)
    approved_by = models.CharField(null=True, max_length=256)
    before_action = models.CharField(null=True, max_length=256)
    after_action = models.CharField(null=True, max_length=256)
    time = models.DateTimeField(default=timezone.now)
    note = models.CharField(null=True, max_length=1024)
    is_create_key_action = models.BooleanField(null=True, default=False)

    class Meta:
        db_table = "DRAFT_StatusAccountLog"
