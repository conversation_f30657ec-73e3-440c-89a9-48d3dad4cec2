import csv
import logging
import os
from datetime import datetime
from enum import Enum
from io import String<PERSON>
from types import SimpleNamespace

from django.core.paginator import Paginator
from django.db.models import Q
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from dotenv import find_dotenv, load_dotenv
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_404_NOT_FOUND,
    HTTP_405_METHOD_NOT_ALLOWED,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from user.models import Campaign, ClientParent, ClientsInformation, Levels, Roles, Users
from user.serializers import (
    LevelSerializer,
    SignIn400ResponseSerializer,
    SignIn401ResponseSerializer,
    SignIn500ResponseSerializer,
    SignInResponseSerializer,
    SignInSerializer,
    UserGetSerializers,
    UserSerializers,
    UsersSerializers,
    changePasswordResponseSerializer,
    changePasswordSerializer,
    createUserRequestSerializer,
    createUserResponseSerializer,
    deleteUserResponseSerializer,
    forgotPasswordUserResponseSerializer,
    forgotPasswordUserSerializer,
    getListUserResponseSerializer,
    refreshTokenUserResponseSerializer,
    updateProfileResponseSerializer,
    updateProfileSerializer,
    updateUserRequestSerializer,
    updateUserResponseSerializer,
    userInfoResponseSerializer,
    userLogoutResponseSerializer,
)
from user.utils.email import generate_password, send_mail_forgot_password
from user.utils.redis_utils import (
    add_access_token_to_redis,
    adding_failed_login_user,
    adding_token_to_refresh,
    checking_login_failed,
    delete_access_token_from_redis,
    delete_count_login_failed,
    delete_token_to_refresh,
)
from user.utils.utils import (
    check_password,
    encode_to_sha256,
    generate_bearer_token,
    generate_bearer_token_with_time,
    generateCustomerApiKey,
    get_user_permissions,
    require_login,
    require_login_admin,
    require_token_refresh,
    validate_email,
    validate_password,
)


class EClientStatus(Enum):
    Active = "Active"
    Integration = "Integration"
    Onboarding = "Onboarding"
    Pre_ONB = "Pre-ONB"
    Closed = "Closed"
    Removed = "Removed"
    Paused = "Paused"


CLIENT_STATUS = ["Active", "Integration", "Onboarding"]
dotenv_file = find_dotenv()
load_dotenv(dotenv_file, override=True)
ENV = os.getenv("ENVIRONMENT")
logger = logging.getLogger(__name__)
REFRESH_TOKEN_TTL_MINUTES = int(os.getenv("REFRESH_TOKEN_TTL_MINUTES"))


@extend_schema(
    request=SignInSerializer,
    responses={
        200: SignInResponseSerializer,
        400: SignIn400ResponseSerializer,
        401: SignIn401ResponseSerializer,
        500: SignIn500ResponseSerializer,
    },
    auth=None,
    operation_id="getToken",
    tags=["User"],
    operation=None,
)
@csrf_exempt
@api_view(["GET"])
def getToken(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        try:
            serializer = SignInSerializer(data=request.data)
            if not serializer.is_valid():
                return JsonResponse(serializer.errors, status=HTTP_400_BAD_REQUEST)

            username = serializer.validated_data.get("username")
            password = serializer.validated_data.get("password")
            checkPassValidate = validate_password(password)
            if checkPassValidate is not None:
                return JsonResponse({"message": checkPassValidate}, status=HTTP_400_BAD_REQUEST)
            myUser = Users.objects.filter(user_name=username).first()
            if not myUser:
                return JsonResponse(
                    {"message": "User Name or Password is incorrect"},
                    status=HTTP_401_UNAUTHORIZED,
                )

            if not check_password(myUser.pwd_sha256, password):
                return JsonResponse(
                    {"message": "User Name or password is incorrect"},
                    status=HTTP_400_BAD_REQUEST,
                )

            if myUser.status_code != "ACTIVATE":
                return JsonResponse(
                    {"message": "Your account has been deactivated."},
                    status=HTTP_500_INTERNAL_SERVER_ERROR,
                )

            permissions = get_user_permissions(myUser)
            token = generate_bearer_token(myUser.id, myUser.user_name, permissions)
            refresh_token = generate_bearer_token_with_time(
                {"userId": myUser.id, "type": "REFRESH_TOKEN"},
                REFRESH_TOKEN_TTL_MINUTES,
            )
            adding_token_to_refresh(myUser, refresh_token)
            add_access_token_to_redis(myUser, token)
            
            data = {
                "message": "Generate token successfully",
                "data": {
                    "access_token": token,
                },
            }
            return JsonResponse(data, status=HTTP_200_OK)
        except Exception:
            return JsonResponse(
                {"message": "Generate token unsuccessfully."},
                status=HTTP_400_BAD_REQUEST,
            )
    return JsonResponse(
        {"message": "Invalid request method. Only GET requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    request=SignInSerializer,
    responses={
        200: SignInResponseSerializer,
        400: SignIn400ResponseSerializer,
        401: SignIn401ResponseSerializer,
        500: SignIn500ResponseSerializer,
    },
    auth=None,
    operation_id="signIn",
    tags=["User"],
    operation=None,
)
@csrf_exempt
@api_view(["POST"])
def signIn(request: HttpRequest) -> JsonResponse:
    if request.method == "POST":
        # try:
        serializer = SignInSerializer(data=request.data)
        if not serializer.is_valid():
            return JsonResponse(serializer.errors, status=HTTP_400_BAD_REQUEST)

        username = serializer.validated_data.get("username")
        password = serializer.validated_data.get("password")
        checkPassValidate = validate_password(password)
        if checkPassValidate is not None:
            return JsonResponse({"message": checkPassValidate}, status=HTTP_400_BAD_REQUEST)
        myUser = Users.objects.filter(user_name=username).first()
        if not myUser:
            return JsonResponse(
                {"message": "User Name or Password is incorrect"},
                status=HTTP_401_UNAUTHORIZED,
            )

        if checking_login_failed(myUser):
            return JsonResponse(
                {
                    "message": "You have inputted the wrong password 5 times in a row. \
                                Your account has been locked for 30 minutes."
                },
                status=HTTP_400_BAD_REQUEST,
            )

        if ENV == "PRODUCTION" and not myUser.production:
            adding_failed_login_user(myUser)
            return JsonResponse(
                {"message": "User Name or password is incorrect"},
                status=HTTP_400_BAD_REQUEST,
            )

        if ENV == "SANDBOX" and not myUser.sandbox:
            adding_failed_login_user(myUser)
            return JsonResponse(
                {"message": "User Name or password is incorrect"},
                status=HTTP_400_BAD_REQUEST,
            )

        if not check_password(myUser.pwd_sha256, password):
            adding_failed_login_user(myUser)
            return JsonResponse(
                {"message": "User Name or password is incorrect"},
                status=HTTP_400_BAD_REQUEST,
            )

        if myUser.status_code != "ACTIVATE":
            return JsonResponse(
                {"message": "Your account has been deactivated."},
                status=HTTP_500_INTERNAL_SERVER_ERROR,
            )
        if myUser.status_account != "Approved":
            return JsonResponse({"message": "The account has not been approved."}, status=HTTP_401_UNAUTHORIZED)

        permissions = get_user_permissions(myUser)
        # print(permissions)
        token = generate_bearer_token(myUser.id, myUser.user_name, permissions)
        refresh_token = generate_bearer_token_with_time({"userId": myUser.id, "type": "REFRESH_TOKEN"}, REFRESH_TOKEN_TTL_MINUTES)
        adding_token_to_refresh(myUser, refresh_token)
        add_access_token_to_redis(myUser, token)

        # print(refresh_token)
        dataUpdate = {"last_login": datetime.now()}
        userSerializers = UserSerializers(myUser, data=dataUpdate, partial=True)
        if userSerializers.is_valid():
            userSerializers.save()
        dataOutput = {
            "id": myUser.id,
            "email": myUser.email,
            "user_name": myUser.user_name,
        }
        data = {
            "message": "Sign-in successfully",
            "data": {
                "access_token": token,
                "refresh_token": refresh_token,
                "permissions": permissions,
                "user": dataOutput,
            },
        }
        return JsonResponse(data, status=HTTP_200_OK)
    # except Exception as e:
    # return JsonResponse({"message": "Sign-in unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(auth=None, operation_id="createApiKeyUser", tags=["User"], operation=None)
@csrf_exempt
@api_view(["POST"])
@require_login
def createApiKeyUser(request: HttpRequest) -> JsonResponse:
    if request.method == "POST":
        try:
            userPayload = SimpleNamespace(**request.user)
            userId = userPayload.id
            myUser = Users.objects.filter(id=userId).first()

            userInfo = request.user
            temp = SimpleNamespace(**userInfo)
            permissions = str(temp.permission)
            if "SUPER-PARENT" in permissions.split("_"):
                clientIDs = ClientParent.objects.filter(user__id=temp.id).values_list("client__id", flat=True)
                clientID = ClientsInformation.objects.filter(id__in=list(clientIDs)).order_by("name").first()["clientID"]
            else:
                clientID = "All"

            if not myUser:
                return JsonResponse(
                    {"message": "User does not exist on the system."},
                    status=HTTP_400_BAD_REQUEST,
                )
            token = generateCustomerApiKey(myUser.id, myUser.user_name, clientID=clientID)
            myUser.api_key = token
            myUser.save()
            data = {
                "message": "Generate token aaaa successfully",
                "data": token,
            }
            return JsonResponse(data, status=HTTP_200_OK)
        except Exception:
            return JsonResponse(
                {"message": "Generate token unsuccessfully."},
                status=HTTP_400_BAD_REQUEST,
            )
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(auth=None, operation_id="getApiKeyUser", tags=["User"], operation=None)
@csrf_exempt
@api_view(["GET", "DELETE"])
@require_login
def getApiKeyUser(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        try:
            userPayload = SimpleNamespace(**request.user)
            userId = userPayload.id
            myUser = Users.objects.filter(id=userId).first()
            if not myUser:
                return JsonResponse(
                    {"message": "User does not exist on the system."},
                    status=HTTP_400_BAD_REQUEST,
                )
            data = {
                "message": "Get token successfully",
                "data": myUser.api_key,
            }
            return JsonResponse(data, status=HTTP_200_OK)
        except Exception:
            return JsonResponse({"message": "Get token unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
    elif request.method == "DELETE":
        try:
            userPayload = SimpleNamespace(**request.user)
            userId = userPayload.id
            myUser = Users.objects.filter(id=userId).first()
            if not myUser:
                return JsonResponse(
                    {"message": "User does not exist on the system."},
                    status=HTTP_400_BAD_REQUEST,
                )
            myUser.api_key = None
            myUser.save()
            return JsonResponse({"message": "Delete token successfully."}, status=HTTP_200_OK)
        except Exception:
            return JsonResponse({"message": "Delete token unsuccessfully."}, status=HTTP_400_BAD_REQUEST)


@extend_schema(
    request=None,
    responses={200: userInfoResponseSerializer},
    auth=None,
    operation_id="userInfo",
    tags=["User"],
    operation=None,
)
@api_view(["GET"])
@csrf_exempt
@require_login
def userInfo(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        try:
            userInfo = request.user
            # print('userInfo', userInfo)
            temp = SimpleNamespace(**userInfo)
            permissions = str(temp.permission)
            usernameCheck = Users.objects.filter(id=temp.id).first()
            clientID = "All"
            if "SUPER-PARENT" in permissions.split("_"):
                clientID = ClientParent.objects.filter(user=usernameCheck).first().client.clientID
            if usernameCheck is None:
                return JsonResponse({"message": "Username not found"}, status=HTTP_400_BAD_REQUEST)
            dataOutput = {
                "id": usernameCheck.id,
                "email": usernameCheck.email,
                "user_name": usernameCheck.user_name,
                "permissions": permissions,
                "clientID": clientID,
            }
            return JsonResponse(
                {"message": "Get user information successfully", "data": dataOutput},
                status=HTTP_200_OK,
            )
        except Exception:
            return JsonResponse(
                {"message": "Get user information Unsuccessfully"},
                status=HTTP_400_BAD_REQUEST,
            )
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_405_METHOD_NOT_ALLOWED,
    )


@extend_schema(
    request=changePasswordSerializer,
    responses={HTTP_200_OK: changePasswordResponseSerializer},
    auth=None,
    operation_id="changePassword",
    tags=["User"],
    operation=None,
)
@api_view(["POST"])
@csrf_exempt
@require_login
def changePassword(request: HttpRequest) -> JsonResponse:
    if request.method == "POST":
        try:
            userPayload = SimpleNamespace(**request.user)
            userId = userPayload.id
            serializer = changePasswordSerializer(data=request.data)
            if not serializer.is_valid():
                return JsonResponse(serializer.errors, status=HTTP_400_BAD_REQUEST)
            newPassword = serializer.validated_data.get("newPassword")
            oldPassword = serializer.validated_data.get("oldPassword")

            checkPassValidate = validate_password(newPassword)
            if not oldPassword or not newPassword:
                return JsonResponse({"message": "Invalid data input."}, status=HTTP_400_BAD_REQUEST)
            new_pwd_sha256 = encode_to_sha256(newPassword)
            old_pwd_sha256 = encode_to_sha256(oldPassword)
            if old_pwd_sha256 != userPayload.pwd_sha256:
                return JsonResponse(
                    {"message": "Old password is not correctly"},
                    status=HTTP_400_BAD_REQUEST,
                )
            if new_pwd_sha256 == userPayload.pwd_sha256 or newPassword == oldPassword:
                return JsonResponse(
                    {"message": "The new password cannot be the same as the old password'"},
                    status=HTTP_400_BAD_REQUEST,
                )
            if checkPassValidate is not None:
                return JsonResponse({"message": checkPassValidate}, status=HTTP_400_BAD_REQUEST)
            dataUpdate = {"pwd_sha256": new_pwd_sha256, "updated_utc": datetime.now()}
            user_info = Users.objects.get(pk=userId)
            userSerializers = UserSerializers(user_info, data=dataUpdate, partial=True)
            if userSerializers.is_valid():
                userSerializers.save()
                return JsonResponse(
                    {"message": "Change password successfully."},
                    status=HTTP_200_OK,
                )
        except:
            return JsonResponse(
                {"message": "Change password unsuccessfully."},
                status=HTTP_400_BAD_REQUEST,
            )
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    request=None,
    responses={HTTP_200_OK: userLogoutResponseSerializer},
    auth=None,
    operation_id="userLogout",
    tags=["User"],
    operation=None,
)
@api_view(["POST"])
@csrf_exempt
@require_login
def userLogout(request: HttpRequest) -> JsonResponse:
    if request.method == "POST":
        try:
            userPayload = SimpleNamespace(**request.user)
            userId = userPayload.id
            user_info = Users.objects.filter(id=userId).first()
            if user_info is None:
                return JsonResponse(
                    {"message": "User does not exist on the system."},
                    status=HTTP_400_BAD_REQUEST,
                )
            delete_token_to_refresh(user_info)
            delete_access_token_from_redis(user_info)

            return JsonResponse(
                {"message": "Log-out successfully."},
                status=HTTP_200_OK,
            )
        except:
            return JsonResponse({"message": "Log-out unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    request=None,
    responses={HTTP_200_OK: refreshTokenUserResponseSerializer},
    auth=None,
    operation_id="refreshTokenUser",
    tags=["User"],
    operation=None,
)
@api_view(["GET"])
@csrf_exempt
@require_token_refresh
def refreshTokenUser(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        try:
            userPayload = SimpleNamespace(**request.user)
            userId = userPayload.id
            user_info = Users.objects.filter(id=userId).first()

            if user_info is None:
                return JsonResponse(
                    {"message": "User does not exist on the system."},
                    status=HTTP_400_BAD_REQUEST,
                )
            # refresh_token = generate_bearer_token_with_time(
            #     {"userId": user_info.id,
            #         "type": "REFRESH_TOKEN"}, REFRESH_TOKEN_TTL_MINUTES
            # )
            permissions = get_user_permissions(user_info)

            token = generate_bearer_token(user_info.id, user_info.user_name, permissions)
            # adding_token_to_refresh(user_info, refresh_token)
            add_access_token_to_redis(user_info, token)
            return JsonResponse(
                {
                    "message": "Refresh successfully.",
                    "data": {
                        "access_token": token,
                        "refresh_token": request.headers.get("Authorization"),
                        "permissions": permissions,
                    },
                },
                status=HTTP_200_OK,
            )
        except Exception:
            # print('Error refreshTokenUser', e)
            return JsonResponse({"message": "Refresh unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    parameters=[
        OpenApiParameter(name="page", description="Page", required=False, type=str),
        OpenApiParameter(name="page_size", description="Page Size", required=False, type=str),
        OpenApiParameter(name="id", description="id", required=False, type=str),
    ],
    responses={HTTP_200_OK: getListUserResponseSerializer},
    auth=None,
    operation_id="getListUser",
    tags=["User"],
    operation=None,
)
@api_view(["GET"])
@csrf_exempt
@require_login_admin
def getListUser(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        try:
            page = request.GET.get("page", 1)
            page_size = request.GET.get("page_size", 10)

            user_id = request.GET.get("id", None)

            users_with_admin_permission = Users.objects.filter().all()

            if user_id:
                users_with_admin_permission = (
                    users_with_admin_permission.filter(Q(id__icontains=user_id) | Q(user_name__icontains=user_id) | Q(email__icontains=user_id)).distinct().all()
                )

            paginator = Paginator(users_with_admin_permission, page_size)
            users_page = paginator.get_page(page)

            list_user = [UserGetSerializers(user).data for user in users_page]
            for user in list_user:
                if user["role_id"] == "SUPER-PARENT":
                    user["client"] = ClientParent.objects.filter(user=user["id"]).first().client.name
                else:
                    user["client"] = ""
                if user["status_code"] == "ACTIVATE":
                    user["status_code"] = "ACTIVE"
                elif user["status_code"] == "DEACTIVATE":
                    user["status_code"] = "INACTIVE"
            return JsonResponse(
                {
                    "message": "Get successfully.",
                    "data": {
                        "list_user": list_user,
                        "pagination": {
                            "page": users_page.number,
                            "total_pages": paginator.num_pages,
                            "total_items": paginator.count,
                        },
                    },
                },
                status=HTTP_200_OK,
            )
        except Exception:
            return JsonResponse(
                {"message": "Get list user unsuccessfully."},
                status=HTTP_400_BAD_REQUEST,
            )
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    request=None,
    responses={HTTP_200_OK: None},
    auth=None,
    exclude=True,
    operation_id="downloadListUser",
    tags=["User"],
    operation=None,
)
@api_view(["GET"])
@csrf_exempt
@require_login_admin
def downloadListUser(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        try:
            users_with_admin_permission = Users.objects.filter().all()

            list_user = [UserGetSerializers(user).data for user in users_with_admin_permission]
            excel_file = StringIO()

            csv_writer = csv.writer(excel_file)

            headers = [
                "User ID",
                "User Name",
                "Email",
                "Role",
                "Active",
                "Last Login",
                "Created At",
            ]
            csv_writer.writerow(headers)

            for user_data in list_user:
                user_row = [
                    user_data["id"],
                    user_data["user_name"],
                    user_data["email"],
                    user_data["level_id"],
                    user_data["status_code"],
                    user_data["last_login"],
                    user_data["created_utc"],
                ]
                csv_writer.writerow(user_row)
            response = HttpResponse(excel_file.getvalue(), content_type="text/csv")
            response["Content-Disposition"] = "attachment; filename=user_data.csv"
            return response
        except Exception:
            # print('Error downloadListUser', e)
            return JsonResponse({"message": "Get unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    methods=["GET"],
    request=None,
    responses={HTTP_200_OK: UserGetSerializers},
    auth=None,
    operation_id="getUser",
    tags=["User"],
    operation=None,
)
@extend_schema(
    methods=["PUT"],
    request=updateUserRequestSerializer,
    responses={HTTP_200_OK: updateUserResponseSerializer},
    auth=None,
    operation_id="updateUser",
    tags=["User"],
    operation=None,
)
@extend_schema(
    methods=["DELETE"],
    request=None,
    responses={HTTP_200_OK: deleteUserResponseSerializer},
    auth=None,
    operation_id="DeleteUser",
    tags=["User"],
    operation=None,
)
@api_view(["GET", "PUT", "DELETE"])
@require_login_admin
def user(request, user_id):
    userPayload = SimpleNamespace(**request.user)
    userId = userPayload.id
    if request.method == "GET":
        try:
            user = Users.objects.get(id=user_id)
            serialized_user = UserGetSerializers(user).data
            return Response(
                {
                    "message": "Get user successfully.",
                    "data": {"user": serialized_user},
                },
                status=HTTP_200_OK,
            )
        except Users.DoesNotExist:
            return Response({"message": "User not found."}, status=HTTP_404_NOT_FOUND)
        except Exception:
            # print('Error get_user', e)
            return Response({"message": "Get user unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
    elif request.method == "PUT":
        try:
            # if user_id == userId :
            #     return Response({'message': 'Administrators cannot update themselves'}, status=HTTP_400_BAD_REQUEST)
            serializer = updateUserRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=HTTP_400_BAD_REQUEST)

            userName = serializer.validated_data.get("userName")
            password = serializer.validated_data.get("password")
            status = serializer.validated_data.get("status")
            level = serializer.validated_data.get("level")

            user = Users.objects.get(id=user_id)
            userSerializer = UserSerializers(user).data

            if not userName:
                userName = userSerializer["user_name"]
            if not password:
                password = userSerializer["pwd_sha256"]
            else:
                checkPassValidate = validate_password(password)
                if checkPassValidate is not None:
                    return Response({"message": checkPassValidate}, status=HTTP_400_BAD_REQUEST)
                pwd_sha256 = password
                password = encode_to_sha256(pwd_sha256)

            if not status:
                status = userSerializer["status_code"]

            if not level:
                level = Levels.objects.filter(id=userSerializer["level"]).first()
            else:
                level = Levels.objects.filter(level_name=level).first()
                if not level:
                    return JsonResponse(
                        {"message": "Level name not existed!!!"},
                        status=HTTP_404_NOT_FOUND,
                    )

            user_serializer = UserSerializers(
                user,
                data={
                    "user_name": userName,
                    "pwd_sha256": password,
                    "status_code": status,
                    "level": level.id,
                },
                partial=True,
            )

            if user_serializer.is_valid():
                user_serializer.save()
                return Response(
                    {"message": "Update User successfully."},
                    status=HTTP_200_OK,
                )
            return Response(
                {"message": "Invalid data.", "errors": user_serializer.errors},
                status=HTTP_400_BAD_REQUEST,
            )
        except Users.DoesNotExist:
            return Response({"message": "User not found."}, status=HTTP_404_NOT_FOUND)
        except Exception:
            return Response({"message": "Update User unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
    elif request.method == "DELETE":
        try:
            if user_id == userId:
                return Response(
                    {"message": "Administrators cannot delete themselves"},
                    status=HTTP_400_BAD_REQUEST,
                )
            user = Users.objects.get(id=user_id)
            if not user:
                return Response(
                    {"message": "Delete User unsuccessfully."},
                    status=HTTP_404_NOT_FOUND,
                )
            if user.role.role_name == "SUPER-PARENT":
                clientParent = ClientParent.objects.filter(user=user).first()
                if clientParent:
                    clientParent.delete()

            user.delete()
            return Response(
                {"message": "Delete User successfully."},
                status=HTTP_200_OK,
            )
        except Users.DoesNotExist or ClientParent.DoesNotExist:
            return Response({"message": "User not found."}, status=HTTP_404_NOT_FOUND)
        except Exception as e:
            print("Error delete_user", e)
            return Response({"message": "Delete User unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    request=updateProfileSerializer,
    responses={HTTP_200_OK: updateProfileResponseSerializer},
    auth=None,
    operation_id="updateProfile",
    tags=["User"],
    operation=None,
)
@api_view(["POST"])
@csrf_exempt
@require_login
def updateProfile(request: HttpRequest) -> JsonResponse:
    if request.method == "POST":
        try:
            # request.POST just use in form-data. with json need to use json.loads
            userPayload = SimpleNamespace(**request.user)
            userId = userPayload.id
            serializer = updateProfileSerializer(data=request.data)
            if not serializer.is_valid():
                return JsonResponse(serializer.errors, status=HTTP_400_BAD_REQUEST)
            userName = serializer.validated_data.get("userName")
            usernameCheck = Users.objects.filter(id=userId).first()
            if usernameCheck is None:
                return JsonResponse({"message": "User not found"}, status=HTTP_400_BAD_REQUEST)
            dataUpdate = {
                "user_name": userName if userName else (usernameCheck.user_name if usernameCheck.user_name else ""),
            }
            userSerializers = UserSerializers(usernameCheck, data=dataUpdate)
            if userSerializers.is_valid():
                userSerializers.save()
                return JsonResponse(
                    {"message": "Update profile successfully."},
                    status=HTTP_200_OK,
                )
        except Exception:
            # print('error', e)
            return JsonResponse(
                {"message": "Update profile unsuccessfully."},
                status=HTTP_400_BAD_REQUEST,
            )
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    methods=["POST"],
    request=createUserRequestSerializer,
    responses={HTTP_200_OK: createUserResponseSerializer},
    auth=None,
    operation_id="userCreate",
    tags=["User"],
    operation=None,
)
@api_view(["POST"])
@csrf_exempt
@require_login_admin
def userCreate(request: HttpRequest) -> JsonResponse:
    if request.method == "POST":
        try:
            # print('request.data', request)
            serializer = createUserRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=HTTP_400_BAD_REQUEST)
            email = serializer.validated_data.get("email")
            userName = serializer.validated_data.get("userName")
            password = serializer.validated_data.get("password")
            level = serializer.validated_data.get("level", "SUPER-PARENT")
            role = serializer.validated_data.get("role", "ADMIN")
            client_id = request.data.get("client_id", None)

            if level == "SUPER-PARENT":
                if client_id == "" or client_id == "None":
                    return Response(
                        {"message": "Level super-parent need select client"},
                        status=HTTP_400_BAD_REQUEST,
                    )

            if not password or not email or not userName:
                return Response({"message": "Invalid data input."}, status=HTTP_400_BAD_REQUEST)

            checkMailValidate = validate_email(email)
            if checkMailValidate is not None:
                return Response({"message": checkMailValidate}, status=HTTP_400_BAD_REQUEST)

            checkPassValidate = validate_password(password)
            if checkPassValidate is not None:
                return Response({"message": checkPassValidate}, status=HTTP_400_BAD_REQUEST)

            userCheck = Users.objects.filter(email=email).first()
            if userCheck is not None:
                return Response({"message": "Email already in use."}, status=HTTP_400_BAD_REQUEST)

            pwd_sha256 = encode_to_sha256(password)

            id_level = Levels.objects.get(level_name=level)
            id_role = Roles.objects.get(role_name=role)
            # print('id_role', id_role.role_name)
            # print('id_level', id_level.level_name)
            user_serializers_save = {
                "user_name": userName,
                "pwd_sha256": pwd_sha256,
                "email": email,
                "updated_utc": datetime.now(),
                "created_utc": datetime.now(),
                "status_code": "ACTIVATE",
                "sign_up_type": "individual",
                "level": id_level.id,
                "role": id_role.id,
            }

            user_serializers = UsersSerializers(data=user_serializers_save)
            if user_serializers.is_valid():
                user_serializers.save()
                if client_id == "" or client_id == "None":
                    return JsonResponse({"message": "Create User successfully."}, status=HTTP_200_OK)

                userAfter = Users.objects.filter(user_name=userName).first().id
                client = ClientsInformation.objects.filter(clientID=client_id).first()
                ClientParent.objects.create(user_id=userAfter, client_id=client.id)
                return JsonResponse({"message": "Create User successfully."}, status=HTTP_200_OK)
            logger.error("User data validation failed: %s", user_serializers.errors)
            return JsonResponse({"message": "Create User unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.exception("An error occurred during user data save: %s", str(e))
            return JsonResponse({"message": "Create User unsuccessfully."}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema()
@api_view(["POST"])
@csrf_exempt
def createCampaignClient(request: HttpRequest) -> JsonResponse:
    if request.method == "POST":
        try:
            campaign_name = request.data.get("campaign_name")
            client_name = request.data.get("client_name")
            user_name = request.data.get("user_name")
            for c in client_name:
                campaign = Campaign.objects.get(campaign_name=campaign_name).id
                user = Users.objects.get(user_name=user_name).id
                client = ClientsInformation.objects.get(name=c).id

                ClientParent.objects.create(campaign_id=campaign, user_id=user, client_id=client)
            return JsonResponse({"message": "Create CampaignClient successfully."}, status=HTTP_200_OK)
        except Exception:
            return JsonResponse(
                {"message": "Create CampaignClient unsuccessfully."},
                status=HTTP_400_BAD_REQUEST,
            )
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    request=forgotPasswordUserSerializer,
    responses={HTTP_200_OK: forgotPasswordUserResponseSerializer},
    auth=None,
    operation_id="forgotPasswordUser",
    operation=None,
    tags=["User"],
)
@api_view(["POST"])
@csrf_exempt
def forgotPasswordUser(request: HttpRequest) -> JsonResponse:
    if request.method == "POST":
        try:
            serializer = forgotPasswordUserSerializer(data=request.data)
            if not serializer.is_valid():
                return JsonResponse(serializer.errors, status=HTTP_400_BAD_REQUEST)

            email = serializer.validated_data.get("email")
            validate_email(email)

            userCheck = Users.objects.filter(email=email).first()
            if userCheck is None:
                return JsonResponse({"message": "Email not registered."}, status=HTTP_404_NOT_FOUND)

            if userCheck.status_code != "ACTIVATE" or not userCheck.pwd_sha256:
                return JsonResponse({"message": "Account must be Active."}, status=HTTP_404_NOT_FOUND)

            delete_count_login_failed(userCheck)
            password_gen = generate_password()
            password_gen_sha256 = encode_to_sha256(password_gen)

            dataUpdate = {
                "pwd_sha256": password_gen_sha256,
                "forgot_password_checking": True,
                "updated_utc": datetime.now(),
            }
            userSerializers = UserSerializers(userCheck, data=dataUpdate, partial=True)

            if userSerializers.is_valid():
                userSerializers.save()
                if not send_mail_forgot_password(userCheck.email, password_gen):
                    return JsonResponse({"message": "Can't send to email "}, status=HTTP_400_BAD_REQUEST)
                return JsonResponse({"message": "Forgot Password successfully."}, status=HTTP_200_OK)

            return JsonResponse(
                {"message": "Forgot Password unsuccessfully."},
                status=HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return JsonResponse(
                {"message": "Forgot Password unsuccessfully.", "error": str(e)},
                status=HTTP_400_BAD_REQUEST,
            )
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    responses={HTTP_200_OK: None},
    auth=None,
    operation_id="getListLevel",
    tags=["User"],
    operation=None,
)
@api_view(["GET"])
@csrf_exempt
@require_login_admin
def getMetaData(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":
        clients = ClientsInformation.objects.filter(
            status__in=[EClientStatus.Active.value, EClientStatus.Onboarding.value, EClientStatus.Pre_ONB.value, EClientStatus.Integration]
        ).order_by("name")
        None_client = [{"id": 0, "clientID": "None", "name": "None "}]
        levels = LevelSerializer(Levels.objects.all(), many=True).data
        try:
            return JsonResponse(
                {
                    "message": "Get successfully!",
                    "data": {
                        "Levels": levels,
                        "Roles": [{"id": 1, "role": "ADMIN"}],
                        "Status": [{"id": 1, "status": "ACTIVATE"}, {"id": 2, "status": "DEACTIVATE"}],
                        "Clients": None_client + list(clients.values("id", "clientID", "name")),
                    },
                },
                status=HTTP_200_OK,
            )
        except Exception:
            return JsonResponse({"message": "Get list user unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )
