import csv
import logging
from datetime import datetime
from enum import Enum
from io import String<PERSON>
from types import SimpleNamespace

from django.core.paginator import Paginator
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from drf_spectacular.utils import OpenApiExample, OpenApiParameter, extend_schema
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_201_CREATED,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
)
from rest_framework.views import APIView

from user.models import (
    ClientParent,
    ClientsInformation,
    Levels,
    StatusAccountLog,
    Users,
)
from user.serializers import (
    ChangeCompanyDeveloperZoneSerializer,
    ChangeLevelDeveloperZoneSerializer,
    ChangePasswordDeveloperZoneSerializer,
    ChangeUserInformationSerializer,
    LevelSerializer,
    UsersSerializers,
    createApiKeySerializer,
    createRequestStatusSerializer,
    createUserAPIRequestSerializer,
    getListAccDeveloperSerializer,
    getProcessAccountStatusSerializer,
    getUserDeveloperSerializer,
)
from user.utils.email import send_email_user
from user.utils.utils import (
    encode_to_sha256,
    generateCustomerApiKey,
    require_login,
    validate_email,
    validate_passcode,
    validate_password,
)


class EClientStatus(Enum):
    Active = "Active"
    Integration = "Integration"
    Onboarding = "Onboarding"
    Pre_ONB = "Pre-ONB"
    Closed = "Closed"
    Removed = "Removed"
    Paused = "Paused"


guide_get_api_key = """
===============================================================================================================================\n
    Here's how you can obtain the API key from the provided account.                                                          \n
    - Access our website, then use the account information we provided to log in.                                             \n
    - Click on the three horizontal lines icon at the top right corner of the screen.                                         \n
    - Next, click on the 'Manage Access Token' button.                                                                        \n
    - In the 'Production Token' tab, enter the passcode we provided into the 'Pin passcode' field and click 'Get Token'.      \n
    - The API key will appear in the token field if you enter the correct passcode that we provided.                          \n
    - Additionally, I highly recommend changing the passcode we provided for security reasons. This is optional.              \n
===============================================================================================================================\n
"""
logger = logging.getLogger(__name__)


@extend_schema(
    methods=["POST"],
    request=createUserAPIRequestSerializer,
    responses={HTTP_200_OK: createUserAPIRequestSerializer},
    auth=None,
    operation_id="userCreate",
    tags=["ACCOUNT-MANAGEMENT"],
    operation=None,
)
@api_view(["POST"])
@csrf_exempt
@require_login
def userAPICreate(request: HttpRequest) -> JsonResponse:

    if request.method == "POST":
        try:
            serializer = createUserAPIRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=HTTP_400_BAD_REQUEST)
            email = serializer.validated_data.get("email")
            first_name = serializer.validated_data.get("first_name")
            last_name = serializer.validated_data.get("last_name")
            username = serializer.validated_data.get("username")
            password = serializer.validated_data.get("password")
            level = serializer.validated_data.get("level")
            client_id = request.data.get("client_id", None)
            if not password or not email or not level:
                return Response({"message": "Invalid data input."}, status=HTTP_400_BAD_REQUEST)

            checkMailValidate = validate_email(email)
            if checkMailValidate is not None:
                return Response({"message": checkMailValidate}, status=HTTP_400_BAD_REQUEST)

            checkPassValidate = validate_password(password)
            if checkPassValidate is not None:
                return Response({"message": checkPassValidate}, status=HTTP_400_BAD_REQUEST)

            userCheck = Users.objects.filter(email=email).first()
            if userCheck is not None:
                return Response({"message": "Email already in use."}, status=HTTP_400_BAD_REQUEST)

            userCheck = Users.objects.filter(user_name=username).first()
            if userCheck is not None:
                return Response({"message": "Username already in use."}, status=HTTP_400_BAD_REQUEST)

            pwd_sha256 = encode_to_sha256(password)
            level_id = Levels.objects.get(level_name=level)
            # environment
            user_serializers_save = {
                "first_name": first_name,
                "last_name": last_name,
                "user_name": username,
                "pwd_sha256": pwd_sha256,
                "email": email,
                "updated_utc": datetime.now(),
                "created_utc": datetime.now(),
                "status_code": "ACTIVATE",
                "sign_up_type": "individual",
                "level": level_id.id,
                "role": 1,
                "status_account": "Approved",
            }
            user_serializers = UsersSerializers(data=user_serializers_save)
            if user_serializers.is_valid():
                user_serializers.save()
                # print(client_id)
                if client_id == "" or client_id == "None":
                    return JsonResponse({"message": "Create User successfully!"}, status=HTTP_200_OK)

                userAfter = Users.objects.filter(user_name=email).first()
                client = ClientsInformation.objects.filter(clientID=client_id).first()
                ClientParent.objects.create(user_id=userAfter.id, client_id=client.id)

                record_log = StatusAccountLog.objects.create(user_name=email, approved_by=request.user["user_name"], after_action="Approved")
                record_log.save()

                return JsonResponse({"message": "Create User successfully!"}, status=HTTP_200_OK)

            logger.error("User data validation failed: %s", user_serializers.errors)
            return JsonResponse({"message": "Create User unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.exception("An error occurred during user data save: %s", str(e))
            return JsonResponse({"message": "Create User unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)

    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(responses={HTTP_200_OK: None}, auth=None, operation_id="getListLevel", tags=["ACCOUNT-MANAGEMENT"], operation=None)
@api_view(["GET"])
@csrf_exempt
@require_login
def getInformationAPIUser(request: HttpRequest) -> JsonResponse:

    if request.method == "GET":
        try:
            None_client = [{"id": 0, "clientID": "None", "name": "None "}]
            clients = ClientsInformation.objects.filter(
                status__in=[EClientStatus.Active.value, EClientStatus.Onboarding.value, EClientStatus.Pre_ONB.value, EClientStatus.Integration]
            ).order_by("name")
            levels = LevelSerializer(Levels.objects.all(), many=True).data
            return JsonResponse(
                {
                    "message": "Get user successfully!",
                    "data": {"Levels": levels, "Clients": None_client + list(clients.values())},
                },
                status=HTTP_200_OK,
            )
        except Exception:
            return JsonResponse({"message": "Get list user unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    responses={HTTP_200_OK: getListAccDeveloperSerializer},
    parameters=[
        OpenApiParameter(
            name="search_by",
            description="Search by field of user.",
            required=False,
            type=str,
            examples=[OpenApiExample("John Admin", value="John Admin")],
        ),
        OpenApiParameter(
            name="search_key",
            description="Search by value of field for user.",
            required=False,
            type=str,
            examples=[OpenApiExample("<EMAIL>", value="<EMAIL>")],
        ),
        OpenApiParameter(
            name="company",
            description="client name of the user",
            required=False,
            type=str,
            examples=[OpenApiExample("Client_a", value="Client_a")],
        ),
        OpenApiParameter(
            name="page",
            description="The page number of a list users. Default=1",
            required=False,
            type=int,
            examples=[OpenApiExample("1", value="1")],
        ),
        OpenApiParameter(
            name="page_size",
            description="The number of users per page. Default=10",
            required=False,
            type=int,
            examples=[OpenApiExample("10", value="10")],
        ),
        OpenApiParameter(
            name="status_account",
            description="The status_account of user",
            required=False,
            type=str,
            examples=[OpenApiExample("Approved", value="Approved")],
            enum=["Approved", "Pending", "Denied"],
        ),
        OpenApiParameter(
            name="sortByVal", required=False, type=str
        ),
        OpenApiParameter(
            name="orderByVal", required=False, type=str, enum=['ASC', 'DESC']
        ),
        OpenApiParameter(name="phone", description="The phone of user", required=False, type=str),
    ],
    auth=None,
    operation_id="getListLevel",
    tags=["ACCOUNT-MANAGEMENT"],
    operation=None,
)
@api_view(["GET"])
@csrf_exempt
@require_login
def getAccountManagementTable(request: HttpRequest) -> JsonResponse:

    if request.method == "GET":

        # try:
        list_user = Users.objects.filter().order_by("-created_utc")

        company = request.GET.get("company", None)
        phone = request.GET.get("phone", None)
        page = request.GET.get("page", 1)
        page_size = request.GET.get("page_size", 10)
        search_key, search_by = request.GET.get("search_key", None), request.GET.get("search_by", None)
        status_account = request.GET.get("status_account", None)

        # list_user = [getListAccDeveloperSerializer(user).data for user in list_user]
        # list_user = getListAccDeveloperSerializer(list_user, many=True).data
        list_user = list_user.values(
            "id", "user_name", "email", "company", "created_utc", "updated_utc", "passcode", "api_key", "level_id", "status_account", "phone", "first_name", "last_name"
        )

        for user in list_user:
            try:
                parent_id = ClientParent.objects.get(user_id=user["id"]).client_id
                clientID = ClientsInformation.objects.get(id=parent_id).clientID
                client_name = ClientsInformation.objects.get(id=parent_id).name
                user["client_id"] = clientID
                user["client_name"] = client_name
            except:
                user["client_id"] = None
                user["client_name"] = None

            try:
                level = Levels.objects.get(id=user["level_id"]).level_name
                user["level"] = level
                user["level_name"] = level
            except:
                user["level"] = None
                user["level_name"] = None

        for user in list_user:
            user["is_passcode"] = bool(user["passcode"])
        if status_account:
            list_user = [item for item in list_user if item.get("status_account", "") and status_account in item.get("status_account", "")]
        if company:
            list_user = [item for item in list_user if item.get("company", "") and company in item.get("company", "")]
        if phone:
            list_user = [item for item in list_user if item.get("phone", "") and company in item.get("phone", "")]
        if search_by is None and search_key is not None:
            list_user = [item for item in list_user if str(search_key).lower() in str(item.values()).lower()]

        if search_by and search_key:
            search_by = search_by.split(",")
            search_key = search_key.lower().split(",")
            for i in range(len(search_by)):
                list_user = [data for data in list_user if str(search_key[i]) in str(data[search_by[i]]).lower()]

        sortByVal = request.GET.get("sortByVal", "created_utc")
        orderByVal = request.GET.get("orderByVal", "DESC")
        list_user = sorted(list_user, key=lambda k: (k[sortByVal] is None, str(k[sortByVal]).lower() if k[sortByVal] is not None else ""), reverse=(orderByVal == "DESC"))

        paginator = Paginator(list_user, page_size)
        response_data = paginator.get_page(page)
        response_data = [i for i in response_data]

        pagination = {"page": page, "limit": int(page_size), "total_page": paginator.num_pages, "total_item": paginator.count}
        return JsonResponse({"message": "Get list user successfully!", "data": {"results": response_data, "pagination": pagination}}, status=HTTP_200_OK)
    # except Exception as e:
    #     return JsonResponse({"message": "Get list user unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)

    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    methods=["GET"],
    request=getUserDeveloperSerializer,
    responses={HTTP_200_OK: getUserDeveloperSerializer},
    auth=None,
    operation_id="userCreate",
    tags=["ACCOUNT-MANAGEMENT"],
    operation=None,
)
@api_view(["GET"])
@csrf_exempt
@require_login
def getUserDeveloper(request, user_id):
    if request.method == "GET":
        try:
            user = Users.objects.get(id=user_id)
            serialized_user = getUserDeveloperSerializer(user).data
            return Response(
                {
                    "message": "Get user successfully!",
                    "data": {"user": serialized_user},
                },
                status=HTTP_200_OK,
            )
        except Users.DoesNotExist:
            return Response({"message": "User not found."}, status=HTTP_404_NOT_FOUND)
        except Exception:
            return Response({"message": "Get user unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only GET requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    methods=["GET"],
    parameters=[
        OpenApiParameter(
            name="user_name",
            description="User name of user.",
            required=True,
            type=str,
            examples=[OpenApiExample("John Admin", value="John Admin")],
        ),
        OpenApiParameter(
            name="passcode",
            description="passcode of user.",
            required=True,
            type=str,
            examples=[OpenApiExample("passcode12345", value="passcode12345")],
        ),
    ],
    responses={HTTP_200_OK: None},
    auth=None,
    operation_id="showToken",
    tags=["ACCOUNT-MANAGEMENT"],
    operation=None,
)
@api_view(["GET"])
@csrf_exempt
@require_login
def showToken(request: HttpRequest) -> JsonResponse:
    if request.method == "GET":

        try:
            user_name = request.GET.get("user_name")
            passcode = request.GET.get("passcode")
            user = Users.objects.get(user_name=user_name)

            if not user:
                return Response({"message": "Not found user."}, status=HTTP_400_BAD_REQUEST)
            if not passcode:
                return Response({"message": "Not provided passcode"}, status=HTTP_400_BAD_REQUEST)
            if not user.passcode:
                return Response({"message": "User does not have passcode"}, status=HTTP_400_BAD_REQUEST)
            if user.passcode == passcode:
                return Response({"message": "Successfully", "data": user.api_key}, status=HTTP_200_OK)
            else:
                return Response({"message": "Wrong passcode"}, status=HTTP_400_BAD_REQUEST)
        except Exception:
            return Response({"message": "Get token unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only GET requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


@extend_schema(
    methods=["POST"], request=createApiKeySerializer, responses={HTTP_200_OK: None}, auth=None, operation_id="createAPIKey", tags=["ACCOUNT-MANAGEMENT"], operation=None
)
@api_view(["POST"])
@csrf_exempt
@require_login
def createAPIKey(request: HttpRequest) -> JsonResponse:

    if request.method == "POST":

        try:
            serializer = createApiKeySerializer(data=request.data)

            if not serializer.is_valid():
                return Response(serializer.errors, status=HTTP_400_BAD_REQUEST)
            user_name = serializer.validated_data.get("user_name")
            passcode = serializer.validated_data.get("passcode")
            user = Users.objects.get(user_name=user_name)

            if not user:
                return Response({"message": "Not found user."}, status=HTTP_404_NOT_FOUND)
            passcode_check = validate_passcode(passcode)
            if passcode_check is not None:
                return Response({"message": passcode_check}, status=HTTP_400_BAD_REQUEST)
            try:
                client_id = ClientParent.objects.get(user=user.id).client.clientID
            except:
                return Response({"message": "Not found client of user."}, status=HTTP_404_NOT_FOUND)
            if not client_id:
                return Response({"message": "Not found client of user."}, status=HTTP_404_NOT_FOUND)

            token = generateCustomerApiKey(user.id, user.user_name, clientID=client_id)
            user.api_key = token
            user.passcode = passcode
            user.save()
            subject = "Information account"
            message = f"""This is your the information account:\n
                - Passcode: {passcode}\n
                - Username :{user.user_name} \n
                - Password: It is the password you type when creating the account.
            You need to change your passcode to avoid security issues.\n
            {guide_get_api_key}\n
            """
            send_email_user(subject=subject, message=message, receiver_email=user.email)
            StatusAccountLog.objects.create(user_name=user.user_name, approved_by=request.user["user_name"], is_create_key_action=True)
            return Response({"message": "Create apikey successfully!", "data": token}, status=HTTP_200_OK)
        except Exception:
            return Response({"message": "Create apikey unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)
    return JsonResponse(
        {"message": "Invalid request method. Only POST requests are allowed."},
        status=HTTP_400_BAD_REQUEST,
    )


class CheckStatusAccountView(APIView):
    @extend_schema(request=createRequestStatusSerializer, description="Setup status account", tags=["ACCOUNT-MANAGEMENT"])
    @method_decorator(require_login)
    def post(self, request, *args, **kwargs):
        try:
            serializer = createRequestStatusSerializer(data=request.data)
            if serializer.is_valid():
                response_data = serializer.save()

                record_log = StatusAccountLog.objects.create(
                    user_name=response_data["username"],
                    approved_by=request.user["user_name"],
                    before_action=response_data["before_status"],
                    after_action=response_data["status"],
                    note=response_data["note"],
                )
                record_log.save()

                return Response({"message": f"Change successfully account status of ({response_data['username']})."}, status=HTTP_201_CREATED)
            error_message = list(serializer.errors.values())[0][0]
            return Response({"message": error_message}, status=HTTP_400_BAD_REQUEST)

        except Exception:
            return Response({"message": "Change unsuccessfully account status"}, status=HTTP_400_BAD_REQUEST)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="id",
                description="id",
                required=True,
                type=int,
            )
        ],
        description="Get process status",
        tags=["ACCOUNT-MANAGEMENT"],
    )
    @method_decorator(require_login)
    def get(self, request, *args, **kwargs):

        try:
            id = request.GET.get("id", None)
            user_name = Users.objects.get(id=id).user_name
            if not user_name:
                return Response({"message": "Invalid data input."}, status=HTTP_400_BAD_REQUEST)
            status_list = list(StatusAccountLog.objects.filter(user_name=user_name).order_by("-time"))
            response_data = [getProcessAccountStatusSerializer(log).data for log in status_list]

            return Response({"message": "Get history successfully", "data": response_data}, status=HTTP_200_OK)
        except Exception:
            return Response({"message": "Get history unsuccessfully"}, status=HTTP_400_BAD_REQUEST)


class DeleteDeveloperZoneView(APIView):
    @extend_schema(description="Delete developer-zone", tags=["ACCOUNT-MANAGEMENT"])
    @method_decorator(csrf_exempt)
    @method_decorator(require_login)
    def delete(self, request, id, *args, **kwargs):
        try:
            userPayload = SimpleNamespace(**request.user)
            userId = userPayload.id
            if id == userId:
                return Response({"message": "Administrators cannot delete themselves"}, status=HTTP_400_BAD_REQUEST)
            if not id:
                return Response({"message": "Need provide id"}, status=HTTP_404_NOT_FOUND)
            user = Users.objects.get(id=id)
            if not user:
                return Response({"message": "Not found user"}, status=HTTP_404_NOT_FOUND)

            user.delete()
            StatusAccountLog.objects.filter(user_name=user.user_name).delete()
            if not self.annouce_delete_user(user.email):
                return Response({"message": "Can't send email"}, status=HTTP_400_BAD_REQUEST)
            return Response({"message": "Delete the user successfully"}, status=HTTP_200_OK)
        except Exception:
            return Response({"message": "Delete the user unsuccessfully"}, status=HTTP_400_BAD_REQUEST)

    def annouce_delete_user(self, email):

        subject = "Delete Account"
        message = "Your account has been deleted by the FLOFIN administrator. \nIf you have any questions, please contact us.\n"
        receiver_email = email

        if send_email_user(subject=subject, message=message, receiver_email=receiver_email):
            return True
        return False


class ChangeLevelDeveloperZone(APIView):
    @extend_schema(request=ChangeLevelDeveloperZoneSerializer, description="Change password account", tags=["ACCOUNT-MANAGEMENT"])
    @method_decorator(require_login)
    def put(self, request, *args, **kwargs):
        try:
            serializer = ChangeLevelDeveloperZoneSerializer(data=request.data)

            if serializer.is_valid():
                level_name = serializer.save()

                return Response({"data": {"message": f"Change {level_name} role successfully!"}}, status=HTTP_200_OK)
            error_message = list(serializer.errors.values())[0][0]
            return Response({"message": error_message}, status=HTTP_400_BAD_REQUEST)

        except Exception:
            return Response({"message": "Change status user unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)


class ExportCSVDeveloperZone(APIView):
    @extend_schema(
        parameters=[OpenApiParameter(name="type", description="Export all user or company", required=False, type=str, enum=["ALL", "COMPANY"])],
        description="Export csv",
        tags=["ACCOUNT-MANAGEMENT"],
    )
    @method_decorator(require_login)
    def get(self, request, *args, **kwargs):
        try:
            type_param = request.query_params.get("type", "ALL")
            excel_file = StringIO()

            csv_writer = csv.writer(excel_file)

            if type_param == "ALL":
                list_user = Users.objects.filter().order_by("-created_utc")
                list_user = [getListAccDeveloperSerializer(user).data for user in list_user]

                headers = ["User Name", "Email", "Company", "Phone Number", "Client Name", "Level", "Status", "Date Created", "Date Updated"]
                csv_writer.writerow(headers)

                for user_data in list_user:
                    user_row = [
                        user_data["user_name"],
                        user_data["email"],
                        user_data["company"],
                        user_data["phone"],
                        user_data["client_name"],
                        user_data["level_name"],
                        user_data["status_account"],
                        user_data["created_utc"],
                        user_data["updated_utc"],
                    ]
                    csv_writer.writerow(user_row)

                response = HttpResponse(excel_file.getvalue(), content_type="text/csv")

                response["Content-Disposition"] = 'attachment; filename="developer-zone.csv"'

                return response
            else:
                list_companies = Users.objects.filter(company__isnull=False).exclude(company="").values("company").distinct().order_by("company")
                headers = ["STT", "Company"]
                csv_writer.writerow(headers)

                for index, company in enumerate(list_companies):
                    company_row = [index + 1, company["company"]]
                    csv_writer.writerow(company_row)

                response = HttpResponse(excel_file.getvalue(), content_type="text/csv")

                response["Content-Disposition"] = 'attachment; filename="list-company.csv"'
                return response
        except Exception:
            # print('Error downloadListUser', e)
            return JsonResponse({"message": "Get unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)


class ChangePasswordDeveloperZone(APIView):
    @extend_schema(request=ChangePasswordDeveloperZoneSerializer, description="Change password user", tags=["ACCOUNT-MANAGEMENT"])
    @method_decorator(require_login)
    def post(self, request, *args, **kwargs):
        try:
            serializer = ChangePasswordDeveloperZoneSerializer(data=request.data)
            if serializer.is_valid():
                serializer.change_password()

                return Response({"message": "An email with user_name and password has been sent to the user."}, status=HTTP_200_OK)
            error_message = list(serializer.errors.values())[0][0]
            return Response({"message": error_message}, status=HTTP_400_BAD_REQUEST)
        except Exception:
            return Response({"message": "Change password user unsuccessfully!"}, status=HTTP_400_BAD_REQUEST)


class ChangeCompanyDeveloperView(APIView):
    @extend_schema(request=ChangeLevelDeveloperZoneSerializer, description="Change company field", tags=["ACCOUNT-MANAGEMENT"])
    @method_decorator(require_login)
    def patch(self, request, id):
        try:
            if not id:
                return Response({"message": "Not exist user"}, status=HTTP_404_NOT_FOUND)
            user = Users.objects.get(id=id)
            serializer = ChangeCompanyDeveloperZoneSerializer(data=request.data, context={"user": user})
            if serializer.is_valid():
                serializer.save()
                return Response({"message": "Update company successfully"}, status=HTTP_201_CREATED)
            error_message = list(serializer.errors.values())[0][0]
            return Response({"message": error_message}, status=HTTP_400_BAD_REQUEST)
        except Exception:
            return Response({"message": "Update company unsuccessfully"}, status=HTTP_400_BAD_REQUEST)


class ChangeUserInformationView(APIView):
    @extend_schema(request=ChangeUserInformationSerializer, description="Change company field", tags=["ACCOUNT-MANAGEMENT"])
    @method_decorator(require_login)
    def patch(self, request, id):
        try:
            if not id:
                return Response({"message": "Not exist user"}, status=HTTP_404_NOT_FOUND)
            user = Users.objects.get(id=id)
            serializer = ChangeUserInformationSerializer(user, data=request.data, context={"id": id}, partial=True)
            if serializer.is_valid():
                serializer.update(user, serializer.validated_data)
                return Response({"message": "Update user successfully"}, status=HTTP_201_CREATED)
            error_message = list(serializer.errors.values())[0][0]
            return Response({"message": error_message}, status=HTTP_400_BAD_REQUEST)
        except Exception:
            return Response({"message": "Update user unsuccessfully"}, status=HTTP_400_BAD_REQUEST)
