import hashlib
import os
import re
import uuid
from datetime import datetime

import dotenv
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode
from rest_framework import serializers

from user.models import (
    ClientParent,
    ClientsInformation,
    Levels,
    Roles,
    StatusAccountLog,
    Users,
)
from user.utils.email import send_email_user
from user.utils.redis_utils import (
    REFRESH_TOKEN_TTL_MINUTES,
    add_access_token_to_redis,
    adding_failed_login_user,
    adding_token_to_refresh,
    checking_login_failed,
    delete_access_token_from_redis,
    delete_token_to_refresh,
)
from user.utils.utils_serializer import (
    check_passw,
    gen_apikey_and_passcode,
    generate_bearer_token,
    generate_bearer_token_with_time,
    get_user_permissions,
    validate_password_zone,
)

dotenv.load_dotenv(dotenv.find_dotenv())


class UserSerializers(serializers.ModelSerializer):
    class Meta:
        model = Users
        fields = "__all__"


class UsersSerializers(serializers.ModelSerializer):
    class Meta:
        model = Users
        fields = "__all__"


class UserGetSerializers(serializers.ModelSerializer):
    role_id = serializers.SerializerMethodField()
    level_id = serializers.SerializerMethodField()
    level_name = serializers.SerializerMethodField()

    class Meta:
        model = Users
        fields = [
            "id",
            "first_name",
            "last_name",
            "company",
            "user_name",
            "email",
            "created_utc",
            "updated_utc",
            "status_code",
            "last_login",
            "role_id",
            "level_id",
            "level_name",
            "phone",
        ]

    def get_role_id(self, obj):
        return obj.level.level_name

    def get_level_id(self, obj):
        return obj.level.id

    def get_level_name(self, obj):
        return obj.level.level_name


# class GlobalConfigSerializers(serializers.ModelSerializer):
#     class Meta:
#         model = GlobalConfig
#         fields = '__all__'


class signUpGoogleSerializer(serializers.Serializer):
    accessToken = serializers.CharField(required=True)


class signInGoogleSerializer(serializers.Serializer):
    accessToken = serializers.CharField(required=True)


class SignInSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True)


class signUpSerializer(serializers.Serializer):
    email = serializers.CharField(required=True)
    password = serializers.CharField(required=True)


class verifyTokenRegisterSerializer(serializers.Serializer):
    token = serializers.CharField(required=True)
    userId = serializers.IntegerField(required=True)


class resendMailVerifyUserSerializer(serializers.Serializer):
    userId = serializers.IntegerField(required=True)


class forgotPasswordUserSerializer(serializers.Serializer):
    email = serializers.CharField(required=True)


class changePasswordAfterForgotUserSerializer(serializers.Serializer):
    password = serializers.CharField(required=True)
    token = serializers.CharField(required=True)


class changePasswordSerializer(serializers.Serializer):
    newPassword = serializers.CharField(required=True)
    oldPassword = serializers.CharField(required=True)


class updateProfileSerializer(serializers.Serializer):
    userName = serializers.CharField(required=True)


class changePasswordFirstTimeSerializer(serializers.Serializer):
    password = serializers.CharField(required=True)


class changeEmailSerializer(serializers.Serializer):
    password = serializers.CharField(required=True)
    email = serializers.CharField(required=True)


class subcriptionSerializer(serializers.Serializer):
    package_id = serializers.CharField(required=True)


class responsePaymentGatewaveSerializer(serializers.Serializer):
    ref_no = serializers.CharField(required=True)
    status = serializers.CharField(required=True)


class uploadFileSerializer(serializers.Serializer):
    file = serializers.FileField(required=True)


class uploadFileResponseSerializer(serializers.Serializer):
    file_name = serializers.CharField(help_text="Uploaded file name")
    file_url = serializers.URLField(help_text="URL to access the uploaded file")
    message = serializers.CharField(default="File uploaded successfully")


class SignInResponseDataSerializer(serializers.Serializer):
    access_token = serializers.CharField()
    refresh_token = serializers.CharField()
    permissions = serializers.ListField()


class SignInResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Sign-in successfully")
    data = SignInResponseDataSerializer()


class SignIn400ResponseSerializer(serializers.Serializer):
    error = serializers.CharField(default="Invalid request")


class SignIn401ResponseSerializer(serializers.Serializer):
    error = serializers.CharField(default="User Name or Password is incorrect")


class SignIn500ResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Your account has been deactivated.")


class userInfoResponseDataSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    email = serializers.CharField()
    user_name = serializers.CharField()
    permissions = serializers.ListField()


class userInfoResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="successfully")
    data = userInfoResponseDataSerializer()


class verifyTokenRegisterResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Verify token successfully")
    data = SignInResponseDataSerializer()


class resendMailVerifyUserResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Resend successfully!")


class forgotPasswordUserResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Forgot Password successfully!")


class changePasswordAfterForgotUserResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Change password successfully!")


class changePasswordResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Change password successfully!")


class changePasswordFirstTimeResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Change password successfully!")


class changeEmailResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Change email successfully!")


class checkPasswordResponseDataSerializer(serializers.Serializer):
    check = serializers.BooleanField(default=False)


class checkPasswordResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="successfully!")
    data = checkPasswordResponseDataSerializer()


class updateProfileResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Update profile successfully!")


class userLogoutResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Log-out successfully!")


class refreshTokenUserResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Refresh successfully")
    data = SignInResponseDataSerializer()


class signUpGoogleResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="User created successfully")
    data = SignInResponseDataSerializer()


class getListUserResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Get successfully")
    data = UserGetSerializers(many=True)


class createUserRequestSerializer(serializers.Serializer):
    email = serializers.CharField(required=True)
    userName = serializers.CharField(required=True)
    password = serializers.CharField(required=True)
    level = serializers.CharField(required=False)
    role = serializers.CharField(required=False)


class updateUserRequestSerializer(serializers.Serializer):
    userName = serializers.CharField(required=False)
    password = serializers.CharField(required=False)
    status = serializers.ChoiceField(required=False, choices=["ACTIVATE", "DEACTIVATE"])
    level = serializers.ChoiceField(required=False, choices=["SUPER-ADMIN", "ADMIN", "SUPER-PARENT"])


class createUserResponseDataSerializer(serializers.Serializer):
    useId = serializers.CharField()


class createUserResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Create User successfully")
    data = createUserResponseDataSerializer(many=False)


class updateUserResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Update User successfully")


class deleteUserResponseSerializer(serializers.Serializer):
    message = serializers.CharField(default="Delete User successfully")


class createUserAPIRequestSerializer(serializers.Serializer):
    email = serializers.CharField(required=True)
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)
    password = serializers.CharField(required=True)
    level = serializers.CharField(required=True)
    client_id = serializers.CharField(required=True)
    username = serializers.CharField(required=True)


class LevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = Levels
        fields = "__all__"


class getListAccDeveloperSerializer(serializers.ModelSerializer):
    client_name = serializers.SerializerMethodField()
    is_passcode = serializers.SerializerMethodField()
    level_name = serializers.SerializerMethodField()
    client_id = serializers.SerializerMethodField()

    class Meta:
        model = Users
        fields = (
            "id",
            "user_name",
            "email",
            "company",
            "created_utc",
            "updated_utc",
            "passcode",
            "is_passcode",
            "api_key",
            "level_id",
            "level_name",
            "status_account",
            "phone",
            "client_name",
            "first_name",
            "last_name",
            "client_id",
        )

    def get_client_id(self, obj):
        try:
            client_id = ClientParent.objects.get(user_id=obj.id).client_id
            clientID = ClientsInformation.objects.get(id=client_id).clientID
        except:
            return None
        return clientID

    def get_client_name(self, obj):
        try:
            client_id = ClientParent.objects.get(user_id=obj.id).client_id
            client_name = ClientsInformation.objects.get(id=client_id).name
        except:
            return None
        return client_name

    def get_is_passcode(self, obj):

        if obj.passcode:
            return True
        return False

    def get_level_name(self, obj):
        try:
            name = Levels.objects.get(id=obj.level_id).level_name
            return name
        except:
            print("Not found level name")
            return None


class getUserDeveloperSerializer(serializers.ModelSerializer):
    class Meta:
        model = Users
        fields = ("id", "user_name")


class createApiKeySerializer(serializers.Serializer):

    user_name = serializers.CharField(required=True)
    passcode = serializers.CharField(required=True)


class createAccountLandingSerializer(serializers.ModelSerializer):
    def get_default_role():
        try:
            return Roles.objects.get(id=1)
        except Roles.DoesNotExist:
            return None

    def get_default_level():
        try:
            return Levels.objects.get(id=1)
        except Roles.DoesNotExist:
            return None

    first_name = serializers.CharField(write_only=True)
    last_name = serializers.CharField(write_only=True)
    password = serializers.CharField(write_only=True)
    phone = serializers.CharField(required=False)
    company = serializers.CharField(required=False)
    level = serializers.PrimaryKeyRelatedField(queryset=Levels.objects.all(), default=get_default_level)
    role = serializers.PrimaryKeyRelatedField(queryset=Roles.objects.all(), default=get_default_role)

    class Meta:
        model = Users
        fields = "__all__"

    def validate_password(self, value):

        if not value:
            raise serializers.ValidationError("Please provide a password")

        if len(value) < 8:
            raise serializers.ValidationError("Password must contain at least 8 characters")

        if not any(char.isupper() for char in value):
            raise serializers.ValidationError("Password must have at least ONE uppercase character")

        if not any(char.isdigit() for char in value):
            raise serializers.ValidationError("Password must have at least ONE number")

        if not re.search("[~`!@#\$%\^&\*\(\)_\+\{\[\}\]\|\\:;\"'<,>\.?/]", value):
            raise serializers.ValidationError("Password must have at least ONE special character")

        return value

    def validate_phone(self, value):
        if len(value) < 7 or len(value) > 15:
            raise serializers.ValidationError("The phone number must contain between 10 and 15 digits.")
        if not value.isdigit():
            raise serializers.ValidationError("The phone number may only contain digits.")

        return value

    def validate_email(self, value):

        if not re.match("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", value) or len(value) > 100:
            raise serializers.ValidationError("Please use a valid email address")

        return value

    def validate(self, data):

        checkUser = Users.objects.filter(user_name=data["email"]).first()
        if checkUser:
            raise serializers.ValidationError("User already in use.")
        data["pwd_sha256"] = hashlib.sha256(data.get("password").encode()).hexdigest()
        return data

    def create(self, validated_data):

        user = Users.objects.create(
            first_name=validated_data["first_name"],
            last_name=validated_data["last_name"],
            user_name=validated_data["email"],
            email=validated_data["email"],
            pwd_sha256=validated_data["pwd_sha256"],
            created_utc=datetime.now(),
            updated_utc=datetime.now(),
            status_code="ACTIVATE",
            sign_up_type="individual",
            status_account="Pending",
            level=validated_data["level"],
            role=validated_data["role"],
            phone=validated_data["phone"],
            company=validated_data["company"],
        )

        record_log = StatusAccountLog.objects.create(
            user_name=user.user_name,
            after_action="Pending",
        )
        record_log.save()

        return user


class createRequestStatusSerializer(serializers.Serializer):

    status = serializers.CharField()
    note = serializers.CharField(allow_blank=True, max_length=512)
    username = serializers.CharField()
    client_id = serializers.CharField(allow_blank=True)
    passcode = serializers.CharField(allow_blank=True)

    def annouce_email(self, note, old_status, new_status, email, passcode):
        subject = "Account Registration Status Notification"
        message = {
            "Approved": """We are pleased to inform you that your account status has been approved.\n
            To begin using our services, please access our website.\n""",
            "Pending": "The status of your account remains pending.\n Please contact us if you have any questions.\n",
            "Denied": f"We regret to inform you that your account status has been denied due to the following reason: {note}.\n"
            "Please contact us if you have any questions.\n",
        }
        # send passcode generate automatically
        if old_status == "Pending" and new_status == "Approved":
            send_email_user(
                subject=subject,
                message=f"""We are pleased to inform you that your account status has been approved.\n
                        To begin using our services, please access our website.\n This is your passcode: {passcode}""",
                receiver_email=email,
            )
        else:
            send_email_user(
                subject=subject,
                message=message[new_status],
                receiver_email=email,
            )

    def validate(self, data):
        user = Users.objects.get(user_name=data["username"])
        if not user:
            raise serializers.ValidationError("Not found user")
        if data["status"] == user.status_account and data["status"] != "Approved" and user.status_account != "Approved":
            raise serializers.ValidationError(f"The current status is already {user.status_account}. It cannot be {user.status_account} again.")
        return data

    def create(self, validated_data):
        user = Users.objects.get(user_name=validated_data["username"])
        if "note" not in validated_data:
            validated_data["note"] = None
        response = {
            "status": validated_data["status"],
            "before_status": user.status_account,
            "username": validated_data["username"],
            "note": validated_data["note"] if validated_data["note"] else None,
        }
        try:
            client = ClientsInformation.objects.filter(clientID=validated_data["client_id"]).first()
            ClientParent.objects.create(user_id=user.id, client_id=client.id)
        except:
            print("User does not have client")
        user.status_account = validated_data["status"]
        response["client_name"] = client.name if client else None
        user.passcode = validated_data["passcode"]
        # create automatically passcode when admin approved account, need check if the client exists or not.
        if response["before_status"] == "Pending" and response["status"] == "Approved":
            if response["client_name"]:
                gen_apikey_and_passcode(user, client.clientID)
            # else:
            #     raise serializers.ValidationError("Can't create passcode automatically because not found your client !!! ")
        user.save()
        self.annouce_email(validated_data["note"], response["before_status"], response["status"], user.email, validated_data["passcode"])

        return response


class getProcessAccountStatusSerializer(serializers.ModelSerializer):
    class Meta:

        model = StatusAccountLog
        fields = "__all__"


class resetPasscodeSerializer(serializers.Serializer):

    old_passcode = serializers.CharField(max_length=64, required=True)
    new_passcode = serializers.CharField(max_length=64, required=True)
    new_passcode_again = serializers.CharField(max_length=64, required=True)

    def validate_old_passcode(self, value):
        if not value:
            raise serializers.ValidationError("Need provide current passcode")

        id = self.context["id_user"]
        user = Users.objects.get(id=id)
        if not user.passcode:
            raise serializers.ValidationError("User does not have passcode")

        if value != user.passcode:

            raise serializers.ValidationError("Current passcode wrong")
        return value

    def validate_new_passcode(self, value):
        if not value:
            raise serializers.ValidationError("Need provide new passcode")

        id = self.context["id_user"]
        user = Users.objects.get(id=id)

        if value == user.passcode:
            raise serializers.ValidationError("New passcode cannot be the same as the old passcode.")

        return value

    def validate_new_passcode_again(self, value):

        if not value:
            raise serializers.ValidationError("Need provide new passcode")

        id = self.context["id_user"]
        user = Users.objects.get(id=id)

        if value == user.passcode:
            raise serializers.ValidationError("New passcode_again cannot be the same as the old passcode.")

        return value

    def validate(self, data):

        if data["new_passcode_again"] != data["new_passcode"]:

            raise serializers.ValidationError("Wrong new passcode. Need type again!")
        return data

    def save(self):

        id = self.context["id_user"]
        user = Users.objects.get(id=id)
        user.passcode = self.validated_data["new_passcode"]
        user.save()


class loginLandingPageSerializer(serializers.Serializer):

    username = serializers.CharField(max_length=25)
    password = serializers.CharField(max_length=25)

    def validate_username(self, value):

        user = Users.objects.get(user_name=value)
        if not user:
            raise serializers.ValidationError("User Name or Password is incorrect")

        if user.status_account != "Approved":
            raise serializers.ValidationError("The user does not access to our website.")

        if user.status_code != "ACTIVATE":
            raise serializers.ValidationError("Your account has been deactivated.")

        return value

    def validate_password(self, value):

        if not value:
            raise serializers.ValidationError("Please provide a password")

        if len(value) < 8:
            raise serializers.ValidationError("Password must contain at least 8 characters")

        if not any(char.isupper() for char in value):
            raise serializers.ValidationError("Password must have at least ONE uppercase character")

        if not any(char.isdigit() for char in value):
            raise serializers.ValidationError("Password must have at least ONE number")

        if not re.search("[~`!@#\$%\^&\*\(\)_\+\{\[\}\]\|\\:;\"'<,>\.?/]", value):
            raise serializers.ValidationError("Password must have at least ONE special character")

        return value

    def validate(self, data):

        user = Users.objects.get(user_name=data.get("username"))
        if checking_login_failed(user):
            raise serializers.ValidationError("You have inputted the wrong password 5 times in a row. Your account has been locked for 30 minutes.")

        if not hashlib.sha256(data.get("password").encode()).hexdigest() == user.pwd_sha256:
            adding_failed_login_user(user)
            raise serializers.ValidationError("User Name or password is incorrect")

        print(get_user_permissions(user))
        return data

    def create(self, validated_data):

        user = Users.objects.get(user_name=validated_data["username"])
        permissions = get_user_permissions(user)

        token = generate_bearer_token(user.id, user.user_name, permissions)
        refresh_token = generate_bearer_token_with_time({"userId": user.id, "type": "REFRESH_TOKEN"}, REFRESH_TOKEN_TTL_MINUTES)
        adding_token_to_refresh(user, refresh_token)
        add_access_token_to_redis(user, token)

        dataOutput = {
            "id": user.id,
            "email": user.email,
            "user_name": user.user_name,
        }

        response_data = {"access_token": token, "refresh_token": refresh_token, "permissions": permissions, "user": dataOutput}

        return response_data


class forgotPasscodeRequestSerializer(serializers.Serializer):
    email = serializers.CharField(required=True)

    def validate_email(self, value):
        if not value:
            raise serializers.ValidationError("Email field is required")
        id = self.context["id_user"]
        user = Users.objects.get(id=id)
        if user.email != value:
            raise serializers.ValidationError("Email does not match")
        if not user.passcode:
            raise serializers.ValidationError("User does not have passcode")
        self.context["user"] = user
        return value

    def save(self):
        user = self.context["user"]
        token = str(uuid.uuid4())
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        domain_reset = {"STAGING": "staging.developer.myrcvr.com", "PROD": "developer.myrcvr.com"}
        reset_link = f"https://{domain_reset[str(os.getenv('ENV'))]}/forgot-passcode/{uid}/{token}/"

        # send email
        send_email_user(subject="Passcode Reset Request", message=f"Click the following link to reset your passcode: {reset_link}\n", receiver_email=user.email)


class forgotPasscodeSerializer(serializers.Serializer):

    new_passcode = serializers.CharField()

    def validate(self, data):

        if not data["new_passcode"]:
            raise serializers.ValidationError("Need provided new passcode")

        uid = urlsafe_base64_decode(self.context["uid"]).decode()
        user = Users.objects.get(pk=uid)
        data["user"] = user
        if not user:
            raise serializers.ValidationError("User not exist")

        return data

    def save(self):

        self.validated_data["user"].passcode = self.validated_data["new_passcode"]
        self.validated_data["user"].save()


class changePasswordZoneSerializer(changePasswordSerializer):
    def validate_oldPassword(self, value):

        check_pass = validate_password_zone(value)
        if check_pass is not None:
            raise serializers.ValidationError(check_pass)
        if not check_passw(value, self.context["user"].pwd_sha256):
            raise serializers.ValidationError("The current password is incorrect.")

        return value

    def validate_newPassword(self, value):

        check_pass = validate_password_zone(value)

        if check_pass is not None:
            raise serializers.ValidationError(check_pass)

        if check_passw(value, self.context["user"].pwd_sha256):
            raise serializers.ValidationError("The new password must be different from the current password.")

        return value

    def save(self):

        hashed_password = hashlib.sha256(self.validated_data["newPassword"].encode()).hexdigest()
        self.context["user"].pwd_sha256 = hashed_password
        self.context["user"].save()


class ChangeLevelDeveloperZoneSerializer(serializers.Serializer):
    user_id = serializers.CharField(required=True)
    level_id = serializers.CharField(required=True)
    client_id = serializers.CharField(required=False, allow_blank=True)

    def validate(self, data):
        try:
            user = Users.objects.get(id=data["user_id"])
            level = Levels.objects.get(id=data["level_id"])
            # 1: role SUPPER_PARENT
            if str(user.level_id) == data.get("level_id") and data.get("level_id") != '1':
                raise serializers.ValidationError("The new role is the same as the current role.")
            else:
                data["user"] = user
                data["level"] = level
                return data
        except Users.DoesNotExist:
            raise serializers.ValidationError("User not found.")
        except Levels.DoesNotExist:
            raise serializers.ValidationError("Level not found.")

    def save(self):
        self.validated_data["user"].level_id = self.validated_data["level_id"]  
        
        # 1: id of role SUPPER-PARENT
        if "client_id" in self.validated_data and self.validated_data["level_id"] == '1':
            client = ClientsInformation.objects.get(clientID=self.validated_data["client_id"])
            parent_user, created = ClientParent.objects.get_or_create(user_id=self.validated_data["user_id"], client_id=client.id)
        
            if created is not True:
                parent_user.client_id = client.id
                parent_user.save()

        if not self.validated_data.get("client_id") and self.validated_data.get("level_id") == '1':
            raise serializers.ValidationError("Client not found.")
            
        self.validated_data["user"].save()

        delete_access_token_from_redis(self.validated_data["user_id"])

        send_email_user(
            subject="Level User Change Request",
            message=(f"Your account level has been changed  successfully\n" f"- Current level: {self.validated_data['level'].level_name}\n"),
            receiver_email=self.validated_data["user"].email,
        )
        return self.validated_data["level"].level_name


class ChangePasswordDeveloperZoneSerializer(serializers.Serializer):
    user_id = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)

    def validate(self, data):
        check_pass = validate_password_zone(data["new_password"])
        if check_pass is not None:
            raise serializers.ValidationError(check_pass)

        try:
            user = Users.objects.get(id=data["user_id"])
        except Users.DoesNotExist:
            raise serializers.ValidationError("User not found")

        data["user"] = user
        return data

    def change_password(self):
        hashed_password = hashlib.sha256(self.validated_data["new_password"].encode()).hexdigest()
        self.validated_data["user"].pwd_sha256 = hashed_password
        self.validated_data["user"].save()

        send_email_user(
            subject="Password Reset Request",
            message=(
                f"Your account password has been changed successfully\n"
                f"- user_name: {self.validated_data['user'].user_name}\n"
                f"- password: {self.validated_data['new_password']}\n"
            ),
            receiver_email=self.validated_data["user"].email,
        )


class ChangeCompanyDeveloperZoneSerializer(serializers.Serializer):

    company = serializers.CharField(required=True)

    def validate(self, data):

        if data["company"] is None:
            raise serializers.ValidationError("Company is required")

        return data

    def save(self):
        self.context["user"].company = self.validated_data["company"]
        self.context["user"].save()


class ChangeUserInformationSerializer(serializers.Serializer):
    phone = serializers.CharField(required=False, allow_blank=True)
    company = serializers.CharField(required=False, allow_blank=True)
    email = serializers.CharField(required=False, allow_blank=True)
    first_name = serializers.CharField(required=False, allow_blank=True)
    last_name = serializers.CharField(required=False, allow_blank=True)
    user_name = serializers.CharField(required=False, allow_blank=True)
    client_id = serializers.CharField(required=False, allow_blank=True)

    def validate_phone(self, value):
        if value:
            if len(value) < 7 or len(value) > 15:
                raise serializers.ValidationError("The phone number must contain between 10 and 15 digits.")
            if not value.isdigit():
                raise serializers.ValidationError("The phone number may only contain digits.")

        return value

    def validate_email(self, value):

        if value:
            emails = list(Users.objects.all().values_list("email", flat=True))
            emails.remove(Users.objects.get(id=self.context["id"]).email)
            if value in emails:
                raise serializers.ValidationError("The email already exists")
            if not re.match("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", value) or len(value) > 100:
                raise serializers.ValidationError("Please use a valid email address")

        return value

    def validate_user_name(self, value):

        if value:
            usernames = list(Users.objects.all().values_list("user_name", flat=True))
            usernames.remove(Users.objects.get(id=self.context["id"]).user_name)
            if value in usernames:
                raise serializers.ValidationError("The username already exists")

        return value

    def update(self, instance, validated_data):

        if "client_id" in validated_data:
            try:
                id_client = ClientsInformation.objects.get(clientID=validated_data["client_id"])
                parent_user = ClientParent.objects.get(user=self.context["id"])
                parent_user.client = id_client
                parent_user.save()
            except:
                print("Can't update client id")


        change_text=[]
        fields_to_check = ["first_name", "last_name", "user_name", "email", "phone", "company"]

        for field in fields_to_check:
            new_value = validated_data.get(field)
            old_value = getattr(instance, field)

            if new_value is not None and old_value != new_value:
                change_text.append(f"Your new {field.replace('_', ' ')} is: {new_value}")
                setattr(instance, field, new_value)

                # instance[field] = new_value   
                
        if len(change_text) > 0:
            instance.save()
            send_email_user(
                "Notification about your username change",
                message="\n".join(change_text),
                receiver_email=instance.email,
            )
        return instance


class createPasscodeLandingSerializer(serializers.Serializer):

    passcode = serializers.CharField(max_length=64)

    def get_user(self):
        return self.context["user"]

    def validate_phone(self, value):
        if not value:
            raise serializers.ValidationError("Passcode is required.")
        if len(value) < 6:
            raise serializers.ValidationError("Passcode need at least 6 character")
        return value

    def validate(self, data):

        user = self.get_user()
        if user.status_account != "Approved":
            raise serializers.ValidationError("Invalid account status")
        if user.passcode:
            raise serializers.ValidationError("The passcode already exist")
        return data

    def save(self):
        user = self.get_user()
        try:
            client_id = ClientParent.objects.get(user=user.id).client_id
            clientID = ClientsInformation.objects.get(id=client_id).clientID
        except:
            raise serializers.ValidationError("Client not exist for your account")

        user.passcode = self.validated_data["passcode"]
        user.save()
        gen_apikey_and_passcode(user, clientID)
