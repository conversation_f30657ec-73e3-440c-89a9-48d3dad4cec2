import os
import random
import smtplib
import string
from datetime import datetime
from email.mime.image import MIMEImage
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.text import MIMEText

from dotenv import load_dotenv
from jinja2 import Template

from data import models as md

# ----------------------------------- Main Functions -----------------------------------#

load_dotenv()
SMTP_SERVER = os.getenv("SMTP_SERVER")
SMTP_PORT = int(os.getenv("SMTP_PORT"))
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")
SMTP_SENDERMAIL = os.getenv("SMTP_SENDERMAIL")

MAIL_RECIPIENT = os.getenv("EMAIL_RECIPIENT")
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def sendMailNotifyUploadFile(client, mid, processor, status, error_mess=None, file_name=None, dev_error=None):

    # subject = f'{crm.loginType} Account Invalid, Clients {client_ns}, Datetime: {str(datetime.now().date())}'
    subject = f"Upload: {status}, Client: {client}, Processor: {processor}, Datetime: {str(datetime.now().date())}"

    logo_path = f"{BASE_DIR}/static/images/logo.png"
    if status == "Success":
        html = f"""\
        <html>
            <body>
                <p>Dear Ops team,<br><br>
                    Upload file for {client} - {processor} has been processed successfully.<br>
                    <ul>
                        <li> Client: {client}</li>
                        <li> MID: {mid}</li>
                        <li> Processor: {processor}</li>
                        <li> Status: {status}</li>
                        <li> File name: {file_name}</li>
                    </ul>

                    Best regards,<br>
                    RCVR Team.<br><br>
                   <img src="cid:logo">
                </p>
            </body>
        </html>
        """
    else:
        html = f"""\
        <html>
        <body>
            <p>Dear Ops team,<br><br>
                Upload file for {client} - {processor} has been processed with error.<br>
                <ul>
                    <li> Client: {client}</li>
                    <li> MID: {mid}</li>
                    <li> Processor: {processor}</li>
                    <li> Status: {status}</li>
                    <li> File name: {file_name}</li>
                    <li> Error message: {error_mess} </li>
                    <li> Developer error: {dev_error} </li>
                </ul>
                Please check your upload file!<br><br>

                Best regards,<br>
                RCVR Team.<br><br>
            <img src="cid:logo">
            </p>
        </body>
        </html>
        """

    for recipient in MAIL_RECIPIENT.split(", "):
        msg = MIMEMultipart("related")
        msg["From"] = SMTP_SENDERMAIL
        msg["To"] = recipient
        msg["Subject"] = subject

        msg.attach(MIMEText(html, "html"))

        with open(logo_path, "rb") as f:
            logo = MIMEImage(f.read(), _subtype=os.path.splitext(logo_path)[1][1:])
            logo.add_header("Content-ID", "<logo>")
            logo.add_header("Content-Disposition", "attachment", filename=os.path.basename(logo_path))
            msg.attach(logo)

        try:
            with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as server:
                server.login(SMTP_SENDERMAIL, SMTP_PASSWORD)
                server.send_message(msg)
            print("Email sent successfully!")
        except Exception as e:
            print(f"Failed to send email: {e}")


def send_mail_forgot_password(MAIL_RECIPIENT, new_password):
    subject = "Reset Password"
    logo_path = f"{BASE_DIR}/static/images/logo.png"

    html = f"""\
    <html>
      <body>
        <p>Dear user,<br><br>
            We received a request to reset your password for your account.<br>
            Your new password: {new_password}<br><br>
            Best regards,<br>
            RCVR Team.<br><br>
           <img src="cid:logo">
        </p>
      </body>
    </html>
    """

    msg = MIMEMultipart("related")
    msg["From"] = SMTP_SENDERMAIL
    msg["To"] = MAIL_RECIPIENT
    msg["Subject"] = subject

    msg.attach(MIMEText(html, "html"))

    with open(logo_path, "rb") as f:
        logo = MIMEImage(f.read(), _subtype=os.path.splitext(logo_path)[1][1:])
        logo.add_header("Content-ID", "<logo>")
        logo.add_header("Content-Disposition", "attachment", filename=os.path.basename(logo_path))
        msg.attach(logo)

    try:
        with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as server:
            server.login(SMTP_SENDERMAIL, SMTP_PASSWORD)
            server.send_message(msg)
        print("Email sent successfully!")
    except Exception as e:
        print(f"Failed to send email: {e}")
        return False

    return True


def generate_password():
    password_gen = ""
    password_gen += random.choice(string.ascii_uppercase)
    password_gen += random.choice("!@#")
    password_gen += random.choice("1234567890")
    for _ in range(10):
        password_gen += random.choice(string.ascii_lowercase)

    return password_gen


def render_html(template_path, context):

    with open(template_path, "r") as file:
        html_template = file.read()

    template = Template(html_template)

    rendered_html = template.render(context)

    return rendered_html


def send_email_user(subject, message, receiver_email):
    logo_path = f"{BASE_DIR}/static/images/logo.png"
    message = message.replace("\n", "<br>")

    html = f"""\
    <html>
      <body>
        <p>Dear User,<br><br>
            {message}<br><br>
            Best regards,<br>
            RCVR Team.<br><br>
           <img src="cid:logo">
        </p>
      </body>
    </html>
    """

    msg = MIMEMultipart()
    msg["From"] = SMTP_SENDERMAIL
    msg["To"] = receiver_email
    msg["Subject"] = subject

    body = MIMEText(html, "html")
    msg.attach(body)

    with open(logo_path, "rb") as f:
        logo = MIMEImage(f.read(), _subtype=os.path.splitext(logo_path)[1][1:])
        logo.add_header("Content-ID", "<logo>")
        # logo.add_header('Content-Disposition', 'attachment', filename=os.path.basename(logo_path))
        msg.attach(logo)

    try:
        with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as server:
            server.login(SMTP_SENDERMAIL, SMTP_PASSWORD)
            server.send_message(msg)
        print(f"Email sent to {receiver_email} successfully!")
    except Exception as e:
        print(e)
        return False

    return True


def sendEmailMissingFile(date=None):

    subject = f"Verifi SFTP missing file on {date}, Datetime: {str(datetime.now().date())}"

    logo_path = f"{BASE_DIR}/static/images/logo.png"

    html = f"""\
    <html>
      <body>
        <p>Dear Ops team,<br><br>
            Error message: SFTP file from Verifi is missing on {date}.
            <br>
            Please review!<br><br>

            Best regards,<br>
            RCVR Team.<br><br>
           <img src="cid:logo">
        </p>
      </body>
    </html>
    """

    for recipient in MAIL_RECIPIENT.split(", "):
        msg = MIMEMultipart("related")
        msg["From"] = SMTP_SENDERMAIL
        msg["To"] = recipient
        msg["Subject"] = subject

        msg.attach(MIMEText(html, "html"))

        with open(logo_path, "rb") as f:
            logo = MIMEImage(f.read(), _subtype=os.path.splitext(logo_path)[1][1:])
            logo.add_header("Content-ID", "<logo>")
            logo.add_header("Content-Disposition", "attachment", filename=os.path.basename(logo_path))
            msg.attach(logo)

        try:
            with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as server:
                server.login(SMTP_SENDERMAIL, SMTP_PASSWORD)
                server.send_message(msg)
            print("Email sent successfully!")
        except Exception as e:
            print(f"Failed to send email: {e}")


def sendEmailInvalidCrm(loginID, error_mess):

    crm = md.ClientLoginInformation.objects.filter(loginID=loginID).first()

    client_ns = " - ".join(
        list(set(md.ClientLoginInformation.objects.filter(internalLoginID=loginID, mainClientUsed__isnull=False).values_list("mainClientUsed", flat=True)))
    )
    if crm is None:
        return False

    subject = f"{crm.loginType} Account Invalid, Clients {client_ns}, Datetime: {str(datetime.now().date())}"

    logo_path = f"{BASE_DIR}/static/images/logo.png"

    html = f"""\
    <html>
      <body>
        <p>Dear Ops team,<br><br>
            The following {crm.loginType} account of Clients {client_ns} has become Invalid:<br>
            <ul>
                <li> Internal Record ID: {crm.loginID}</li>
                <li> Platform: {crm.loginPlatform}</li>
                <li> Type: {crm.loginType}</li>
                <li> CRM Number: {crm.crmNumber}</li>
                <li> Client: {client_ns}</li>
                <li> Username: {crm.username}</li>
                <li> Password: {('*******' + str(crm.password)[-4:]) if crm.password is not None else 'None'}</li>
                <li> API key: {('*******' + str(crm.API_key)[-4:]) if crm.API_key is not None else 'None'}</li>
                <li> Validation status: {crm.validation} </li>
                <li> Error message: {error_mess} </li>
            </ul>
            Please review!<br><br>

            Best regards,<br>
            RCVR Team.<br><br>
           <img src="cid:logo">
        </p>
      </body>
    </html>
    """

    for recipient in MAIL_RECIPIENT.split(", "):
        msg = MIMEMultipart("related")
        msg["From"] = SMTP_SENDERMAIL
        msg["To"] = recipient
        msg["Subject"] = subject

        msg.attach(MIMEText(html, "html"))

        with open(logo_path, "rb") as f:
            logo = MIMEImage(f.read(), _subtype=os.path.splitext(logo_path)[1][1:])
            logo.add_header("Content-ID", "<logo>")
            logo.add_header("Content-Disposition", "attachment", filename=os.path.basename(logo_path))
            msg.attach(logo)

        try:
            with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as server:
                server.login(SMTP_SENDERMAIL, SMTP_PASSWORD)
                server.send_message(msg)
            print("Email sent successfully!")
        except Exception as e:
            print(f"Failed to send email: {e}")
