import os

from django.core.cache import cache
from dotenv import load_dotenv

from user.models import GlobalConfig

load_dotenv()
TOKEN_TTL_MINUTES = int(os.getenv("TOKEN_TTL_MINUTES"))
REFRESH_TOKEN_TTL_MINUTES = int(os.getenv("REFRESH_TOKEN_TTL_MINUTES"))


def push_data_to_redis(key, data, ttl):
    cache.set(key, data, int(ttl))


def get_data_from_redis(key):
    data = cache.get(key)
    return data


def delete_data_from_redis(key):
    cache.delete(key)


def adding_failed_login_user(user):
    data_redis = get_data_from_redis(str("FAILED_USER_FLOFIN_COUNT_" + str(user.id)))
    count_data = 1
    if data_redis is not None:
        count_data = int(data_redis) + 1
    push_data_to_redis(str("FAILED_USER_FLOFIN_COUNT_" + str(user.id)), count_data, 1800)


def checking_login_failed(user):
    data_redis = get_data_from_redis(str("FAILED_USER_FLOFIN_COUNT_" + str(user.id)))
    if data_redis is not None:
        maximin_count = GlobalConfig.objects.filter(config_key="NUMBER_MAX_LOGIN_FAILED").first().config_value
        maximin_count = int(maximin_count)
        if maximin_count > data_redis:
            return False
        else:
            return True
    return False


def delete_count_login_failed(user):
    data_redis = get_data_from_redis(str("FAILED_USER_FLOFIN_COUNT_" + str(user.id)))
    if data_redis is not None:
        delete_data_from_redis(str("FAILED_USER_FLOFIN_COUNT_" + str(user.id)))


def adding_token_to_refresh(user, token):
    push_data_to_redis(
        str("FLOFIN_TOKEN_USER" + str(user.id)),
        token,
        int(REFRESH_TOKEN_TTL_MINUTES * 60),
    )


def delete_token_to_refresh(user):
    data_redis = get_data_from_redis(str("FLOFIN_TOKEN_USER" + str(user.id)))
    if data_redis is not None:
        delete_data_from_redis(str("FLOFIN_TOKEN_USER" + str(user.id)))


def checking_token_to_refresh(user_id, token):
    data_redis = get_data_from_redis(str("FLOFIN_TOKEN_USER" + str(user_id)))
    if data_redis is not None and data_redis == token:
        return True
    return False

def add_access_token_to_redis(user, token):
    push_data_to_redis(str("FLOFIN_ACCESS_TOKEN_USER_" + str(user.id)), token, int(TOKEN_TTL_MINUTES * 60))

def delete_access_token_from_redis(user_id):
    key = str("FLOFIN_ACCESS_TOKEN_USER_" + str(user_id))
    data_redis = get_data_from_redis(key)
    if (data_redis) is not None:
        delete_data_from_redis(key)


def checking_access_token_exist(user_id, token):
    key = str("FLOFIN_ACCESS_TOKEN_USER_" + str(user_id))
    data_redis = get_data_from_redis(key)

    if data_redis is not None and data_redis == token:
        return True
    return False