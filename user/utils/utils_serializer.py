import base64
import hashlib
import os
import re
from datetime import datetime, timedelta

import jwt
from cryptography.fernet import <PERSON><PERSON><PERSON>
from dotenv import load_dotenv

from user.models import Users

load_dotenv()
TOKEN_TTL_MINUTES = int(os.getenv("TOKEN_TTL_MINUTES"))


def get_user_permissions(user: Users):
    return f"{user.level.level_name}_{user.role.role_name}"


def generate_bearer_token(user_id, user_name, permissions: str):
    exp = datetime.now() + timedelta(minutes=TOKEN_TTL_MINUTES)
    # permissions_data = list(permissions)
    user_id_str = str(user_id)
    user_name_str = str(user_name)

    token_payload = {
        "id": user_id_str,
        "user_name": user_name_str,
        "permissions": permissions,
        "type": "ACCESS_TOKEN",
        "exp": int(exp.timestamp()),  # Convert exp to a Unix timestamp
    }
    access_token = jwt.encode(token_payload, os.getenv("SECRET_KEY", "secret"), algorithm="HS256")
    return str(access_token)


def generate_bearer_token_with_time(dataInput, ttlTime):
    exp = datetime.utcnow() + timedelta(minutes=ttlTime)
    dataInput["exp"] = int(exp.timestamp())
    access_token = jwt.encode(dataInput, os.getenv("SECRET_KEY", "secret"), algorithm="HS256")
    return str(access_token)


def validate_password_zone(password):
    if not password:
        return "Please provide a password"

    if len(password) < 8:
        return "Password must contain at least 8 characters"

    if not any(char.isupper() for char in password):
        return "Password must have at least ONE uppercase character"

    if not any(char.isdigit() for char in password):
        return "Password must have at least ONE number"

    if not re.search("[~`!@#\$%\^&\*\(\)_\+\{\[\}\]\|\\:;\"'<,>\.?/]", password):
        return "Password must have at least ONE special character"


def encode_password(password):
    return hashlib.sha256(password.encode()).hexdigest()


def check_passw(password, hashed_password):
    return hashlib.sha256(password.encode()).hexdigest() == hashed_password


def generateCustomerApiKey(user_id, user_name, clientID=None):
    user_id_str = str(user_id)
    user_name_str = str(user_name)

    token_payload = {
        "id": user_id_str,
        "user_name": user_name_str,
        "clientID": clientID,
    }
    access_token = jwt.encode(token_payload, os.getenv("SECRET_KEY", "secret"), algorithm="HS256")
    return str(access_token)


def generate_key(passcode):
    """
    Generate a key for encryption and decryption based on the passcode.
    """
    passcode = passcode.encode()  # Convert to bytes
    key = hashlib.sha256(passcode).digest()  # Hash the passcode
    return base64.urlsafe_b64encode(key)  # Encode in a URL-safe format


def encrypt_token(token, passcode):
    """
    Encrypt a token using the passcode.
    """
    key = generate_key(passcode)
    fernet = Fernet(key)
    encrypted_token = fernet.encrypt(token.encode())
    return encrypted_token


def decrypt_token(encrypted_token, passcode):
    """
    Decrypt a token using the passcode.
    """
    key = generate_key(passcode)
    fernet = Fernet(key)
    try:
        decrypted_token = fernet.decrypt(encrypted_token).decode()
        return decrypted_token
    except Exception:
        print("Invalid passcode or corrupted token")
        return None


def gen_apikey_and_passcode(user: Users, clientID):

    token = generateCustomerApiKey(user.id, user.user_name, clientID=clientID)
    user.api_key = token
    user.save()

    encrypted_token = encrypt_token(user.api_key, user.passcode)
    decrypted_token = decrypt_token(encrypted_token, user.passcode)

    if decrypted_token == token:
        return True

    return False
