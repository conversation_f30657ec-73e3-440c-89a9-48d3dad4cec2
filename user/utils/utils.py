import hashlib
import os
import random
import re
import string
from datetime import datetime, timedelta

import jwt
from django.http import JsonResponse
from dotenv import load_dotenv
from rest_framework.status import (
    HTTP_401_UNAUTHORIZED,
    HTTP_413_REQUEST_ENTITY_TOO_LARGE,
)

from user.models import Users
from user.serializers import UserSerializers
from user.utils.redis_utils import checking_access_token_exist, checking_token_to_refresh

# Get the current working directory
current_directory = os.path.dirname(os.path.abspath(__file__))

load_dotenv()
TOKEN_TTL_MINUTES = int(os.getenv("TOKEN_TTL_MINUTES"))


def encode_to_sha256(password):
    return hashlib.sha256(password.encode()).hexdigest()


def check_password(hashed_password, password):
    return hashlib.sha256(password.encode()).hexdigest() == hashed_password


def get_user_permissions(user: Users):
    return f"{user.level.level_name}_{user.role.role_name}"


def generate_bearer_token(user_id, user_name, permissions: str):
    exp = datetime.now() + timedelta(minutes=TOKEN_TTL_MINUTES)
    # permissions_data = list(permissions)
    user_id_str = str(user_id)
    user_name_str = str(user_name)

    token_payload = {
        "id": user_id_str,
        "user_name": user_name_str,
        "permissions": permissions,
        "type": "ACCESS_TOKEN",
        "exp": int(exp.timestamp()),  # Convert exp to a Unix timestamp
    }
    access_token = jwt.encode(token_payload, os.getenv("SECRET_KEY", "secret"), algorithm="HS256")
    return str(access_token)


def generateCustomerApiKey(user_id, user_name, clientID=None):
    user_id_str = str(user_id)
    user_name_str = str(user_name)

    token_payload = {
        "id": user_id_str,
        "user_name": user_name_str,
        "clientID": clientID,
    }
    access_token = jwt.encode(token_payload, os.getenv("SECRET_KEY", "secret"), algorithm="HS256")
    return str(access_token)


def generate_bearer_token_with_time(dataInput, ttlTime):
    exp = datetime.utcnow() + timedelta(minutes=ttlTime)
    dataInput["exp"] = int(exp.timestamp())
    access_token = jwt.encode(dataInput, os.getenv("SECRET_KEY", "secret"), algorithm="HS256")
    return str(access_token)


def validateToken(token):
    try:
        payload = jwt.decode(token, os.getenv("SECRET_KEY", "secret"), algorithms=["HS256"])
        return payload
    except jwt.exceptions.ExpiredSignatureError as e:
        raise e


def handle_error(error):
    return JsonResponse({"success": False, "error": str(error)})


def handle_success(message, data, status):
    return JsonResponse({"message": message, "data": data}, status=status)


def get_random_string(length):
    result_str = "".join(random.choice(string.ascii_letters) for i in range(length))
    return result_str


def require_login(view_func):
    def wrapper(request, *args, **kwargs):
        bearerToken = request.headers.get("Authorization")
        if not bearerToken or bearerToken is None:
            return handle_error("No token provided")
        token = bearerToken[7:]

        try:
            payload = validateToken(token)
            temp = Users.objects.get(chargeback_id=payload["id"])
            permission = payload["permissions"]
            serializer = UserSerializers(temp)
            request.user = dict(serializer.data)
            request.user["permission"] = permission

            # current add new token to redis
            # checkToken = checking_access_token_exist(payload['id'], token)
            # print(payload['id'])
            # if checkToken is False:
            #     print(1)
            #     return JsonResponse(
            #         {"success": False, "error": str("Invalid token")},
            #         status=HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            #     )
                
            if payload["type"] != "ACCESS_TOKEN":
                return JsonResponse(
                    {"success": False, "error": str("Invalid token")},
                    status=HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                )
        except jwt.exceptions.InvalidTokenError:
            return JsonResponse(
                {"success": False, "error": str("Invalid token")},
                status=HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            )
        except Exception:
            return JsonResponse(
                {"success": False, "error": str("Token expire time")},
                status=HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            )

        return view_func(request, *args, **kwargs)

    return wrapper


def require_login_admin(view_func):
    def wrapper(request, *args, **kwargs):
        bearerToken = request.headers.get("Authorization")
        if not bearerToken or bearerToken is None:
            return handle_error("No token provided")
        token = bearerToken[7:]

        try:
            payload = validateToken(token)
            temp = Users.objects.get(chargeback_id=payload["id"])
            permission = payload["permissions"]
            serializer = UserSerializers(temp)
            request.user = dict(serializer.data)
            request.user["permission"] = permission
            if payload["type"] != "ACCESS_TOKEN" or ("SUPER-ADMIN_ADMIN" not in permission and "GLOBAL-SUPER-ADMIN" not in permission):
                return JsonResponse(
                    {"success": False, "error": str("Invalid permission")},
                    status=HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                )
        except jwt.exceptions.InvalidTokenError:
            return JsonResponse(
                {"success": False, "error": str("Invalid token")},
                status=HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            )
        except Exception:
            return JsonResponse(
                {"success": False, "error": str("Token expire time")},
                status=HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            )

        return view_func(request, *args, **kwargs)

    return wrapper


def require_token_refresh(view_func):
    def wrapper(request, *args, **kwargs):
        bearerToken = request.headers.get("Authorization")
        if not bearerToken or bearerToken is None:
            return handle_error("No token provided")
        token = bearerToken[7:]

        try:
            payload = validateToken(token)
            temp = Users.objects.get(chargeback_id=payload["userId"])
            serializer = UserSerializers(temp)
            request.user = dict(serializer.data)
            checkToken = checking_token_to_refresh(payload["userId"], token)
            if payload["type"] != "REFRESH_TOKEN" or not checkToken:
                return JsonResponse({"success": False, "error": str("Invalid token")}, status=400)
        except jwt.exceptions.InvalidTokenError:
            return JsonResponse(
                {"success": False, "error": str("Invalid token")},
                status=HTTP_401_UNAUTHORIZED,
            )
        except Exception:
            return JsonResponse(
                {"success": False, "error": str("Token expire time")},
                status=HTTP_401_UNAUTHORIZED,
            )

        return view_func(request, *args, **kwargs)

    return wrapper


def validate_password(password):
    if not password:
        return "Please provide a password"

    if len(password) < 8:
        return "Password must contain at least 8 characters"

    if not any(char.isupper() for char in password):
        return "Password must have at least ONE uppercase character"

    if not any(char.isdigit() for char in password):
        return "Password must have at least ONE number"

    if not re.search("[~`!@#\$%\^&\*\(\)_\+\{\[\}\]\|\\:;\"'<,>\.?/]", password):
        return "Password must have at least ONE special character"


def validate_email(email):
    if not re.match("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", email) or len(email) > 100:
        return "Please use a valid email address"


def validateImage(image):
    return True


def validate_passcode(passcode):

    if len(passcode) < 6:
        return "Password must contain at least 6 characters"
