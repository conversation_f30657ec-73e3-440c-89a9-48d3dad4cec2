# Generated by Django 5.0.8 on 2024-12-10 03:43

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="StatusAccountLog",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("user_name", models.CharField(max_length=256, null=True)),
                ("approved_by", models.CharField(max_length=256, null=True)),
                ("before_action", models.CharField(max_length=256, null=True)),
                ("after_action", models.CharField(max_length=256, null=True)),
                ("time", models.DateTimeField(default=django.utils.timezone.now)),
                ("note", models.CharField(max_length=1024, null=True)),
                ("is_create_key_action", models.BooleanField(default=False, null=True)),
            ],
            options={
                "db_table": "DRAFT_StatusAccountLog",
            },
        ),
        migrations.Add<PERSON>ield(
            model_name="users",
            name="company",
            field=models.Char<PERSON>ield(max_length=64, null=True),
        ),
        migrations.AddField(
            model_name="users",
            name="first_name",
            field=models.CharField(max_length=256, null=True),
        ),
        migrations.AddField(
            model_name="users",
            name="last_name",
            field=models.CharField(max_length=256, null=True),
        ),
        migrations.AddField(
            model_name="users",
            name="passcode",
            field=models.CharField(max_length=64, null=True),
        ),
        migrations.AddField(
            model_name="users",
            name="phone",
            field=models.CharField(blank=True, max_length=15, null=True),
        ),
        migrations.AddField(
            model_name="users",
            name="status_account",
            field=models.CharField(max_length=64, null=True),
        ),
        migrations.AlterModelTable(
            name="campaign",
            table="Draft_user_campaign_dba",
        ),
        migrations.AlterModelTable(
            name="clientparent",
            table="Draft_user_client_parent",
        ),
        migrations.AlterModelTable(
            name="levels",
            table="Draft_user_level",
        ),
        migrations.AlterModelTable(
            name="roles",
            table="Draft_user_role",
        ),
        migrations.AlterModelTable(
            name="users",
            table="Draft_user",
        ),
    ]
