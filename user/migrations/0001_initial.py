# Generated by Django 5.0.8 on 2024-08-19 04:33

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("data", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Campaign",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("campaign_name", models.CharField(max_length=256, null=True)),
                ("campaign_description", models.TextField(null=True)),
                (
                    "created_utc",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("updated_utc", models.DateTimeField(null=True)),
            ],
            options={
                "db_table": "user_campaign_dba",
            },
        ),
        migrations.CreateModel(
            name="GlobalConfig",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("config_key", models.Char<PERSON><PERSON>(max_length=128, null=True)),
                ("config_value", models.TextField(null=True)),
            ],
            options={
                "db_table": "global_config",
            },
        ),
        migrations.CreateModel(
            name="Levels",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("level_name", models.CharField(max_length=128, null=True)),
                ("level_description", models.TextField(null=True)),
            ],
            options={
                "db_table": "user_level",
            },
        ),
        migrations.CreateModel(
            name="Roles",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("role_name", models.CharField(max_length=128, null=True)),
                ("role_description", models.TextField(null=True)),
            ],
            options={
                "db_table": "user_role",
            },
        ),
        migrations.CreateModel(
            name="Users",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("user_name", models.CharField(max_length=256, null=True)),
                ("pwd_sha256", models.CharField(max_length=64, null=True)),
                ("email", models.CharField(max_length=256, null=True)),
                ("gender", models.CharField(max_length=64, null=True)),
                (
                    "created_utc",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("updated_utc", models.DateTimeField(null=True)),
                ("status_code", models.CharField(max_length=50, null=True)),
                ("last_login", models.DateTimeField(null=True)),
                ("sign_up_type", models.CharField(max_length=50, null=True)),
                ("sign_up_social_data", models.TextField(null=True)),
                ("user_social_id", models.TextField(null=True)),
                ("first_login", models.BooleanField(default=False)),
                ("api_key", models.TextField(null=True)),
                (
                    "level",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="user.levels"),
                ),
                (
                    "role",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="user.roles"),
                ),
            ],
            options={
                "db_table": "user",
            },
        ),
        migrations.CreateModel(
            name="ClientParent",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "campaign",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="user.campaign",
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="data.clientsinformation",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="user.users"),
                ),
            ],
            options={
                "db_table": "user_client_parent",
            },
        ),
    ]
