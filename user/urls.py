from django.urls import path

from user.services import userAPIServices, userServices

urlpatterns = [
    path("users/sign_in", userServices.signIn, name="sign_in"),
    path("users/get_token", userServices.getToken, name="get_token"),
    path("users/user_info", userServices.userInfo, name="user_info"),
    path("users/change_password", userServices.changePassword, name="change_password"),
    path("users/forgot_password", userServices.forgotPasswordUser, name="forgot_password"),
    path("users/update_profile", userServices.updateProfile, name="update_profile"),
    path("users/refresh_token_user", userServices.refreshTokenUser, name="refresh_token_user"),
    path("users/log_out", userServices.userLogout, name="log_out"),
    path("admin/user/<int:user_id>/", userServices.user, name="user"),
    path("admin/meta_data", userServices.getMetaData, name="get role"),
    path("admin/getInformationAPIUser", userAPIServices.getInformationAPIUser, name="getInformationAPIUser"),
    path("admin/userAPICreate", userAPIServices.userAPICreate, name="Create API User"),
    path("admin/checkStatusAccount", userAPIServices.CheckStatusAccountView.as_view(), name="checkStatusAccount"),
    path("admin/getAccountManagementTable", userAPIServices.getAccountManagementTable, name="get Account Management Table"),
    path("admin/getUserDeveloper/<int:user_id>", userAPIServices.getUserDeveloper, name="get User developer"),
    path("admin/deleteDeveloperZone/<int:id>", userAPIServices.DeleteDeveloperZoneView.as_view(), name="deleteDeveloperZone"),
    path("admin/developer-zone/change-level", userAPIServices.ChangeLevelDeveloperZone.as_view(), name="changeDeveloperZoneLevel"),
    path("admin/developer-zone/export-csv", userAPIServices.ExportCSVDeveloperZone.as_view(), name="ExportCSVDeveloperZone"),
    path("admin/developer-zone/change-password", userAPIServices.ChangePasswordDeveloperZone.as_view(), name="changePasswordDeveloperZone"),
    path("admin/developer-zone/update-company/<int:id>", userAPIServices.ChangeCompanyDeveloperView.as_view(), name="update name"),
    path("admin/developer-zone/update-all/<int:id>", userAPIServices.ChangeUserInformationView.as_view(), name="update-all"),
]
