# FLOFIN Database Structure Documentation

## Overview

This document provides detailed information about the database structure of the FLOFIN application, based on the models defined in `models.py`. The database is structured to support various features including sales flow management, order tracking, transaction reporting, and alert management.

## Core Models

### 1. Card Information

#### CardBinLookup

Stores information about payment card BIN (Bank Identification Number) details.

| Field                 | Type              | Description                            |
| --------------------- | ----------------- | -------------------------------------- |
| card_bin              | IntegerField (PK) | The 6-digit BIN number                 |
| card_issuer_name      | CharField         | Name of the card issuing institution   |
| prepaid_card          | IntegerField      | Flag indicating if the card is prepaid |
| issuer_country        | CharField         | Country where the card was issued      |
| card_brand            | CharField         | Card brand (Visa, Mastercard, etc.)    |
| card_type             | CharField         | Card type (Credit, Debit)              |
| card_subtype          | CharField         | Additional card type information       |
| created_at/updated_at | DateTimeField     | Creation/update timestamps             |

### 2. Sales Flow Management

#### OfferFlow

Defines the top-level container for a sales flow.

| Field                 | Type           | Description                            |
| --------------------- | -------------- | -------------------------------------- |
| flow_id               | AutoField (PK) | Unique flow identifier                 |
| flow_brand_name       | CharField      | Name of the flow                       |
| client                | ForeignKey     | Link to client information             |
| client_login          | ForeignKey     | Link to client login credentials       |
| flow_descriptor1-5    | CharField      | Additional flow descriptors            |
| status                | CharField      | Flow status (Active, Pending, Deleted) |
| created_at/updated_at | DateTimeField  | Creation/update timestamps             |

#### OfferFlowSteps

Defines individual steps within a sales flow.

| Field                    | Type           | Description                                          |
| ------------------------ | -------------- | ---------------------------------------------------- |
| offer_flow_step_id       | AutoField (PK) | Unique step identifier                               |
| flow                     | ForeignKey     | Link to parent flow                                  |
| flow_step_number         | IntegerField   | Position of step in the flow (1=initial, 2+=upsells) |
| continuity_enabled       | IntegerField   | Whether recurring billing is enabled                 |
| cont_cycle_frequency     | IntegerField   | Days between billing cycles                          |
| rebill_price             | DecimalField   | Price for rebill transactions                        |
| cont_cycle_1-6plus_price | DecimalField   | Prices for specific billing cycles                   |
| status                   | CharField      | Step status                                          |
| created_at/updated_at    | DateTimeField  | Creation/update timestamps                           |

#### OfferProductDetail

Stores detailed product information for each flow step and billing cycle.

| Field                 | Type           | Description                                       |
| --------------------- | -------------- | ------------------------------------------------- |
| id                    | AutoField (PK) | Unique product detail identifier                  |
| offer_flow_step       | ForeignKey     | Link to parent flow step                          |
| cycle_number          | IntegerField   | Billing cycle number (0=initial, 1+=rebills)      |
| step_price            | CharField      | Price for this product/cycle                      |
| product_match         | CharField      | Product identifier for matching with transactions |
| product_cost          | DecimalField   | Cost of the product                               |
| shipping_cost         | DecimalField   | Shipping cost                                     |
| status                | CharField      | Product status                                    |
| created_at/updated_at | DateTimeField  | Creation/update timestamps                        |

### 3. Order Management

#### OrderImport

Stores detailed information about imported orders/transactions.

| Field                 | Type           | Description                                    |
| --------------------- | -------------- | ---------------------------------------------- |
| id                    | AutoField (PK) | Unique order identifier                        |
| client_login          | ForeignKey     | Client login information                       |
| order_id              | CharField      | Order identifier from the source system        |
| sub_order_id          | CharField      | Sub-order identifier                           |
| transaction_date      | DateTimeField  | Date of the transaction                        |
| product_match         | CharField      | Product identifier matching OfferProductDetail |
| is_test               | IntegerField   | Flag for test transactions                     |
| price_point           | DecimalField   | Transaction amount                             |
| order_status          | IntegerField   | Status of the order (1=approved)               |
| vendor_id1-5          | CharField      | Traffic source identifiers                     |
| campaign              | CharField      | Campaign name                                  |
| is_scrubbed           | IntegerField   | Flag for scrubbed transactions                 |
| payment_network       | CharField      | Card network (Visa, MC, etc.)                  |
| customer_id           | CharField      | Customer identifier                            |
| cc_bin                | IntegerField   | First 6 digits of card number                  |
| card_last4            | IntegerField   | Last 4 digits of card number                   |
| merchantId            | CharField      | Merchant identifier                            |
| mid                   | CharField      | MID number                                     |
| is_3ds_verified       | IntegerField   | 3D Secure verification status                  |
| offer_product         | ForeignKey     | Link to OfferProductDetail                     |
| cycle_num_lookup      | IntegerField   | Billing cycle number                           |
| ancestor_id           | IntegerField   | ID of the initial transaction                  |
| ancestor_date         | DateTimeField  | Date of the initial transaction                |
| card_infor            | ForeignKey     | Link to CardBinLookup                          |
| created_at/updated_at | DateTimeField  | Creation/update timestamps                     |

#### OrderUpdate

Stores information about order updates (refunds, chargebacks, voids).

| Field                 | Type           | Description                                     |
| --------------------- | -------------- | ----------------------------------------------- |
| id                    | AutoField (PK) | Unique update identifier                        |
| client_login          | ForeignKey     | Client login information                        |
| order_id              | CharField      | Order identifier                                |
| sub_order_id          | CharField      | Sub-order identifier                            |
| update_date           | DateTimeField  | Date of the update                              |
| order_update_type     | IntegerField   | Type of update (1=refund, 2=void, 3=chargeback) |
| update_reason         | CharField      | Reason for the update                           |
| revenue_update        | DecimalField   | Amount of the update                            |
| product_match         | CharField      | Product identifier                              |
| update_status         | IntegerField   | Status of the update                            |
| order_import          | ForeignKey     | Link to the original OrderImport                |
| created_at/updated_at | DateTimeField  | Creation/update timestamps                      |

### 4. Reporting

#### HomeCLVReport

Stores aggregated customer lifetime value reporting data.

| Field                                          | Type           | Description                        |
| ---------------------------------------------- | -------------- | ---------------------------------- |
| id                                             | AutoField (PK) | Unique report identifier           |
| clientID                                       | CharField      | Client identifier                  |
| client                                         | CharField      | Client name                        |
| transaction_date                               | DateTimeField  | Transaction date                   |
| ancestor_date                                  | DateTimeField  | Initial sale date                  |
| count_order                                    | IntegerField   | Order count                        |
| cycle_num                                      | IntegerField   | Billing cycle number               |
| flow_brand                                     | ForeignKey     | Link to OfferFlow                  |
| flow_step                                      | ForeignKey     | Link to OfferFlowSteps             |
| network                                        | CharField      | Traffic source network             |
| pub                                            | CharField      | Publisher identifier               |
| merchantId                                     | CharField      | Merchant identifier                |
| post_tax_order_total                           | FloatField     | Order total amount                 |
| total_sales                                    | IntegerField   | Count of successful sales          |
| total_unique_sale                              | IntegerField   | Count of unique sales              |
| price_point                                    | FloatField     | Price point                        |
| card_brand                                     | CharField      | Card brand                         |
| card_type                                      | CharField      | Card type                          |
| card_issuer                                    | CharField      | Card issuer                        |
| total_foreign_card                             | IntegerField   | Count of foreign card transactions |
| total_3ds                                      | IntegerField   | Count of 3DS verified transactions |
| transactions                                   | IntegerField   | Total transaction count            |
| transactions_amount                            | FloatField     | Total transaction amount           |
| refunds/voids/chargebacks                      | IntegerField   | Count of each update type          |
| refunds_amount/voids_amount/chargebacks_amount | FloatField     | Amount of each update type         |
| created_at/updated_at                          | DateTimeField  | Creation/update timestamps         |

### 5. Alerts

#### AlertThreshold

Defines alert thresholds for monitoring key metrics.

| Field                 | Type           | Description                                  |
| --------------------- | -------------- | -------------------------------------------- |
| id                    | AutoField (PK) | Unique threshold identifier                  |
| name                  | CharField      | Name of the threshold                        |
| dimension             | CharField      | Entity to monitor (client, network, etc.)    |
| metric                | CharField      | Metric to monitor (chargeback_rate, etc.)    |
| value                 | FloatField     | Threshold value                              |
| current_value         | FloatField     | Current value of the metric                  |
| client                | ForeignKey     | Link to client information                   |
| dimension_value       | CharField      | Specific value for the dimension             |
| is_active             | BooleanField   | Whether the threshold is active              |
| trigger_frequency     | CharField      | Frequency for checking (daily, weekly, etc.) |
| condition             | CharField      | Comparison operator (<, >, <=, >=)           |
| lst_email             | JSONField      | List of notification emails                  |
| user                  | ForeignKey     | User who created the threshold               |
| created_at/updated_at | DateTimeField  | Creation/update timestamps                   |

## Relationships Diagram

```
CardBinLookup <----+
                    |
ClientLoginInformation <---+
                           |
ClientsInformation <---+   |
                       |   |
                       v   v
                     OfferFlow
                         |
                         v
                   OfferFlowSteps
                         |
                         v
                  OfferProductDetail <----+
                                          |
                                          |
CardBinLookup ----+                       |
                  |                       |
ClientLoginInformation ---+               |
                          |               |
                          v               |
                       OrderImport -------+
                          |
                          |
                          v
ClientLoginInformation ---+
                          |
                          v
                      OrderUpdate


OfferFlow -------+
                 |
OfferFlowSteps --+
                 |
                 v
             HomeCLVReport


ClientsInformation ---+
                      |
                      v
                 AlertThreshold
```

## Key Relationships

1. **OfferFlow -> OfferFlowSteps -> OfferProductDetail**

   - Hierarchical relationship defining the sales process
   - Flow contains multiple steps (initial offer, upsells)
   - Each step contains multiple product details for different billing cycles

2. **OrderImport -> OrderUpdate**

   - OrderUpdate records reference their original OrderImport
   - Tracks the relationship between initial orders and subsequent modifications

3. **OrderImport -> OfferProductDetail**

   - Links transactions to the product definition in the sales flow
   - Enables tracking which products and steps generated the transactions

4. **OrderImport -> CardBinLookup**

   - Links transactions to card BIN information
   - Provides additional data about card types and issuers

5. **HomeCLVReport -> OfferFlow/OfferFlowSteps**
   - Aggregated reporting data linked to flows and steps
   - Enables reporting across different dimensions of the sales funnel

## Notes on Data Flow

1. **Transaction Data Import**

   - Transaction data is imported from CRM systems into OrderImport
   - Update events (refunds, chargebacks) are stored in OrderUpdate

2. **Sales Flow Configuration**

   - Flows, steps, and products are configured through the OfferFlow hierarchy
   - These configurations determine how transactions are processed and tracked

3. **Reporting Aggregation**

   - The HomeCLVReport table stores pre-aggregated data
   - Generated by summarizing OrderImport and OrderUpdate data across various dimensions

4. **Alert Monitoring**
   - AlertThreshold configurations define monitoring rules
   - Regular checks compare current metric values against thresholds

## Database Table Names

All tables in this database have a "FloFin\_" prefix. The actual table names are:

- `FloFin_card_bin_lookup`
- `FloFin_offer_flow`
- `FloFin_offer_flow_steps`
- `FloFin_offer_product_detail`
- `FloFin_order_import`
- `FloFin_order_import_temp`
- `FloFin_order_update`
- `FloFin_Report_Home_CLV`
- `FloFin_alert_threshold`
