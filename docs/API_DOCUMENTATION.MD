# FLOFIN API Documentation

This document provides detailed information about the FLOFIN API endpoints, request/response formats, authentication, and common usage patterns.

## Authentication

FLOFIN uses JWT (JSON Web Token) authentication for API access.

### Obtaining a Token

```
POST /api/auth/token/
```

#### Request Body

```json
{
  "username": "your_username",
  "password": "your_password"
}
```

#### Response

```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1..."
}
```

### Using the Token

Include the access token in the Authorization header of all API requests:

```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1...
```

### Refreshing a Token

```
POST /api/auth/token/refresh/
```

#### Request Body

```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1..."
}
```

#### Response

```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1..."
}
```

## API Versioning

The API uses URL-based versioning. All endpoints are prefixed with `/api/v1/`.

## Common Response Formats

### Success Response

All successful responses follow a standard format:

```json
{
  "status": "success",
  "data": {
    // Response data here
  }
}
```

### Error Response

Error responses follow this format:

```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error details (optional)
    }
  }
}
```

## Pagination

List endpoints are paginated by default. Pagination information is included in the response:

```json
{
  "status": "success",
  "data": [...],
  "pagination": {
    "count": 100,
    "next": "http://api.example.org/api/v1/resource/?page=2",
    "previous": null,
    "page_size": 10,
    "current_page": 1,
    "total_pages": 10
  }
}
```

You can control pagination with the following query parameters:

- `page`: Page number (default: 1)
- `page_size`: Number of items per page (default: 10, max: 100)

## Rate Limiting

API requests are subject to rate limiting to ensure fair usage. Rate limit headers are included in all responses:

```
X-Rate-Limit-Limit: 100
X-Rate-Limit-Remaining: 95
X-Rate-Limit-Reset: 1589547362
```

## API Endpoints

### Clients

#### List Clients

```
GET /api/v1/clients/
```

Query Parameters:

- `status`: Filter by status (active, inactive)
- `created_after`: Filter by creation date (YYYY-MM-DD)
- `search`: Search by name or code

#### Get a Client

```
GET /api/v1/clients/{client_id}/
```

#### Create a Client

```
POST /api/v1/clients/
```

Request Body:

```json
{
  "name": "Client Name",
  "code": "CLIENT_CODE",
  "contact_email": "<EMAIL>",
  "contact_phone": "+1234567890",
  "status": "active"
}
```

#### Update a Client

```
PUT /api/v1/clients/{client_id}/
```

Request Body: (same as Create)

#### Partial Update a Client

```
PATCH /api/v1/clients/{client_id}/
```

Request Body: (partial fields from Create)

#### Delete a Client

```
DELETE /api/v1/clients/{client_id}/
```

### CRM Integrations

#### List CRM Integrations

```
GET /api/v1/clients/{client_id}/crm-integrations/
```

#### Get a CRM Integration

```
GET /api/v1/clients/{client_id}/crm-integrations/{integration_id}/
```

#### Create a CRM Integration

```
POST /api/v1/clients/{client_id}/crm-integrations/
```

Request Body:

```json
{
  "crm_type": "salesforce",
  "api_key": "api_key_here",
  "api_secret": "api_secret_here",
  "endpoint_url": "https://example.salesforce.com/api",
  "sync_frequency": "daily",
  "status": "active"
}
```

#### Update a CRM Integration

```
PUT /api/v1/clients/{client_id}/crm-integrations/{integration_id}/
```

#### Trigger CRM Data Import

```
POST /api/v1/clients/{client_id}/crm-integrations/{integration_id}/import/
```

### Products

#### List Products

```
GET /api/v1/clients/{client_id}/products/
```

Query Parameters:

- `status`: Filter by status (active, inactive)
- `category`: Filter by category
- `search`: Search by name or code

#### Get a Product

```
GET /api/v1/clients/{client_id}/products/{product_id}/
```

#### Create a Product

```
POST /api/v1/clients/{client_id}/products/
```

Request Body:

```json
{
  "name": "Product Name",
  "code": "PRODUCT_CODE",
  "description": "Product description",
  "category": "category_id",
  "price": 99.99,
  "currency": "USD",
  "tax_rate": 7.5,
  "status": "active"
}
```

### Customers

#### List Customers

```
GET /api/v1/clients/{client_id}/customers/
```

Query Parameters:

- `status`: Filter by status (active, inactive)
- `created_after`: Filter by creation date (YYYY-MM-DD)
- `search`: Search by name or email

#### Get a Customer

```
GET /api/v1/clients/{client_id}/customers/{customer_id}/
```

#### Create a Customer

```
POST /api/v1/clients/{client_id}/customers/
```

Request Body:

```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "address": {
    "street": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "zip": "12345",
    "country": "US"
  },
  "status": "active"
}
```

#### Get Customer Lifetime Value

```
GET /api/v1/clients/{client_id}/customers/{customer_id}/lifetime-value/
```

### Transactions

#### List Transactions

```
GET /api/v1/clients/{client_id}/transactions/
```

Query Parameters:

- `status`: Filter by status (completed, pending, failed)
- `transaction_type`: Filter by type (sale, refund, chargeback)
- `date_from`: Filter by date (YYYY-MM-DD)
- `date_to`: Filter by date (YYYY-MM-DD)
- `customer`: Filter by customer ID
- `product`: Filter by product ID

#### Get a Transaction

```
GET /api/v1/clients/{client_id}/transactions/{transaction_id}/
```

#### Create a Transaction

```
POST /api/v1/clients/{client_id}/transactions/
```

Request Body:

```json
{
  "customer_id": "customer_id",
  "transaction_type": "sale",
  "items": [
    {
      "product_id": "product_id",
      "quantity": 1,
      "unit_price": 99.99,
      "discount": 0
    }
  ],
  "payment_method": {
    "type": "credit_card",
    "last_four": "1234",
    "expiry_month": "12",
    "expiry_year": "2025"
  },
  "billing_address": {
    "street": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "zip": "12345",
    "country": "US"
  },
  "currency": "USD",
  "notes": "Order notes"
}
```

#### Process a Refund

```
POST /api/v1/clients/{client_id}/transactions/{transaction_id}/refund/
```

Request Body:

```json
{
  "amount": 99.99,
  "reason": "Customer request",
  "refund_all": false
}
```

#### Record a Chargeback

```
POST /api/v1/clients/{client_id}/transactions/{transaction_id}/chargeback/
```

Request Body:

```json
{
  "amount": 99.99,
  "reason": "Unauthorized transaction",
  "chargeback_id": "CB12345"
}
```

### Reports

#### List Available Reports

```
GET /api/v1/clients/{client_id}/reports/
```

#### Generate a Report

```
POST /api/v1/clients/{client_id}/reports/
```

Request Body:

```json
{
  "report_type": "sales_summary",
  "date_from": "2023-01-01",
  "date_to": "2023-12-31",
  "format": "csv",
  "filters": {
    "product_id": "product_id",
    "transaction_type": "sale"
  }
}
```

#### Get Report Status

```
GET /api/v1/clients/{client_id}/reports/{report_id}/
```

#### Download a Report

```
GET /api/v1/clients/{client_id}/reports/{report_id}/download/
```

### Monitoring

#### Get System Health

```
GET /api/v1/monitoring/health/
```

#### List Alerts

```
GET /api/v1/monitoring/alerts/
```

Query Parameters:

- `status`: Filter by status (active, resolved)
- `severity`: Filter by severity (critical, high, medium, low)
- `source`: Filter by source system

#### Get Alert Details

```
GET /api/v1/monitoring/alerts/{alert_id}/
```

#### Resolve an Alert

```
POST /api/v1/monitoring/alerts/{alert_id}/resolve/
```

Request Body:

```json
{
  "resolution_notes": "Fixed the issue by..."
}
```

## Webhooks

FLOFIN supports webhooks for real-time notifications of system events.

### List Registered Webhooks

```
GET /api/v1/clients/{client_id}/webhooks/
```

### Register a Webhook

```
POST /api/v1/clients/{client_id}/webhooks/
```

Request Body:

```json
{
  "event_type": "transaction.created",
  "target_url": "https://your-system.com/webhook-endpoint",
  "secret": "your_webhook_secret",
  "description": "Transaction notification webhook"
}
```

### Webhook Event Types

- `transaction.created`: Triggered when a new transaction is created
- `transaction.updated`: Triggered when a transaction is updated
- `transaction.refunded`: Triggered when a refund is processed
- `transaction.chargeback`: Triggered when a chargeback is recorded
- `customer.created`: Triggered when a new customer is created
- `customer.updated`: Triggered when customer information is updated
- `alert.created`: Triggered when a new alert is generated

### Webhook Payload Format

```json
{
  "event_type": "transaction.created",
  "timestamp": "2023-06-15T14:30:00Z",
  "client_id": "client_id",
  "data": {
    // Event-specific data
  }
}
```

## API Status Codes

The API uses standard HTTP status codes:

- `200 OK`: Request succeeded
- `201 Created`: Resource created successfully
- `204 No Content`: Request succeeded, no content to return
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Permission denied
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict
- `422 Unprocessable Entity`: Validation error
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## API Error Codes

Common error codes:

- `AUTHENTICATION_FAILED`: Invalid credentials
- `TOKEN_EXPIRED`: Authentication token expired
- `PERMISSION_DENIED`: Insufficient permissions
- `RESOURCE_NOT_FOUND`: Requested resource not found
- `VALIDATION_ERROR`: Request data validation failed
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_ERROR`: Internal server error

## Tools and Libraries

### Official SDKs

- [Python SDK](https://github.com/flofin/python-sdk)
- [JavaScript SDK](https://github.com/flofin/js-sdk)

### API Client Examples

#### cURL

```bash
# Get authentication token
curl -X POST \
  https://api.flofin.com/api/auth/token/ \
  -H 'Content-Type: application/json' \
  -d '{"username":"your_username","password":"your_password"}'

# Get client list
curl -X GET \
  https://api.flofin.com/api/v1/clients/ \
  -H 'Authorization: Bearer your_access_token'
```

#### Python

```python
import requests

# Get authentication token
auth_response = requests.post(
    'https://api.flofin.com/api/auth/token/',
    json={'username': 'your_username', 'password': 'your_password'}
)
token = auth_response.json()['access']

# Get client list
headers = {'Authorization': f'Bearer {token}'}
clients_response = requests.get(
    'https://api.flofin.com/api/v1/clients/',
    headers=headers
)
clients = clients_response.json()['data']
```

#### JavaScript

```javascript
// Get authentication token
fetch("https://api.flofin.com/api/auth/token/", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    username: "your_username",
    password: "your_password",
  }),
})
  .then((response) => response.json())
  .then((data) => {
    const token = data.access;

    // Get client list
    return fetch("https://api.flofin.com/api/v1/clients/", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  })
  .then((response) => response.json())
  .then((data) => {
    const clients = data.data;
    console.log(clients);
  });
```

## Best Practices

1. **Rate Limiting**: Implement exponential backoff when hitting rate limits
2. **Caching**: Cache responses when appropriate to reduce API calls
3. **Validation**: Validate input data before making API calls
4. **Error Handling**: Implement proper error handling for all API responses
5. **Authentication**: Securely store and manage authentication tokens
6. **Webhooks**: Set up proper error handling for webhook processing
7. **Testing**: Test your integration thoroughly before going to production
