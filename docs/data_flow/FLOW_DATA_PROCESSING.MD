# Flow Data Processing Documentation

## Overview

After retrieving transaction data from CRM systems (Konnektive and Sticky), the system processes this data to establish relationships, enrich records with additional information, and prepare it for analysis. This document explains the flow data processing steps implemented in `Flow_v2.py`.

## Processing Steps

The data processing flow consists of two main steps:

### Step 1: Basic Order Enrichment

**Implemented by**: `UpdateOrderStep1` class

This step handles fundamental enrichment of order data:

1. **Card Information Update**:

   - Links `OrderImport` records to corresponding `CardBinLookup` data
   - Identifies and stores card issuer information based on BIN numbers
   - Updates records in batches for efficiency

2. **Order Update Association**:
   - Links `OrderUpdate` records (refunds, chargebacks, etc.) to their original `OrderImport` records
   - Creates relationships between initial orders and subsequent modifications
   - Focuses on successful orders (status=1) when creating these relationships

### Step 2: Customer Journey Analysis

**Implemented by**: `UpdateOrderStep2` class

This step analyzes the customer journey through product flows:

1. **Product Flow Mapping**:

   - Works with product offers defined in `OfferProductDetail` and `OfferFlow` models
   - Identifies initial and subsequent product purchases within flows
   - Tracks customer progression through predefined sales funnels

2. **Billing Cycle Tracking**:

   - Analyzes customer purchase history to identify billing cycles
   - Orders customer transactions chronologically
   - Assigns cycle numbers to establish purchase sequence
   - Links follow-up orders to ancestor (initial) orders
   - Stores ancestor information (ID, date, vendor IDs)

3. **Special Case Handling**:
   - Contains specific logic for certain product matches like "KetoBod Detox" and "SugarShield Detox"
   - Applies different cycle numbering rules based on product type

## Key Relationships

The processing creates and maintains several important relationships:

- Orders → Card information
- Order updates → Original orders
- Orders → Offer products
- Orders → Billing cycles
- Orders → Ancestor orders

## Data Models Used

- **OrderImport**: Stores transaction data from CRMs
- **OrderUpdate**: Stores order modifications (refunds, chargebacks)
- **CardBinLookup**: Contains card issuer information based on BIN numbers
- **OfferFlow**: Defines product flow sequences
- **OfferProductDetail**: Contains detailed product information within flows

## Performance Considerations

1. **Batch Processing**:

   - Uses `bulk_update` for efficient database operations
   - Processes data in chunks to optimize memory usage
   - Default chunk size: 10,000 records

2. **Parallel Processing**:

   - Uses `concurrent.futures.ThreadPoolExecutor` for parallel operations
   - Handles multiple customer records simultaneously
   - Configurable thread pool size (default: 50 workers)

3. **Progress Tracking**:
   - Uses `tqdm` for progress visualization
   - Provides real-time feedback during long-running operations

## Usage

### Basic Usage

```python
# Initialize Step 1 processing
step1 = UpdateOrderStep1(
    start_date="2024-01-01",
    end_date="2024-01-31",
    client_login=client_login_obj
)
step1.run()  # Runs all Step 1 processing

# Initialize Step 2 processing
step2 = UpdateOrderStep2(
    start_date="2024-01-01",
    end_date="2024-01-31",
    client_login=client_login_obj,
    lst_flow=specific_flows  # Optional: specify flows to process
)
step2.run()  # Runs all Step 2 processing
```

### Selective Processing

You can run specific parts of the process:

```python
step1 = UpdateOrderStep1(...)
step1.updateCard()  # Only update card information
step1.updateOrderUpdate()  # Only update order relationships

step2 = UpdateOrderStep2(...)
step2.updateCycleNumLookup()  # Only update cycle numbering
```

## Process Flow Diagram

```
CRM Data Import → UpdateOrderStep1 → UpdateOrderStep2
    ↓                    ↓                  ↓
OrderImport      Card Info & Order     Customer Journey
OrderUpdate      Relationships         & Billing Cycles
```

## Best Practices

1. **Date Range Considerations**:

   - Process data in manageable date ranges
   - For large historical datasets, use smaller date windows

2. **Client Filtering**:

   - Always specify client_login when processing client-specific data
   - This improves performance and ensures data integrity

3. **Flow Configuration**:

   - Ensure offer flows are properly configured before processing
   - Verify product matches exist in the OfferProductDetail table

4. **Error Handling**:
   - Monitor processing logs for exceptions
   - Validate results after processing
   - Re-run specific segments if issues are detected

## Troubleshooting

1. **Missing Card Information**:

   - Verify CardBinLookup table contains entries for the relevant BINs
   - Check cc_bin field in OrderImport records

2. **Order Update Linking Failures**:

   - Ensure order_id and product_match fields match between OrderImport and OrderUpdate
   - Verify OrderImport records have order_status=1

3. **Cycle Numbering Issues**:
   - Check that OfferFlow and OfferProductDetail are properly configured
   - Verify product_match values in OrderImport match those in OfferProductDetail
