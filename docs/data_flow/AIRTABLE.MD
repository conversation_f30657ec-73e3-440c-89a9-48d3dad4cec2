# Airtable Integration Documentation

## Overview

Airtable serves as our central database for client information, CRM credentials, and merchant data. This document outlines the integration process, the data we sync, and how it's stored in our local database.

## What is Airtable?

Airtable is a cloud-based database platform that combines the functionality of a spreadsheet with the power of a database. In our system, it acts as the single source of truth for client, CRM, and merchant information.

## Configuration

The integration requires the following environment variables:

- `AIRTABLE_PERSONAL_ACCESS_TOKEN`: Authentication token for Airtable API access
- `AIRTABLE_DATABASE_ID`: The ID of the Airtable base containing our data

## Data Synced from Airtable

### 1. Client Information

**Airtable Table**: `Clients`
**View Used**: `RCVR_dev_view`
**Local Model**: `ClientsInformation`

**Fields Synced**:

- Client ID
- Client Name
- Account Status
- Chargeback Service Level
- Creation Time
- Employee Emails
- Service Level
- Internal Team
- Verifi CID

### 2. CRM Login Information

**Airtable Table**: `Client CRM Logins`
**View Used**: `RCVR_dev_view`
**Local Model**: `ClientLoginInformation`

**Fields Synced**:

- Login ID
- Login Type
- Login Platform
- CRM Number
- URL
- Validation Status
- Username
- Password
- API Key
- Main Client Used
- Client ID Midigator
- List of Clients Used
- Client ID Verifi Direct

### 3. Merchant Information

**Airtable Table**: `RCVR Master`
**View Used**: `RCVR_dev_view`
**Local Model**: `MerchantsInformation`

**Fields Synced**:

- Merchant Record ID
- MID (Merchant ID)
- Alias
- Status
- CRM IDs (1-4)
- Client
- Client ID
- Corporation
- Processor
- Created Date
- GWID
- Descriptor Information
- CAID
- DBA
- MCC
- ARN
- Processor Portal Details
- API Keys
- Products
- Enrollment Dates
- Closed Date
- Alert Status (RDR, Ethoca)

## Database Models

### ClientsInformation

Stores core client data including identification, service levels, and contact information.

### ClientLoginInformation

Stores all CRM and API login credentials associated with clients. This model is critical for accessing the various CRM systems.

### MerchantsInformation

Stores detailed merchant data including identifiers, descriptors, processor information, and status.

## Synchronization Process

The integration follows a defined process to sync data from Airtable to our local database:

1. **Update Internal Login IDs**: Ensures consistent internal login IDs across duplicate CRM credentials
2. **Get Client Data**: Syncs client information and marks removed clients
3. **Get Client Login Data**: Syncs CRM and API login credentials and marks removed accounts
4. **Get Merchant Data**: Syncs merchant information with extensive field mapping
5. **Get Alert Status Data**: Updates merchant alert status for RDR and Ethoca

## Key Functions

### `updateInternalLoginID()`

Ensures consistent internal login IDs across duplicate CRM credentials by updating records with missing internal login IDs.

### `getClientsData()`

Fetches client information from Airtable and updates the local database. Marks removed clients with a "Removed" status.

### `getClientsLoginData()`

Fetches CRM and API login credentials from Airtable and updates the local database. Marks removed accounts as invalid.

### `getMerchantsData()`

Fetches detailed merchant information from Airtable and updates the local database.

### `getAlertStausData()`

Updates merchant alert status for RDR and Ethoca from Airtable.

### `updateAirTable()`

Main function that orchestrates the entire sync process, running all the above functions in sequence.

## Automated Updates

The synchronization process is typically scheduled to run at regular intervals to ensure our local database stays updated with the latest information from Airtable.

## Best Practices

1. Maintain the Airtable base structure to ensure smooth data synchronization
2. Keep the environment variables secure
3. Monitor the synchronization logs for any errors
4. Update the Airtable with accurate information as it serves as the source of truth
5. Run the sync process during off-peak hours to minimize impact on system performance

## Troubleshooting

1. Check environment variables if the sync process fails
2. Verify Airtable API access and permissions
3. Inspect the sync logs for specific error messages
4. Verify that the expected tables and views exist in Airtable
5. Ensure the Airtable schema matches the expected structure
