# Report Calculation Documentation

## Overview

After retrieving transaction data from CRMs and processing it through the data enrichment steps, the system generates aggregated reports for analysis. This document explains the report calculation process implemented in `SummaryTable.py`.

## Report Generation Process

The report generation is the final step in the data processing pipeline:

```
CRM Data Import → Data Enrichment → Report Calculation
```

### HomeCLVReport Generation

**Implemented by**: `HomeCLVReportRun` class in `SummaryTable.py`

This class is responsible for aggregating transaction data and calculating key metrics for client reporting and analysis. It transforms the enriched transaction data into a format suitable for business intelligence and performance tracking.

## Data Sources for Reports

The report calculation uses two primary data sources:

1. **OrderImport Data**:

   - Successful transactions
   - Initial sales
   - Revenue metrics
   - Customer acquisition data

2. **OrderUpdate Data**:
   - Refunds
   - Voids
   - Chargebacks
   - Revenue adjustments

## Key Metrics Calculated

### From OrderImport

- Transaction counts
- Revenue totals
- Unique sales
- 3DS verification rates
- Foreign card usage
- Sales by card type/brand/issuer
- Sales by merchant ID
- Sales by network/publisher

### From OrderUpdate

- Refund counts and amounts
- Void counts and amounts
- Chargeback counts and amounts
- Adjustment metrics by card type/brand/issuer
- Adjustment metrics by network/publisher

## Calculation Process

### Step 1: Order Import Data Aggregation

**Implemented by**: `makeOrderImportData()` method

1. **Data Filtering**:

   - Filters data by date range
   - Optionally filters by client login and flow

2. **Data Enrichment**:

   - Categorizes card issuers (major banks vs. "Longtail")
   - Flags foreign cards
   - Standardizes card brands and types
   - Groups data by transaction date, ancestor date, network, etc.

3. **Metric Calculation**:
   - Counts total orders
   - Sums transaction amounts
   - Counts successful sales
   - Identifies unique sales
   - Tallies 3DS verified transactions
   - Counts foreign card transactions

### Step 2: Order Update Data Aggregation

**Implemented by**: `makeOrderUpdateData()` method

1. **Data Filtering**:

   - Focuses on updates to successful, non-test orders
   - Filters by date range
   - Optionally filters by client login and flow

2. **Data Enrichment**:

   - Links to original order data
   - Categorizes by card information
   - Groups by transaction date, ancestor date, etc.

3. **Metric Calculation**:
   - Counts refunds, voids, and chargebacks
   - Calculates refund, void, and chargeback amounts
   - Segments by card type, brand, and issuer

### Step 3: Database Update

The calculated metrics are stored in the `HomeCLVReport` model using either:

- `update_or_create` for incremental updates
- `bulk_create` for complete rebuilds (when `is_reset_db=True`)

## Key Dimensions for Analysis

Reports are generated with these key dimensions, enabling multi-faceted analysis:

1. **Temporal Dimensions**:

   - Transaction date
   - Ancestor date (original purchase date)

2. **Client Dimensions**:

   - Client ID
   - Client name

3. **Traffic Source Dimensions**:

   - Network
   - Publisher

4. **Payment Dimensions**:

   - Card brand (Visa, Mastercard, etc.)
   - Card type (Credit, Debit)
   - Card issuer (major banks vs. longtail)
   - Foreign vs. domestic cards
   - 3DS verification status

5. **Product Dimensions**:

   - Flow brand
   - Flow step
   - Cycle number
   - Price point

6. **Merchant Dimensions**:
   - Merchant ID

## Important Configuration Options

The `HomeCLVReportRun` class accepts several parameters:

- **start_date/end_date**: Define the date range for report calculation
- **client_login**: Filter data by specific client
- **chunk_size**: Control batch size for database operations (default: 10,000)
- **is_reset_db**: When true, completely rebuilds reports instead of updating
- **lst_flow**: Optional list of specific flows to include in reports

## Card Issuer Categorization

The system categorizes card issuers into major banks vs. "Longtail":

Major issuers include:

- CITIBANK N.A.
- BANK OF AMERICA, N.A.
- CHASE BANK USA, N.A.
- BANK OF AMERICA, NATIONAL ASSOCIATION
- FIFTH THIRD BANK, THE
- CAPITAL ONE BANK (USA), NATIONAL ASSOCIATION

All other issuers are categorized as "Longtail".

## Usage

### Basic Usage

```python
from flofin.source.utils import run_data_flow

# Run the entire data flow including report calculation
run_data_flow(
    client_login=client_login_obj,
    start_date="2024-01-01",
    end_date="2024-01-31"
)
```

### Running Only Report Calculation

```python
from flofin.source.SummaryTable import HomeCLVReportRun

# Run just the report calculation
report_runner = HomeCLVReportRun(
    client_login=client_login_obj,
    start_date="2024-01-01",
    end_date="2024-01-31"
)
report_runner.run()
```

### Rebuilding All Reports

```python
from flofin.source.SummaryTable import HomeCLVReportRun

# Reset and rebuild all reports
report_runner = HomeCLVReportRun(
    client_login=client_login_obj,
    start_date="2024-01-01",
    end_date="2024-01-31",
    is_reset_db=True
)
report_runner.run()
```

## Performance Considerations

1. **Transaction Atomicity**:

   - All report calculations run within a database transaction
   - Ensures data consistency even if process is interrupted

2. **Batch Processing**:

   - Uses bulk operations for efficient database updates
   - Processes data in configurable chunks

3. **Progress Tracking**:
   - Uses `tqdm` to provide visual progress indicators
   - Helpful for long-running report calculations

## Best Practices

1. **Date Range Management**:

   - For large datasets, process smaller date ranges
   - Consider incremental updates for recent data

2. **Database Reset Caution**:

   - Use `is_reset_db=True` with care as it deletes existing reports
   - Typically only needed for initial setup or data corrections

3. **Monitoring**:

   - Watch for MultipleObjectsReturned exceptions in logs
   - These indicate duplicate keys which may require data cleanup

4. **Flow Implementation**:
   - Always update OfferFlow status to "Active" after report generation
   - This signals that data is ready for analysis

## End-to-End Workflow

The complete data processing workflow, implemented in `utils.py`, consists of:

1. **Step 1: Basic Data Enrichment**

   - Card information association
   - Order update linkage

2. **Step 2: Customer Journey Analysis**

   - Product flow tracking
   - Billing cycle analysis

3. **Report Generation**
   - Aggregation and calculation
   - Report storage in HomeCLVReport

After the entire workflow completes, all processed flows are marked as "Active" to indicate they are ready for analysis.
