# FLOFIN Developer Workflow

This document provides a comprehensive guide to the development workflow for the FLOFIN project, helping new team members get up to speed quickly.

## Development Environment Setup

### Prerequisites

- Python 3.9+
- Docker and Docker Compose
- Git
- A code editor (VS Code recommended)

### Initial Setup

1. **Clone the repository**

```bash
<NAME_EMAIL>:flofin/flofin-be.git
cd flofin-be
```

2. **Set up virtual environment**

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies
```

3. **Configure environment variables**

```bash
cp .env.example .env.development
# Edit .env.development with your local settings
```

4. **Start the database with Docker**

```bash
docker-compose up -d postgres
```

5. **Run migrations**

```bash
python manage.py migrate
```

6. **Load initial data (optional)**

```bash
python manage.py loaddata initial_data
```

7. **Create a superuser**

```bash
python manage.py createsuperuser
```

### Running the Application

```bash
# Start the application server
python manage.py runserver

# Start background workers
celery -A core worker -l info

# Start scheduled tasks
celery -A core beat -l info
```

## Development Workflow

### Git Workflow

We follow a feature branch workflow:

1. **Create a feature branch**

```bash
git checkout -b feature/your-feature-name
```

2. **Make changes and commit regularly**

```bash
git add .
git commit -m "Meaningful commit message"
```

3. **Keep your branch up to date with main**

```bash
git checkout main
git pull
git checkout feature/your-feature-name
git rebase main
```

4. **Push your branch and create a Pull Request**

```bash
git push -u origin feature/your-feature-name
```

5. **After review and approval, merge to main**

### Coding Standards

- Follow PEP 8 for Python code style
- Use type hints for function parameters and return values
- Write docstrings for all public functions and classes
- Keep functions small and focused on a single responsibility
- Follow the existing project patterns and conventions

### Code Quality Tools

- **Linting**: We use flake8 and pylint

  ```bash
  flake8 .
  pylint app/ tests/
  ```

- **Type checking**: We use mypy

  ```bash
  mypy .
  ```

- **Code formatting**: We use black

  ```bash
  black .
  ```

- **Import sorting**: We use isort

  ```bash
  isort .
  ```

- **Pre-commit hooks**: Install hooks to automate checks
  ```bash
  pre-commit install
  ```

## Testing

### Running Tests

```bash
# Run all tests
pytest

# Run specific tests
pytest tests/unit/
pytest tests/integration/
pytest tests/functional/

# Run with coverage
pytest --cov=app
```

### Writing Tests

- Place tests in the appropriate directory:
  - `tests/unit/` for unit tests
  - `tests/integration/` for integration tests
  - `tests/functional/` for functional tests
- Use fixtures from `tests/utils/fixtures.py`
- Use test factories from `tests/utils/factories.py`
- Mock external dependencies using `tests/utils/mocks.py`

### Test Guidelines

- Unit tests should be fast and not rely on external services
- Each test should focus on a single aspect of functionality
- Use descriptive test names that explain what is being tested
- Follow the Arrange-Act-Assert pattern

## Database Operations

### Running Migrations

```bash
# Create a new migration
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Revert a migration
python manage.py migrate app_name migration_name
```

### Working with the Database

- Always use models and repositories instead of raw SQL when possible
- Add appropriate indexing for frequently accessed fields
- Write migrations for any data structure changes
- Test migrations on a copy of production data before deploying

## API Development

### Adding a New Endpoint

1. Define the request/response schema in `api/schemas/`
2. Implement input validation in `api/validators/`
3. Implement the view function in `api/views/`
4. Add the route to `api/routers/`
5. Update API documentation
6. Write tests for the new endpoint

### API Documentation

- Keep the API documentation in `docs/API_DOCUMENTATION.MD` up to date
- Include request and response examples
- Document error responses

## Working with CRM Integrations

### Adding a New CRM Connector

1. Create a new connector class in `crm/connectors/`
2. Implement data transformation in `crm/transformers/`
3. Add validation rules in `crm/validators/`
4. Update the importer service in `crm/importers/`
5. Write tests for the new connector

### Testing CRM Integration

- Use mock responses for unit tests
- Create integration tests that verify the end-to-end flow
- Test edge cases and error handling

## Debugging

### Logging

- Use the logging module for consistent logging
- Add context to log messages
- Use appropriate log levels

```python
import logging
logger = logging.getLogger(__name__)

logger.debug("Detailed information for debugging")
logger.info("Confirmation of normal events")
logger.warning("Warning about potential issues")
logger.error("Error that prevents a specific operation")
logger.critical("Critical error that requires immediate attention")
```

### Debugging Tools

- Use the Django Debug Toolbar in development
- Set breakpoints with `import pdb; pdb.set_trace()`
- Use the `--pdb` flag with pytest to debug tests

## Deployment

### Staging Deployment

- Staging deployments happen automatically when a PR is merged to the `staging` branch
- Verify changes in the staging environment before deploying to production

### Production Deployment

- Production deployments happen after approval from the tech lead
- Verify deployments with post-deployment checks

## Monitoring and Alerting

- Use the monitoring dashboard to check application health
- Configure alerts for critical system metrics
- Monitor error logs and performance metrics

## Getting Help

- Check the existing documentation in the `docs/` directory
- Ask questions in the #flofin-dev Slack channel
- Pair program with an experienced team member
- Schedule design reviews for complex changes

## Common Workflows

### Transaction Processing

1. Understand the flow in `transactions/services.py`
2. Follow the transaction lifecycle from creation to completion
3. Review the payment gateway integration in `transactions/gateways/`

### CRM Data Import

1. Review the import process in `crm/services.py`
2. Understand how data is transformed and validated
3. Check the background job processing in `tasks/workers/crm_import.py`

### Reporting

1. Study the report generation in `reports/services.py`
2. Review how data is analyzed and formatted
3. Understand the caching strategy for reports

## Continuous Integration

- CI runs automatically on all pull requests
- Tests must pass before a PR can be merged
- Code quality checks must pass
- Coverage should not decrease

## Contributing to Documentation

- Keep this guide updated as processes change
- Add documentation for new features
- Improve existing documentation based on feedback
