# Utility Functions API Documentation

## Overview

The Utility Functions API provides supporting endpoints that handle common operations across the application, primarily focused on client data retrieval and filter metadata. These endpoints are used by multiple application areas to retrieve consistent data about clients, available filter options, and other shared functionality.

## Key Concepts

- **Client View**: A filtered list of clients available to the current user
- **Filter Metadata**: Data needed to populate filter UI components
- **Permission-Based Access**: Client data access based on user permissions
- **User Context**: Operations that take into account the current user's roles and permissions

## Endpoints

### 1. Get Client View

```
getClientView(request)
```

This is a utility function rather than an exposed API endpoint. It retrieves a list of clients available to the current user based on their permissions.

#### Parameters

- `request`: The HTTP request object containing user information

#### Response

Returns a tuple of:

- List of client objects with clientID, name, and status
- User permissions string

### 2. Get CLV Filter Metadata for Reporting

```
GET /reporting/meta-data
```

Retrieves metadata for populating filter UI components in the reporting section.

#### Parameters

- `show_by_date`, `show_by_network`, `show_by_pub`: Display options
- `showing_filters[]`: Which filters to show
- `lst_flow_brand_name[]`: Flow brand names to filter by
- `clientID[]`: Client IDs to filter by

#### Response

Returns data for populating filter dropdowns and other UI components:

- Flow brand names
- Card brands and types
- Card issuers
- Price points
- Cycle numbers
- Flow step numbers
- Date ranges

### 3. Get CLV Filter Metadata for Fraud

```
GET /fraud/meta-data
```

Retrieves metadata for populating filter UI components in the fraud reporting section.

#### Parameters

- Same as "Get CLV Filter Metadata for Reporting" endpoint

#### Response

Returns data for populating filter dropdowns and other UI components, specifically tailored for fraud analysis:

- Flow brand names
- Card brands and types
- Price points
- Flow step numbers
- Date ranges
- (Note: Card issuer filters are excluded for fraud analysis)

### 4. Get CLV Filter Metadata for Alert Threshold

```
GET /alert-threshold/meta-data
```

Retrieves metadata for populating filter UI components in the alert threshold management section.

#### Parameters

- `showing_filters[]`: Which filters to show
- `clientID[]`: Client IDs to filter by

#### Response

Returns data for populating filter dropdowns specifically for alert threshold configuration:

- Flow brand names (including "All" option)
- Card brands (including "All" option)
- Card types (including "All" option)
- Networks (including "All" option)
- Publishers/affiliates (including "All" option)
- Merchant IDs (including "All" option)

## Implementation Details

### Client Filtering

The client view function implements two types of filtering:

1. **Super-Parent filtering**: Users with SUPER-PARENT permission can only see clients they are explicitly associated with
2. **Standard filtering**: Other users see all active clients (Active, Onboarding, Pre-ONB, Integration status)

### Filter Metadata Generation

The filter metadata endpoints share a common pattern:

1. Retrieve client view based on user permissions
2. Apply any client-specific or flow-specific filters
3. Query the `HomeCLVReport` model to find available values for each filter dimension
4. Format the data for UI consumption with label/value pairs

### Date Range Handling

The filter metadata endpoints provide smart date range defaults:

- Default to the last 90 days if no date range is specified
- Allow both explicit date range parameters and automatic defaults
- Provide proper date formatting for UI components

### Permission-Based Access Control

The utility functions implement permission-based access control:

- All endpoints are protected with the `@require_login` decorator
- Super-parent users have restricted client access
- Regular users can access all active clients
- Permissions are tied to the user from the request context

## Usage Examples

### Getting Available Clients

This is typically used internally by other API endpoints to filter data based on client permissions:

```python
clients, permissions = getClientView(request)
if "SUPER-PARENT" in permissions.split("_"):
    # Restrict to only the first client for super-parent users
    clientID = clients[0]["clientID"]
```

### Retrieving Filter Options for Reporting

```
GET /reporting/meta-data?show_by_date=1&show_by_network=1&show_by_pub=0&showing_filters[]=flow_brand_name&showing_filters[]=card_brand
```

This request retrieves filter metadata for the reporting section, with flow brand name and card brand filters shown.

### Retrieving Filter Options for Alert Thresholds

```
GET /alert-threshold/meta-data?clientID[]=client123
```

This request retrieves filter metadata for alert threshold configuration for a specific client.
