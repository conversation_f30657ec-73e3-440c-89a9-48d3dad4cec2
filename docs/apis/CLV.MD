# Customer Lifetime Value (CLV) API Documentation

## Overview

The Customer Lifetime Value (CLV) API provides endpoints for analyzing and reporting on customer value metrics over time. These endpoints help track revenue, retention, and profitability across different dimensions such as time periods, networks, publishers, and products.

## Key Concepts

- **Customer Lifetime Value (CLV)**: The total value a customer brings over the entire relationship with your business
- **Cash Flow Analysis**: Analysis of revenue streams over time, including initial sales, rebills, refunds, and chargebacks
- **Metrics Calculation**: Methods to calculate key performance indicators like revenue, transactions, retention rates, etc.

## Endpoints

### 1. Get CLV Data (Overall)

```
GET /reporting/customer-lifetime-value/overall
```

Retrieves aggregated CLV metrics across selected dimensions.

#### Parameters

- `clientID[]`: Filter by specific client IDs
- `flow_brand_name[]`: Filter by flow brand names
- `card_brand[]`: Filter by card brands
- `network[]`: Filter by networks
- `pub[]`: Filter by publishers
- `original_sale_start_date`, `original_sale_end_date`: Filter by initial sale date range
- `as_of_date_start_date`, `as_of_date_end_date`: Filter by "as of" date range
- `sales_start_number`, `sales_end_number`: Filter by sales volume
- `custom_fees`: Apply custom fees to calculations
- `show_by_date`, `show_by_network`, `show_by_pub`: Control grouping dimensions
- `page`, `limit`: Pagination controls
- `sortByVal`, `orderByVal`: Sorting controls

#### Response

Returns aggregated CLV metrics, including:

- Revenue metrics (initial sales, rebills, refunds, chargebacks)
- Transaction counts
- Average values
- Retention rates
- Profit calculations

### 2. Get Cash Flow Data

```
GET /reporting/customer-lifetime-value/getCashFlow
```

Provides cash flow analysis based on CLV data.

#### Parameters

Same as "Get CLV Data" endpoint.

#### Response

Returns cash flow metrics across time periods, including:

- Revenue inflows and outflows
- Cumulative values
- Period-specific metrics
- Segmentation by selected dimensions

### 3. Get CLV Overview (Tool Tips)

```
GET /reporting/customer-lifetime-value/overview
```

Retrieves summary metrics for specific flow brand and time period combinations.

#### Parameters

- Standard CLV filtering parameters, plus:
- `flow_brand_name`: Specific flow brand to analyze
- `month_of_flow_brand`: Month to analyze for the selected flow
- `date_choice`, `network_choice`, `pub_choice`: Additional display options

#### Response

Returns overview metrics for the specified flow and time period, including:

- Summary statistics
- Key performance indicators
- Comparison metrics

### 4. Get CLV Filter Metadata

```
GET /reporting/customer-lifetime-value/meta-data
```

Provides metadata for populating filter UI components.

#### Parameters

- `show_by_date`, `show_by_network`, `show_by_pub`: Display options
- `showing_filters[]`: Which filters to show
- `lst_flow_brand_name[]`: Flow brand names to filter by
- `clientID[]`: Client IDs to filter by

#### Response

Returns data for populating filter dropdowns and other UI components:

- Available flow brand names
- Card brands and types
- Networks and publishers
- Date ranges
- Available metrics

### 5. Download CLV Data

```
GET /reporting/customer-lifetime-value/downloadCLV
```

Downloads CLV data in CSV format.

#### Parameters

Same as "Get CLV Data" endpoint.

#### Response

Returns a CSV file containing the CLV data based on the specified filters and parameters.

## Implementation Details

### CLV Metrics Calculation

The `calculate_clv_show_metrics` function handles the calculation of CLV metrics:

- Groups data by specified dimensions (date, network, publisher)
- Calculates revenue metrics (initial sales, rebills, refunds, chargebacks)
- Computes rates (retention, refund, chargeback)
- Applies custom fees for profitability calculations
- Normalizes metrics for comparison

### Cash Flow Metrics Calculation

The `calculateCashFlowMetrics` function handles cash flow analysis:

- Organizes transactions by time periods
- Calculates inflows and outflows
- Computes running totals
- Provides period-over-period comparisons

### Data Processing

The APIs fetch data from the `HomeCLVReport` model, which stores aggregated transaction data. The endpoints leverage Django's query capabilities to filter, group, and aggregate the data before applying business logic for metrics calculation.

## Usage Examples

### Basic CLV Analysis

```
GET /reporting/customer-lifetime-value/overall?clientID[]=client123&original_sale_start_date=2024-01-01&original_sale_end_date=2024-03-31&show_by_date=1&show_by_network=0&show_by_pub=0
```

This request retrieves CLV metrics for a specific client, grouped by date, for Q1 2024.

### Network Performance Comparison

```
GET /reporting/customer-lifetime-value/overall?flow_brand_name[]=Product1&flow_brand_name[]=Product2&show_by_date=0&show_by_network=1&show_by_pub=0
```

This request compares CLV metrics across different networks for selected product flows.

### Detailed Publisher Analysis

```
GET /reporting/customer-lifetime-value/overall?network[]=Network1&show_by_date=0&show_by_network=0&show_by_pub=1
```

This request breaks down CLV metrics by publisher within a specific network.
