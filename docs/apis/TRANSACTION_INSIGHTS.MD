# Transaction Insights API Documentation

## Overview

The Transaction Insights API provides detailed analytics and reporting on transaction data across various dimensions. These endpoints enable in-depth analysis of transaction patterns, performance breakdowns, and comparative metrics to inform business decisions.

## Key Concepts

- **Breakdown Analysis**: Segmentation of transaction data by various dimensions
- **Chart View**: Visualization-ready data formats for charting
- **Metric Calculations**: Standardized calculations for key performance metrics
- **Dimension Filtering**: Ability to filter and segment data by multiple dimensions

## Endpoints

### 1. Get Analysis Chart View

```
GET /reporting/analysis/chart-view
```

Retrieves data formatted for chart visualization of transaction metrics.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns time-series data suitable for charting, including:

- Time period points
- Transaction metrics over time
- Comparative metrics across segments
- Trend indicators

### 2. Get Analysis Breakdown by Issuer

```
GET /reporting/analysis/breakdown-by-issuer
```

Provides transaction metrics broken down by card issuer.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns transaction metrics segmented by card issuer:

- Major banks vs. longtail issuers
- Transaction volume by issuer
- Approval rates
- Decline rates
- Chargeback rates

### 3. Get Flow Brand Name Breakdown

```
GET /reporting/flow-brand-name
```

Breaks down transaction metrics by flow brand name.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns transaction metrics segmented by flow brand:

- Transaction counts
- Revenue metrics
- Performance indicators
- Relative performance

### 4. Get Flow Step Number Breakdown

```
GET /reporting/flow-step-number
```

Breaks down transaction metrics by flow step number (initial offer, upsells).

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns transaction metrics segmented by flow step number:

- Step-specific performance
- Conversion rates between steps
- Revenue contribution by step

### 5. Get Card Brand Breakdown

```
GET /reporting/card-brand
```

Breaks down transaction metrics by card brand (Visa, Mastercard, etc.).

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns transaction metrics segmented by card brand:

- Brand-specific approval rates
- Transaction volumes
- Revenue contributions
- Chargeback rates by brand

### 6. Get Card Type Breakdown

```
GET /reporting/card-type
```

Breaks down transaction metrics by card type (credit, debit).

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns transaction metrics segmented by card type:

- Credit vs. debit performance
- Transaction volumes
- Approval rates
- Chargeback rates

### 7. Get Price Point Breakdown

```
GET /reporting/price-point
```

Breaks down transaction metrics by price point.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns transaction metrics segmented by price point:

- Performance by price tier
- Conversion rates
- Revenue contribution
- Refund and chargeback rates

### 8. Get Cycle Breakdown

```
GET /reporting/cycle
```

Breaks down transaction metrics by billing cycle number.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns transaction metrics segmented by cycle number:

- Initial vs. rebill performance
- Retention rates
- Revenue by cycle
- Lifetime value indicators

### 9. Get Issuer Breakdown

```
GET /reporting/issuer
```

Breaks down transaction metrics by card issuer.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns transaction metrics segmented by issuer:

- Performance by issuer category
- Transaction volumes
- Approval rates
- Decline patterns

### 10. Get 3DS Breakdown

```
GET /reporting/3ds
```

Breaks down transaction metrics by 3DS verification status.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns transaction metrics segmented by 3DS status:

- 3DS vs. non-3DS performance
- Approval rates
- Chargeback rates
- Revenue impact

### 11. Get 3DS by MID Breakdown

```
GET /reporting/3ds-by-mid
```

Breaks down 3DS metrics by merchant ID.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns 3DS metrics segmented by merchant ID:

- 3DS implementation by MID
- Performance impacts
- Approval rate differences
- Chargeback protection effectiveness

### 12. Download Report

```
GET /reporting/download-report
```

Downloads breakdown report data in CSV format.

#### Parameters

- Standard filtering parameters, plus:
- `type_report_name`: The type of breakdown to download (flow_brand_name, flow_step_number, card_brand, cycle, card_type, price_point, issuer, 3ds, 3ds_by_mid)

#### Response

Returns a CSV file containing the requested breakdown report.

### 13. Download Report Chart View

```
GET /reporting/downloadReportChartView
```

Downloads chart view data in CSV format.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns a CSV file containing the chart view data.

### 14. Get Filter Metadata

```
GET /reporting/meta-data
```

Retrieves metadata for populating transaction insights filter UI components.

#### Parameters

- `show_by_date`, `show_by_network`, `show_by_pub`: Display options
- `showing_filters[]`: Which filters to show
- `lst_flow_brand_name[]`: Flow brand names to filter by
- `clientID[]`: Client IDs to filter by

#### Response

Returns data for populating filter dropdowns and other UI components.

## Implementation Details

### Data Processing

The transaction insights endpoints use the `HomeCLVReport` model as their primary data source, applying various filters, groupings, and aggregations to generate the required metrics.

### Chart View Data Generation

The `get_chart_view_data` function handles the preparation of time-series data for chart visualizations:

- Filters data based on user parameters
- Groups data by time period
- Calculates metrics for each period
- Formats data for chart consumption

### Filtering Logic

The `filterData` function applies user-specified filters to the raw data:

- Date range filters
- Client and flow filters
- Card and transaction type filters
- Network and publisher filters
- Sale volume filters

### Metric Calculations

The `getTotalData` function handles the calculation of metrics for various breakdown reports:

- Groups data by the specified dimension
- Calculates aggregate metrics for each group
- Normalizes percentages and rates
- Sorts results by specified criteria

## Usage Examples

### Basic Card Brand Analysis

```
GET /reporting/card-brand?clientID[]=client123&original_sale_start_date=2024-01-01&original_sale_end_date=2024-03-31
```

This request retrieves a breakdown of transaction metrics by card brand for Q1 2024.

### Comparative Flow Analysis

```
GET /reporting/flow-brand-name?flow_brand_name[]=Product1&flow_brand_name[]=Product2
```

This request compares performance metrics between two product flows.

### 3DS Impact Analysis

```
GET /reporting/3ds?merchantId[]=12345&merchantId[]=67890
```

This request analyzes the impact of 3DS verification across different merchant IDs.

### Time-Series Performance View

```
GET /reporting/analysis/chart-view?show_by_date=1&show_by_network=0&show_by_pub=0&original_sale_start_date=2024-01-01&original_sale_end_date=2024-03-31
```

This request generates time-series data for charting transaction performance over Q1 2024.
