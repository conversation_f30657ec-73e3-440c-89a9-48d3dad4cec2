# Daily Performance Reports API Documentation

## Overview

The Daily Performance Reports API provides endpoints for tracking and analyzing daily operational metrics. These reports focus on day-to-day performance across various dimensions including traffic sources, payment processing, and 3DS verification.

## Key Concepts

- **Daily Sales Recap**: Summary of daily sales performance metrics
- **Traffic Source Performance**: Analysis of performance by traffic source/affiliate
- **3DS Coverage**: Metrics related to 3D Secure authentication coverage
- **MID Performance**: Analysis of merchant ID (MID) performance

## Endpoints

### 1. Get Filter Values

```
GET /reporting/daily-performance-summary-reports/filter
```

Retrieves available filter values for the daily performance reports.

#### Parameters

None required.

#### Response

Returns filter options including:

- Available date ranges
- Client IDs
- Flow brand names
- Card brands and types
- Merchant IDs
- Traffic sources

### 2. Daily Sales Recap

```
GET /reporting/daily-performance-summary-reports/daily-sales-recap
```

Provides a comprehensive daily recap of sales performance.

#### Parameters

- `transaction_date_start`, `transaction_date_end`: Filter by transaction date range
- `clientID[]`: Filter by specific client IDs
- `flow_brand_name[]`: Filter by flow brand names
- `card_brand[]`: Filter by card brands
- `card_type[]`: Filter by card types (credit, debit)
- `merchantId[]`: Filter by merchant IDs
- Table parameters: pagination, sorting, searching

#### Response

Returns daily performance metrics including:

- Total sales amount
- Transaction count
- Approval rate
- Refund count and amount
- Void count and amount
- Chargeback count and amount
- 3DS verification rate
- Foreign card rate

### 3. Download Daily Sales Recap

```
GET /reporting/daily-performance-summary-reports/daily-sales-recap/download
```

Downloads the daily sales recap data in CSV format.

#### Parameters

Same as "Daily Sales Recap" endpoint.

#### Response

Returns a CSV file containing the daily sales recap data.

### 4. Traffic Source Performance

```
GET /reporting/daily-performance-summary-reports/traffic-source-performance
```

Analyzes performance metrics by traffic source/affiliate.

#### Parameters

Same as "Daily Sales Recap" endpoint.

#### Response

Returns performance metrics grouped by traffic source:

- Sales volume by source
- Approval rates
- Refund rates
- Void rates
- Chargeback rates
- Revenue metrics

### 5. Download Traffic Source Performance

```
GET /reporting/daily-performance-summary-reports/traffic-source-performance/download
```

Downloads the traffic source performance data in CSV format.

#### Parameters

Same as "Traffic Source Performance" endpoint.

#### Response

Returns a CSV file containing the traffic source performance data.

### 6. 3DS Coverage Report

```
GET /reporting/daily-performance-summary-reports/3ds-coverage
```

Provides analysis of 3D Secure authentication coverage.

#### Parameters

Same as "Daily Sales Recap" endpoint.

#### Response

Returns 3DS coverage metrics:

- 3DS enabled transaction count and percentage
- Non-3DS transaction count and percentage
- Performance comparison between 3DS and non-3DS transactions
- Approval rates
- Decline rates
- Chargeback rates

### 7. Download 3DS Coverage Report

```
GET /reporting/daily-performance-summary-reports/3ds-coverage/download
```

Downloads the 3DS coverage report in CSV format.

#### Parameters

Same as "3DS Coverage Report" endpoint.

#### Response

Returns a CSV file containing the 3DS coverage report data.

### 8. MID Performance Analysis

```
GET /reporting/daily-performance-summary-reports/midPerformanceAnalysis
```

Analyzes performance metrics by merchant ID (MID).

#### Parameters

Same as "Daily Sales Recap" endpoint, plus table parameters.

#### Response

Returns performance metrics grouped by MID:

- Transaction count and volume
- Approval rates
- Decline rates
- Refund counts and rates
- Void counts and rates
- Chargeback counts and rates
- 3DS verification rates
- Foreign card rates

### 9. Download MID Performance Analysis

```
GET /reporting/daily-performance-summary-reports/midPerformanceAnalysis/download
```

Downloads the MID performance analysis data in CSV format.

#### Parameters

Same as "MID Performance Analysis" endpoint.

#### Response

Returns a CSV file containing the MID performance analysis data.

## Implementation Details

### Data Processing

The daily performance reports use the `HomeCLVReport` model as their primary data source, applying various filters and aggregations to generate the required metrics.

### Summary Data Calculation

The `getDailyPerformanceSummary` function handles the filtering of data based on user-specified parameters, applying date range, client, and other filters to the raw data.

#### Daily Sales Recap Generation

The `create_daily_sales_recap_data` function:

- Groups data by date
- Calculates daily totals for transactions, revenue, refunds, voids, and chargebacks
- Computes rates (approval rate, refund rate, etc.)
- Formats the data for presentation

#### Traffic Source Analysis

The `create_traffic_source_performance_data` function:

- Groups data by traffic source/affiliate
- Calculates performance metrics for each source
- Identifies top and underperforming sources

#### 3DS Coverage Analysis

The `create_3ds_coverage_data` function:

- Segments transactions by 3DS status
- Compares performance metrics between 3DS and non-3DS transactions
- Calculates coverage rates and efficiencies

#### MID Performance Analysis

The `create_mid_performance_data` function:

- Groups data by merchant ID
- Calculates performance metrics for each MID
- Identifies performance patterns and anomalies

## Usage Examples

### Basic Daily Recap

```
GET /reporting/daily-performance-summary-reports/daily-sales-recap?transaction_date_start=2024-03-01&transaction_date_end=2024-03-31
```

This request retrieves daily sales recap for March 2024.

### Traffic Source Comparison

```
GET /reporting/daily-performance-summary-reports/traffic-source-performance?clientID[]=client123&card_brand[]=VISA&card_brand[]=MASTERCARD
```

This request compares traffic source performance for a specific client, filtered by selected card brands.

### 3DS Effectiveness Analysis

```
GET /reporting/daily-performance-summary-reports/3ds-coverage?flow_brand_name[]=Flow1&merchantId[]=12345
```

This request analyzes 3DS coverage for a specific flow and merchant ID combination.
