# Sale Flow Management API Documentation

## Overview

The Sale Flow Management API provides endpoints for configuring, managing, and analyzing sales process flows. These endpoints allow for the creation and modification of sales funnels, offer steps, and product details within a unified flow system.

## Key Concepts

- **Sale Flow**: A defined sales process path that customers follow
- **Flow Steps**: Individual stages within a sale flow (e.g., initial offer, upsell 1, upsell 2)
- **Product Details**: Specific product configurations offered at each step
- **Cycle Numbers**: Indicators of billing cycles for rebill products

## Endpoints

### 1. Get List of Clients

```
GET /sale-flow/getListClients
```

Retrieves a list of active clients that have valid API credentials.

#### Parameters

None required.

#### Response

Returns a list of clients with their IDs and names.

### 2. Get Client Login Information

```
GET /sale-flow/getLoginByClient
```

Retrieves CRM login information for a specific client.

#### Parameters

- `clientID`: The ID of the client to retrieve login information for

#### Response

Returns a list of CRM login credentials for the specified client, including:

- Login ID
- Login platform
- CRM information
- Username

### 3. Get List of Products

```
GET /sale-flow/getListProducts
```

Retrieves a list of products available for a specific CRM login.

#### Parameters

- `loginID`: The ID of the CRM login to retrieve products for

#### Response

Returns a list of product match strings available for the specified CRM login.

### 4. Get Table of Sale Flows

```
GET /sale-flow/getTableSaleFlows
```

Retrieves a paginated table of sale flows.

#### Parameters

- Table parameters: pagination, sorting, searching

#### Response

Returns a paginated list of sale flows with their details:

- Flow ID
- Client name
- Flow brand name
- Number of steps
- CRM information
- Creation date
- Status

### 5. View Sale Flow

```
GET /sale-flow/viewSaleFlow
```

Retrieves detailed information about a specific sale flow.

#### Parameters

- `flow_id`: The ID of the flow to retrieve

#### Response

Returns detailed information about the specified flow:

- Flow information (ID, brand name, descriptors)
- Client and CRM details
- Steps information:
  - Step number
  - Type (Initial Offer, Upsell)
  - Product match
  - Initial price
  - Product/shipping costs
  - Continuity cycle frequency
  - Product details for each cycle

### 6. Adjust Sale Flow

```
POST /sale-flow/adjustSaleFlow
```

Creates a new sale flow or updates an existing one.

#### Parameters (POST body)

- `flow_id`: (Optional) The ID of the flow to update (if omitted, creates a new flow)
- Flow information:
  - `flow_brand_name`: Name of the flow
  - `flow_descriptor1` through `flow_descriptor5`: Additional descriptors
  - `clientID`: Client ID
  - `crm_id`: CRM login ID
- `steps`: Array of step objects containing:
  - `flow_step_number`: Step number (1 for initial, 2+ for upsells)
  - `cont_cycle_frequency`: Continuity cycle frequency
  - `product_match`: Product identifier
  - `initial_price`: Initial price
  - `product_cost`: Product cost
  - `shipping_cost`: Shipping cost
  - `products`: Array of product cycle details:
    - `cycle_number`: Cycle number (1+ for rebills)
    - `step_price`: Price for this cycle
    - `product_match`: Product identifier
    - `product_cost`: Product cost
    - `shipping_cost`: Shipping cost

#### Response

Returns the updated flow data and a message indicating success.

### 7. Remove Sale Flow

```
DELETE /sale-flow/removeSaleFlow
```

Marks a sale flow as deleted.

#### Parameters

- `flow_id`: The ID of the flow to delete

#### Response

Returns a success message if the flow was successfully marked as deleted.

## Implementation Details

### Flow Data Structure

Each sale flow follows a structured hierarchy:

- **Flow**: Top-level container with brand name and descriptors
- **Steps**: Ordered sequence of offer steps (initial offer, upsells)
- **Products**: Product configurations for each cycle within a step

### Data Extraction

The `extractModelData` function handles the extraction of data from model instances, creating structured representations of flows, steps, and products.

### Flow Processing

When a flow is created or updated:

1. Flow-level data is saved
2. Each step is processed in sequence:
   - Initial step (flow_step_number=1) must be added first
   - Subsequent steps must follow in order
3. For each step, cycle 0 product and additional cycle products are saved
4. Data flow processing is triggered in a background thread

### Flow Deletion

Flows are not physically deleted but marked with a "Deleted" status, preserving historical data while removing them from active use.

## Usage Examples

### Creating a Basic Flow

```
POST /sale-flow/adjustSaleFlow
```

With body:

```json
{
  "flow_brand_name": "Weight Loss Program",
  "clientID": "client123",
  "crm_id": "crm456",
  "steps": [
    {
      "flow_step_number": 1,
      "cont_cycle_frequency": 30,
      "product_match": "WeightLossInitial",
      "initial_price": 39.95,
      "product_cost": 5.0,
      "shipping_cost": 4.95,
      "products": [
        {
          "cycle_number": 1,
          "step_price": 49.95,
          "product_match": "WeightLossRebill",
          "product_cost": 5.0,
          "shipping_cost": 0.0
        }
      ]
    }
  ]
}
```

### Adding an Upsell to Existing Flow

```
POST /sale-flow/adjustSaleFlow
```

With body:

```json
{
  "flow_id": 123,
  "flow_brand_name": "Weight Loss Program",
  "clientID": "client123",
  "crm_id": "crm456",
  "steps": [
    {
      "flow_step_number": 1,
      "cont_cycle_frequency": 30,
      "product_match": "WeightLossInitial",
      "initial_price": 39.95,
      "product_cost": 5.0,
      "shipping_cost": 4.95,
      "products": [
        {
          "cycle_number": 1,
          "step_price": 49.95,
          "product_match": "WeightLossRebill",
          "product_cost": 5.0,
          "shipping_cost": 0.0
        }
      ]
    },
    {
      "flow_step_number": 2,
      "cont_cycle_frequency": 30,
      "product_match": "WeightLossBooster",
      "initial_price": 19.95,
      "product_cost": 3.0,
      "shipping_cost": 0.0,
      "products": []
    }
  ]
}
```

### Viewing Flow Details

```
GET /sale-flow/viewSaleFlow?flow_id=123
```

### Deleting a Flow

```
DELETE /sale-flow/removeSaleFlow?flow_id=123
```
