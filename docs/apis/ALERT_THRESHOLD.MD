# Alert Threshold Management API Documentation

## Overview

The Alert Threshold Management API provides endpoints for configuring, monitoring, and managing alert thresholds for key performance metrics. These thresholds allow for proactive monitoring of business operations and automatic notification when metrics exceed defined limits.

## Key Concepts

- **Alert Threshold**: A defined limit for a specific metric that, when crossed, triggers a notification
- **Dimension**: The entity to monitor (client, network, affiliate, flow, card brand, card type, merchant ID)
- **Metric**: The specific value to monitor (chargeback rate, refund rate, etc.)
- **Trigger Frequency**: The time period for aggregating data (days)
- **Condition**: The comparison operator for the threshold (>, <, >=, <=)

## Endpoints

### 1. Create Alert Threshold

```
POST /alert-threshold/createAlertThreshold
```

Creates or updates an alert threshold configuration.

#### Parameters

- `threshold_id`: (Optional) Existing threshold ID for updates
- `name`: Name of the threshold
- `dimension`: Entity to monitor (client, network, affiliate, flow_brand_name, card_brand, card_type, merchantId)
- `metric`: Metric to monitor (chargeback_rate, refund_rate, void_rate, transaction_rate, foreign_card_rate, initial_approval_rate)
- `value`: Threshold value
- `clientID`: Client ID
- `dimension_value`: Specific value of the dimension to monitor
- `is_active`: Whether the threshold is active
- `trigger_frequency`: Time period for aggregation in days
- `lst_email`: Comma-separated list of notification email addresses
- `condition`: Comparison operator (<, >, <=, >=)

#### Response

Returns a success message and the created/updated threshold details.

### 2. List Alert Thresholds

```
GET /alert-threshold/listAlertThresholds
```

Retrieves a list of configured alert thresholds.

#### Parameters

None required.

#### Response

Returns a list of alert thresholds with their configuration details:

- Threshold ID
- Name
- Dimension and dimension value
- Metric and threshold value
- Status (active/inactive)
- Notification configuration
- Current metric value
- Last triggered timestamp

### 3. Delete Alert Threshold

```
DELETE /alert-threshold/deleteAlertThreshold
```

Deletes an alert threshold configuration.

#### Parameters

- `threshold_id`: ID of the threshold to delete

#### Response

Returns a success message if the threshold was deleted.

### 4. Get Alert Threshold Details

```
GET /alert-threshold/getAlertThreshold
```

Retrieves detailed information about a specific alert threshold.

#### Parameters

- `threshold_id`: ID of the threshold to retrieve

#### Response

Returns detailed configuration information for the specified threshold:

- Basic configuration (name, dimension, metric, etc.)
- Current metric value
- Historical triggers
- Notification settings

### 5. Get Alert Threshold Overview

```
GET /alert-threshold/getAlertThresholdOverview
```

Provides a summary overview of alert thresholds and their statuses.

#### Parameters

None required.

#### Response

Returns an overview of alert thresholds:

- Count of active/inactive thresholds
- Count of triggered alerts
- Distribution by dimension and metric
- Recent trigger history

### 6. Get Current Value

```
GET /alert-threshold/getCurrentValue
```

Retrieves the current value of a metric for a specific dimension and dimension value.

#### Parameters

- `clientID`: Client ID
- `dimension`: Entity to check (client, network, affiliate, flow_brand_name, card_brand, card_type, merchantId)
- `metric`: Metric to check (chargeback_rate, refund_rate, void_rate, transaction_rate, foreign_card_rate, initial_approval_rate)
- `trigger_frequency`: Time period for aggregation in days
- `dimension_value`: Specific value of the dimension to check

#### Response

Returns the current value of the specified metric and threshold status:

- Current metric value
- Comparison with threshold
- Historical trend
- Supporting data points

### 7. Get Filter Metadata

```
GET /alert-threshold/meta-data
```

Retrieves metadata for populating alert threshold filter UI components.

#### Parameters

- `showing_filters[]`: Which filters to show
- `clientID[]`: Client IDs to filter by

#### Response

Returns data for populating filter dropdowns and other UI components:

- Available dimensions (flow_brand_name, card_brand, card_type, network, affiliate, merchantId)
- Available values for each dimension

## Implementation Details

### Alert Threshold Evaluation

The system periodically evaluates all active thresholds using the `getCurrentData` function:

1. Retrieves data for the specified client, dimension, and dimension value
2. Aggregates data based on the trigger frequency
3. Calculates the specified metric
4. Compares the metric value against the threshold using the defined condition
5. Triggers an alert if the condition is met

### Metrics Calculation

The system supports various metrics:

- **Chargeback Rate**: `chargebacks / transactions`
- **Refund Rate**: `refunds / transactions`
- **Void Rate**: `voids / transactions`
- **Transaction Rate**: Total transaction count comparison
- **Foreign Card Rate**: `foreign_card_transactions / total_transactions`
- **Initial Approval Rate**: `approved_initial_transactions / total_initial_transactions`

### Notification System

When an alert threshold is triggered:

1. The system records the trigger event
2. Notification emails are sent to the addresses in `lst_email`
3. The alert appears in the alert threshold overview

## Usage Examples

### Creating a Chargeback Alert

```
POST /alert-threshold/createAlertThreshold
```

With body:

```json
{
  "name": "High Chargeback Alert",
  "dimension": "flow_brand_name",
  "metric": "chargeback_rate",
  "value": 1.5,
  "clientID": "client123",
  "dimension_value": "WeightLossProgram",
  "is_active": true,
  "trigger_frequency": 7,
  "lst_email": "<EMAIL>,<EMAIL>",
  "condition": ">"
}
```

This creates an alert that triggers when the chargeback rate for the WeightLossProgram flow exceeds 1.5% over a 7-day period.

### Checking Current Metric Value

```
GET /alert-threshold/getCurrentValue?clientID=client123&dimension=network&metric=refund_rate&trigger_frequency=3&dimension_value=Facebook
```

This checks the current refund rate for Facebook traffic over the past 3 days.

### Viewing Active Alerts

```
GET /alert-threshold/getAlertThresholdOverview
```

This provides an overview of all active alert thresholds and their status.
