# Fraud Reporting API Documentation

## Overview

The Fraud Reporting API provides endpoints for monitoring, analyzing, and responding to potentially fraudulent transactions. These tools help identify suspicious traffic sources, high-risk transactions, and fraud patterns to minimize financial losses.

## Key Concepts

- **Fraud Detection**: Algorithms and models to identify potentially fraudulent transactions
- **Traffic Source Analysis**: Evaluation of traffic sources by fraud risk
- **Recommendation System**: AI-powered system that flags suspicious transactions
- **Action Management**: System for reviewing and taking action on flagged transactions

## Endpoints

### 1. Get Fraud Overview

```
GET /fraud/getFraudOverview
```

Provides a high-level overview of fraud metrics for the selected time period.

#### Parameters

- Standard filtering parameters (date range, client, flow, etc.)

#### Response

Returns summary fraud metrics including:

- Total fraud score
- Identified fraud transactions count and rate
- Financial impact estimates
- Trend analysis compared to previous periods

### 2. Get Traffic Source Table

```
GET /fraud/getTrafficSourceTable
```

Retrieves detailed fraud metrics by traffic source.

#### Parameters

- Standard filtering parameters, plus table parameters for pagination, sorting, and searching

#### Response

Returns fraud metrics by traffic source:

- Traffic source identifiers
- Transaction volume
- Fraud score
- Retention rate
- Expected vs. actual performance
- Recommended actions

### 3. Download Traffic Source Table

```
GET /fraud/downloadTrafficSourceTable
```

Downloads the traffic source fraud data in CSV format.

#### Parameters

Same as "Get Traffic Source Table" endpoint.

#### Response

Returns a CSV file containing the traffic source fraud data.

### 4. Get Recommendation Transactions

```
GET /fraud/getRecommendationTransactions
```

Retrieves recommended transactions for review based on fraud indicators.

#### Parameters

- Standard filtering parameters, plus:
- `traffic_source`: Specific traffic source to analyze
- `flow_brand_name`: Specific flow brand to analyze

#### Response

Returns a list of transactions flagged for review:

- Transaction IDs and details
- Fraud indicators and scores
- Customer information
- Transaction history
- Recommended actions

### 5. Post Action Transaction

```
POST /fraud/postActionTransaction
```

Records an action taken on a flagged transaction.

#### Parameters

- `id`: Transaction ID
- `action`: Action taken (e.g., "approve", "deny", "flag", "review")

#### Response

Returns confirmation of the action recorded.

### 6. Get Filter Metadata

```
GET /fraud/meta-data
```

Retrieves metadata for populating fraud reporting filter UI components.

#### Parameters

- `showing_filters[]`: Which filters to show
- `lst_flow_brand_name[]`: Flow brand names to filter by
- `clientID[]`: Client IDs to filter by

#### Response

Returns data for populating filter dropdowns and other UI components.

## Implementation Details

### Fraud Model

The system uses a machine learning model to identify potentially fraudulent transactions based on:

- Historical chargeback patterns
- Vendor/traffic source performance
- Customer behavior patterns
- Card information
- Geographic indicators

### Data Processing Functions

#### `getDataAfterFilter`

Applies user-defined filters to order data for fraud analysis.

#### `getAllOrderImports` and `getAllOrderUpdate`

Retrieve transaction data with appropriate joins for fraud analysis.

#### `scale_score` and `preprocess_data`

Prepare transaction data for analysis by the fraud model.

#### `get_expect_retention_rate` and `get_fraud_score`

Calculate expected retention rates and fraud scores based on historical data.

#### `calculate_vendor_specific_retention`

Analyzes retention rates by vendor to identify anomalies.

#### `set_fraud_label`

Assigns fraud labels to transactions based on model outputs.

#### `UpdateFraudModel`

Updates the fraud detection model with new transaction data.

### Fraud Reporting Flow

1. User selects filters for fraud analysis
2. System retrieves filtered transaction data
3. Fraud model calculates risk scores
4. Transactions are grouped by traffic source/vendor
5. Expected vs. actual performance is calculated
6. Recommendations are generated for high-risk sources
7. Individual transactions may be flagged for review

## Model Training

The `UpdateFraudModel` function handles periodic updates to the fraud detection model:

- Processes recent transaction data
- Identifies confirmed fraud cases (e.g., chargebacks)
- Updates model parameters
- Calibrates scoring thresholds

## Usage Examples

### Basic Fraud Overview

```
GET /fraud/getFraudOverview?transaction_date_start=2024-03-01&transaction_date_end=2024-03-31
```

This request retrieves a fraud overview for March 2024.

### Traffic Source Analysis

```
GET /fraud/getTrafficSourceTable?clientID[]=client123&show_by_date=0&sortByVal=fraud_score&orderByVal=DESC
```

This request retrieves traffic sources sorted by fraud score in descending order.

### Reviewing Flagged Transactions

```
GET /fraud/getRecommendationTransactions?traffic_source=TS001&flow_brand_name=FlowBrand
```

This request retrieves transactions requiring review for a specific traffic source and flow brand.

### Taking Action on a Transaction

```
POST /fraud/postActionTransaction
```

With body:

```json
{
  "id": 12345,
  "action": "deny"
}
```

This records a "deny" action for the specified transaction.
