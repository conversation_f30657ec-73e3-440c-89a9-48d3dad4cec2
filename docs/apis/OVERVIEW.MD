# FLOFIN API Documentation

## Overview

This document provides an overview of all API endpoints available in the FLOFIN application. The system is organized into several service areas, each handling specific aspects of the application's functionality.

## API Service Areas

1. **Customer Lifetime Value (CLV)** - Analyze and report on customer value over time
2. **Daily Performance Reports** - Track daily performance metrics across various dimensions
3. **Sale Flow Management** - Configure and manage sales process flows
4. **Fraud Reporting** - Monitor and manage potentially fraudulent transactions
5. **Alert Threshold Management** - Configure and manage alert thresholds for key metrics
6. **Transaction Insights** - Detailed analytics and insights on transaction data
7. **Client Management** - Utility functions for managing client data and views

## Endpoint Organization

The API endpoints are organized under specific URL paths that reflect their purpose:

```
/reporting/customer-lifetime-value/...   # CLV reporting endpoints
/reporting/daily-performance-summary-reports/...   # Daily performance reporting
/reporting/analysis/...   # Transaction insights analysis
/sale-flow/...   # Sale flow management
/fraud/...   # Fraud reporting
/alert-threshold/...   # Alert threshold management
```

## Common Parameters

Many endpoints share common parameters:

- **clientID[]** - Filter by specific client IDs
- **flow_brand_name[]** - Filter by flow brand names
- **card_brand[]** - Filter by card brands
- **network[]** - Filter by networks
- **pub[]** - Filter by publishers
- **original_sale_start_date/end_date** - Filter by original sale date range
- **as_of_date_start_date/end_date** - Filter by "as of" date range
- **show_by_date/network/pub** - Control display grouping
- **sales_start_number/end_number** - Filter by sales volume

## Authentication and Security

All endpoints require authentication using the `@require_login` decorator.

## Pagination and Filtering

Many table endpoints support:

- Pagination (page, limit)
- Sorting (sortByVal, orderByVal)
- Searching (search_all, search_key, search_by)

## Documentation Structure

Detailed documentation for each service area is available in separate files:

1. [Customer Lifetime Value API](./apis/README_API_CLV.MD)
2. [Daily Performance Reports API](./apis/README_API_DAILY_PERFORMANCE.MD)
3. [Sale Flow Management API](./apis/README_API_SALE_FLOW.MD)
4. [Fraud Reporting API](./apis/README_API_FRAUD.MD)
5. [Alert Threshold Management API](./apis/README_API_ALERT_THRESHOLD.MD)
6. [Transaction Insights API](./apis/README_API_TRANSACTION_INSIGHTS.MD)
7. [Utility Functions API](./apis/README_API_UTILS.MD)
