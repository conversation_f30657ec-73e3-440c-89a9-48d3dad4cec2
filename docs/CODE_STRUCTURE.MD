# FLOFIN Codebase Structure

This document provides a comprehensive overview of the FLOFIN codebase structure, detailing the organization of directories, key components, and their interactions.

## Directory Structure

```
flofin-be/
├── config/               # Django project configuration
│   ├── settings/         # Environment-specific settings
│   ├── urls.py           # Main URL routing
│   └── wsgi.py           # WSGI configuration
├── apps/                 # Django applications
│   ├── accounts/         # User account management
│   ├── core/             # Core functionality and shared components
│   ├── crm/              # CRM integration services
│   ├── payments/         # Payment processing and management
│   ├── products/         # Product catalog and management
│   ├── reporting/        # Reporting and analytics
│   └── alerts/           # Monitoring and alerting system
├── api/                  # API endpoints and serializers
│   ├── v1/               # Version 1 of the API
│   └── common/           # Shared API components
├── utils/                # Utility functions and helpers
├── tasks/                # Celery task definitions
├── templates/            # HTML templates
├── static/               # Static assets
├── docs/                 # Documentation
├── scripts/              # Management and deployment scripts
├── tests/                # Automated tests
└── manage.py             # Django management script
```

## Core Applications

### accounts

Handles user authentication, authorization, and profile management.

**Key Models:**

- `User` - Extended Django user with additional profile fields
- `Role` - User roles for permission management
- `Permission` - Custom permission definitions

**Key Components:**

- Authentication backend with JWT support
- Permission middleware
- User management API endpoints

### core

Contains core functionality and components shared across the system.

**Key Models:**

- `BaseModel` - Abstract base model with common fields
- `Tenant` - Multi-tenancy support
- `AuditLog` - System activity tracking

**Key Components:**

- Base classes for views, models, and serializers
- Common middleware
- Utility functions and decorators

### crm

Manages integration with external CRM systems.

**Key Models:**

- `CRMConnection` - Connection details for different CRM platforms
- `CRMImportLog` - Record of data imports
- `CRMMapping` - Field mappings between CRM and internal data models

**Key Components:**

- CRM client adapters for different platforms
- Import/sync services
- Webhooks for real-time updates

### payments

Handles payment processing, transactions, and financial records.

**Key Models:**

- `Transaction` - Record of financial transactions
- `PaymentMethod` - Payment methods configuration
- `PaymentGateway` - Payment gateway integration settings
- `Refund` - Refund records
- `Chargeback` - Chargeback records

**Key Components:**

- Payment gateway integrations
- Transaction processing services
- Refund and chargeback handling

### products

Manages product catalog and related configurations.

**Key Models:**

- `Product` - Product information
- `ProductCategory` - Product categorization
- `Pricing` - Product pricing models
- `Subscription` - Subscription plan definitions

**Key Components:**

- Product management services
- Pricing calculation logic
- Subscription handling

### reporting

Provides reporting and analytics functionality.

**Key Models:**

- `Report` - Report definitions
- `ReportSchedule` - Scheduled report generation
- `Dashboard` - Dashboard configuration

**Key Components:**

- Report generation services
- Data aggregation utilities
- Export functionality

### alerts

Implements the monitoring and alerting system.

**Key Models:**

- `AlertDefinition` - Alert configuration
- `AlertInstance` - Record of triggered alerts
- `Notification` - Notification configuration

**Key Components:**

- Alert checking services
- Notification dispatch system
- Alert management

## API Structure

The API follows RESTful design principles, organized by resource type:

```
api/
├── v1/
│   ├── accounts/          # User account endpoints
│   ├── transactions/      # Transaction endpoints
│   ├── products/          # Product endpoints
│   ├── crm/               # CRM integration endpoints
│   ├── reports/           # Reporting endpoints
│   └── alerts/            # Alert and monitoring endpoints
└── common/
    ├── pagination.py      # Pagination classes
    ├── permissions.py     # Permission classes
    ├── serializers.py     # Base serializers
    └── views.py           # Base view classes
```

**Key API Features:**

- JWT authentication
- Rate limiting
- Comprehensive filtering
- Detailed error responses
- API documentation with Swagger/OpenAPI

## Background Task Processing

FLOFIN uses Celery for asynchronous task processing.

**Key Task Categories:**

- Data import tasks
- Report generation
- Notification dispatch
- Scheduled monitoring
- Cleanup and maintenance

**Task Structure:**

```
tasks/
├── base.py               # Base task classes
├── crm_import.py         # CRM data import tasks
├── payment_processing.py # Payment processing tasks
├── reporting.py          # Report generation tasks
├── monitoring.py         # System monitoring tasks
└── notifications.py      # Notification tasks
```

## Database Models Relationships

FLOFIN uses a relational database (PostgreSQL) with the following high-level data model relationships:

```
User ─────┐
          │
          ▼
Transaction ◄─── Product
    │
    ├─── Customer
    │
    ├─── PaymentMethod
    │
    └─── CRMReference
```

For a detailed view of database schema, refer to [DATABASE_STRUCTURE.MD](DATABASE_STRUCTURE.MD).

## Authentication & Authorization

- JWT-based authentication for API access
- Session-based authentication for admin interfaces
- Role-based access control
- Object-level permissions
- Permission checking at the API and service layers

## Testing Architecture

```
tests/
├── unit/                 # Unit tests for individual components
├── integration/          # Integration tests across components
├── api/                  # API endpoint tests
├── factories/            # Test data factories
├── fixtures/             # Test fixtures
└── conftest.py           # pytest configuration
```

**Testing Components:**

- pytest test runner
- factory_boy for test data generation
- pytest-django for Django integration
- pytest-cov for code coverage
- Mock objects for external dependencies

## Logging and Monitoring

The system implements comprehensive logging:

```python
# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/flofin.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'flofin': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

## Configuration Management

Configuration is managed through:

- Environment variables
- Django settings modules for different environments
- Configuration files for third-party services
- Feature flags for conditional functionality

## Code Style and Standards

The codebase follows:

- PEP 8 style guide for Python code
- Django coding style for Django-specific components
- Type hints for better IDE integration and documentation
- Docstrings for all modules, classes, and functions

## Key Design Patterns

FLOFIN implements several design patterns:

- **Repository Pattern**: For data access
- **Service Layer**: For business logic
- **Command Pattern**: For transactional operations
- **Factory Pattern**: For object creation
- **Strategy Pattern**: For interchangeable algorithms
- **Observer Pattern**: For event handling

## External Dependencies

Major external dependencies include:

- Django - Web framework
- Django REST Framework - API toolkit
- Celery - Task queue
- Redis - Caching and message broker
- PostgreSQL - Database
- Pandas - Data manipulation
- Requests - HTTP client
- JWT - Authentication tokens
- pytest - Testing framework

## Development Tools Integration

The codebase integrates with several development tools:

- Black - Code formatting
- isort - Import sorting
- flake8 - Linting
- mypy - Type checking
- pre-commit - Git hooks
- Coverage - Test coverage reporting

## Extending the Codebase

When adding new functionality to FLOFIN:

1. Identify the appropriate app for the functionality
2. Create models, views, and serializers following existing patterns
3. Add tests for all new components
4. Update API documentation
5. Register any new tasks in the Celery configuration
6. Update relevant documentation

For more details on the development process, see [DEVELOPER_WORKFLOW.MD](DEVELOPER_WORKFLOW.MD).
