# Getting Started with FLOFIN

This guide will walk you through the process of setting up the FLOFIN development environment on your local machine and introduce you to the basic workflows.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Git** - For version control
- **Docker** and **Docker Compose** - For containerized development environment
- **Python 3.10+** - For local development (optional if using Docker exclusively)
- **Poetry** - For Python dependency management (optional if using Docker exclusively)
- **PostgreSQL** - For local database (optional if using Docker exclusively)
- **Redis** - For caching and message broker (optional if using Docker exclusively)

## Environment Setup

### 1. Clone the Repository

```bash
<NAME_EMAIL>:yourusername/flofin-be.git
cd flofin-be
```

### 2. Set Up Environment Variables

Copy the example environment file and modify as needed:

```bash
cp .env.example .env
```

Edit the `.env` file with your preferred text editor and update the values as needed.

### 3. Docker Setup (Recommended)

Build and start the Docker containers:

```bash
docker-compose up -d
```

This will start the following services:

- Django web application
- PostgreSQL database
- Redis
- Celery worker
- Celery beat scheduler

### 4. Local Setup (Alternative)

If you prefer to run the application locally without Docker:

#### Install dependencies

```bash
poetry install
```

#### Set up the database

Create a PostgreSQL database and update the `.env` file with your database credentials.

#### Run migrations

```bash
poetry run python manage.py migrate
```

#### Create a superuser

```bash
poetry run python manage.py createsuperuser
```

#### Start the development server

```bash
poetry run python manage.py runserver
```

#### Start Celery worker and beat scheduler

In separate terminal windows:

```bash
# Terminal 1: Start Celery worker
poetry run celery -A config worker -l info

# Terminal 2: Start Celery beat scheduler
poetry run celery -A config beat -l info
```

## Accessing the Application

Once the application is running, you can access:

- **API**: http://localhost:8000/api/
- **Admin Interface**: http://localhost:8000/admin/
- **API Documentation**: http://localhost:8000/api/docs/

## Running Tests

To run the test suite:

```bash
# With Docker
docker-compose run --rm web pytest

# Without Docker
poetry run pytest
```

To run specific tests:

```bash
# Run tests in a specific file
poetry run pytest tests/test_file.py

# Run tests in a specific directory
poetry run pytest tests/directory/

# Run tests with a specific marker
poetry run pytest -m marker_name
```

## Development Workflow

### Code Structure

Please refer to the [Code Structure Document](CODE_STRUCTURE.MD) for a detailed overview of the project organization.

### Database Structure

The database schema and relationships are documented in [Database Structure Document](DATABASE_STRUCTURE.MD).

### API Documentation

The API endpoints, request/response formats, and authentication details are available in the [API Documentation](API_DOCUMENTATION.MD).

### Making Changes

1. **Create a branch**: Always create a feature branch from `develop` for your changes.

   ```bash
   git checkout develop
   git pull
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**: Implement your feature or fix.

3. **Run tests**: Ensure all tests pass with your changes.

   ```bash
   poetry run pytest
   ```

4. **Lint your code**: Ensure your code follows the project's style guidelines.

   ```bash
   poetry run flake8
   poetry run black .
   poetry run isort .
   ```

5. **Commit your changes**: Use descriptive commit messages.

   ```bash
   git add .
   git commit -m "Add feature: your feature description"
   ```

6. **Push your branch**: Push your changes to the remote repository.

   ```bash
   git push -u origin feature/your-feature-name
   ```

7. **Create a pull request**: Create a pull request against the `develop` branch.

### Working with Migrations

To create and apply database migrations:

```bash
# Create migrations
poetry run python manage.py makemigrations

# Apply migrations
poetry run python manage.py migrate
```

### Debugging

For debugging issues, check:

1. **Application logs**: Available in Docker logs or console output
2. **Database**: Use the Django admin or a tool like pgAdmin
3. **Django Debug Toolbar**: Enabled in development mode
4. **Celery Flower**: For monitoring Celery tasks (http://localhost:5555 when enabled)

## Common Issues and Solutions

### Docker Issues

- **Container not starting**: Check Docker logs with `docker-compose logs service_name`
- **Port conflicts**: Change ports in `docker-compose.yml`
- **Volume permission issues**: Run `chmod -R 777 ./` in project directory (use with caution)

### Database Issues

- **Migration errors**: Try resetting migrations or check for conflicting migrations
- **Connection issues**: Verify database credentials in `.env`

### Dependency Issues

- **Dependency conflicts**: Update Poetry lock file with `poetry lock --no-update`
- **Missing dependencies**: Ensure `poetry install` or Docker rebuild has been done after changes

## Getting Help

If you encounter issues not covered in this guide:

1. Check the project documentation in the `docs/` directory
2. Ask a team member on the project Slack channel
3. Create an issue in the GitHub repository

## Next Steps

Now that you have your development environment set up, we recommend:

1. Familiarize yourself with the [Code Structure](CODE_STRUCTURE.MD)
2. Explore the [Database Structure](DATABASE_STRUCTURE.MD)
3. Review the [API Documentation](API_DOCUMENTATION.MD)
4. Try running the test suite to ensure everything is working
5. Make a small change to get familiar with the development workflow
