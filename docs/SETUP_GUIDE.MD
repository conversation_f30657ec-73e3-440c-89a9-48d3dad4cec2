# FLOFIN Development Environment Setup Guide

This guide will walk you through setting up the FLOFIN development environment on your local machine.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Docker** and **Docker Compose** (required for containerized development)
- **Git** (for version control)
- **Python 3.9+** (for local tooling)
- **PostgreSQL Client** (optional, for direct database access)
- **Redis CLI** (optional, for cache inspection)

## Getting the Code

1. Clone the repository:

```bash
git clone https://github.com/your-org/flofin-be.git
cd flofin-be
```

2. Set up Git hooks (optional but recommended):

```bash
cp scripts/pre-commit .git/hooks/
chmod +x .git/hooks/pre-commit
```

## Environment Configuration

1. Create your local environment file:

```bash
cp .env.example .env
```

2. Edit the `.env` file to configure:
   - Database connection settings
   - Redis connection settings
   - API keys for external services
   - Development-specific settings

## Docker Setup

The recommended way to run FLOFIN locally is using Docker Compose:

1. Build the Docker images:

```bash
docker-compose build
```

2. Start the services:

```bash
docker-compose up -d
```

This will start the following services:

- Django web application
- PostgreSQL database
- Redis for caching and message broker
- Celery workers
- Celery Beat scheduler

## Database Setup

Initialize the database with the following commands:

```bash
# Apply migrations
docker-compose exec web python manage.py migrate

# Create a superuser
docker-compose exec web python manage.py createsuperuser

# Load initial data (if available)
docker-compose exec web python manage.py loaddata initial_data
```

## Accessing the Application

- **Web Interface**: http://localhost:8000/
- **Admin Interface**: http://localhost:8000/admin/
- **API Documentation**: http://localhost:8000/api/docs/

## Development Workflow

### Code Structure

The FLOFIN codebase is organized as follows:

```
flofin-be/
├── apps/                  # Django applications
│   ├── crm/               # CRM integration
│   ├── customers/         # Customer management
│   ├── orders/            # Order processing
│   ├── products/          # Product management
│   ├── reports/           # Reporting functionality
│   └── alerts/            # Alert system
├── config/                # Project configuration
├── core/                  # Core functionality and utilities
├── docs/                  # Documentation
├── scripts/               # Utility scripts
├── static/                # Static files
├── templates/             # HTML templates
└── tests/                 # Test suite
```

### Running Tests

To run the test suite:

```bash
# Run all tests
docker-compose exec web python manage.py test

# Run specific tests
docker-compose exec web python manage.py test apps.orders.tests

# Run with coverage
docker-compose exec web coverage run --source='.' manage.py test
docker-compose exec web coverage report
```

### Code Quality Tools

We use several tools to maintain code quality:

```bash
# Run linting
docker-compose exec web flake8

# Run type checking
docker-compose exec web mypy .

# Run code formatting
docker-compose exec web black .
```

### Working with the Database

To connect to the database:

```bash
# Using the Django shell
docker-compose exec web python manage.py dbshell

# Using psql directly
docker-compose exec db psql -U postgres flofin
```

### Working with Celery

To monitor Celery tasks:

```bash
# View Celery logs
docker-compose logs -f celery

# Run a specific task
docker-compose exec web python manage.py shell
```

Then in the Python shell:

```python
from apps.crm.tasks import import_transactions
import_transactions.delay(source_id=1)
```

## Local Development Without Docker

While Docker is recommended, you can also set up a local development environment:

1. Create and activate a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:

```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

3. Set up the local database:

```bash
createdb flofin  # Using PostgreSQL client
python manage.py migrate
python manage.py createsuperuser
```

4. Run the development server:

```bash
python manage.py runserver
```

5. In a separate terminal, run Celery:

```bash
celery -A config worker -l INFO
celery -A config beat -l INFO
```

## Troubleshooting

### Common Issues

1. **Docker Networking Issues**:

   - Reset Docker network: `docker-compose down && docker-compose up -d`

2. **Database Migrations**:

   - If you encounter migration errors: `docker-compose exec web python manage.py makemigrations --merge`

3. **Celery Tasks Not Running**:

   - Check broker connection: `docker-compose exec web celery -A config inspect ping`
   - Check worker status: `docker-compose exec web celery -A config status`

4. **Permission Issues**:
   - Some files may require execute permissions: `chmod +x scripts/*.sh`

### Getting Help

If you encounter issues not covered in this guide:

1. Check the project wiki
2. Review existing GitHub issues
3. Contact the development team via Slack
4. Create a new GitHub issue with detailed information about your problem

## Development Best Practices

1. **Branch Strategy**:

   - Create feature branches from `develop`
   - Use the format: `feature/short-description` or `bugfix/issue-number`
   - Submit pull requests for review

2. **Commit Messages**:

   - Use clear, descriptive commit messages
   - Reference issue numbers when applicable

3. **Code Style**:

   - Follow PEP 8 guidelines
   - Use type hints
   - Write docstrings for all functions and classes

4. **Testing**:

   - Write tests for all new features
   - Maintain or improve test coverage
   - Include both unit and integration tests

5. **Documentation**:
   - Update documentation when changing functionality
   - Document complex algorithms or business logic
   - Use inline comments for non-obvious code

## Next Steps

After setting up your development environment:

1. Review the [Project Overview](PROJECT_OVERVIEW.MD)
2. Explore the [System Architecture](SYSTEM_ARCHITECTURE.MD)
3. Familiarize yourself with the [Database Structure](DATABASE_STRUCTURE.MD)
4. Complete the onboarding tasks assigned to you

Welcome to the FLOFIN development team!
