# FLOFIN Deployment Guide

This document provides instructions for deploying the FLOFIN backend application to different environments.

## Environment Overview

FLOFIN operates in three environments, each with its own configuration:

| Environment | Branch  | Host Server    | Database Server | Backend URL               | Frontend URL                  | PM2 ID | Path                             |
| ----------- | ------- | -------------- | --------------- | ------------------------- | ----------------------------- | ------ | -------------------------------- |
| Staging     | staging | *************  | *************   | staging.flofin.myrcvr.com | api.staging.flofin.myrcvr.com | 1      | /root/flofin/flofin-be           |
| Production  | main    | ************** | **************  | flofin.myrcvr.com         | api.flofin.myrcvr.com         | 18     | /root/charge-back/flofin         |
| Sandbox     | main    | ************** | **************  | sandbox.flofin.myrcvr.com | api.sandbox.flofin.myrcvr.com | 20     | /root/charge-back/flofin-sandbox |

## Prerequisites

Before deploying, ensure you have:

- SSH access to the respective servers
- Git access to the repository
- Python 3.10 installed on the server
- PM2 installed on the server
- Database access credentials

## Deployment Procedure

### Common Deployment Steps

The deployment process follows these general steps:

1. SSH into the server
2. Navigate to the project directory
3. Pull the latest code from the appropriate branch
4. Update dependencies if needed
5. Apply database migrations if needed
6. Restart the PM2 service

### Staging Environment Deployment

```bash
# 1. SSH into the staging server
ssh root@*************

# 2. Navigate to the project directory
cd /root/flofin/flofin-be

# 3. Pull the latest code from the staging branch
git pull origin staging

# 4. Update dependencies (if needed)
pip install -r requirements.txt

# 5. Apply database migrations (if needed)
python manage.py migrate

# 6. Restart the PM2 service
pm2 restart 1
```

### Production Environment Deployment

```bash
# 1. SSH into the production server
ssh root@**************

# 2. Navigate to the project directory
cd /root/charge-back/flofin

# 3. Pull the latest code from the main branch
git pull origin main

# 4. Update dependencies (if needed)
pip install -r requirements.txt

# 5. Apply database migrations (if needed)
python manage.py migrate

# 6. Restart the PM2 service
pm2 restart 18
```

### Sandbox Environment Deployment

```bash
# 1. SSH into the sandbox server (same as production)
ssh root@**************

# 2. Navigate to the project directory
cd /root/charge-back/flofin-sandbox

# 3. Pull the latest code from the main branch
git pull origin main

# 4. Update dependencies (if needed)
pip install -r requirements.txt

# 5. Apply database migrations (if needed)
python manage.py migrate

# 6. Restart the PM2 service
pm2 restart 20
```

## Post-Deployment Tasks

After deploying to any environment, it's recommended to:

1. Verify the application is running correctly:

   ```bash
   # Check PM2 status
   pm2 status

   # Check application logs
   pm2 logs <PM2_ID>
   ```

2. Run automated tasks (if necessary):

   ```bash
   # Example: Update Airtable data
   python manage.py all_cron update_airtable
   ```

3. Update scheduled tasks (if necessary):
   ```bash
   # Update crontab entries
   python manage.py crontab remove
   python manage.py crontab add
   ```

## Environment-Specific Considerations

### Staging Environment

- Used for testing new features before production deployment
- Database changes should be tested thoroughly
- Verify all API endpoints function correctly

### Production Environment

- Always make a backup before deploying
- Schedule deployments during off-peak hours
- Monitor the application closely after deployment
- Be prepared to rollback if issues are detected

### Sandbox Environment

- Used for isolated testing and demonstrations
- Changes here should not affect production
- Can be used to test database migrations

## Common Issues and Troubleshooting

### Application Not Starting

If the application fails to start after deployment:

1. Check the PM2 logs:

   ```bash
   pm2 logs <PM2_ID>
   ```

2. Verify the environment variables:

   ```bash
   # Check if .env file exists and has correct values
   cat .env
   ```

3. Check for Python errors:
   ```bash
   # Try running the application manually
   python manage.py runserver 0.0.0.0:8000
   ```

### Database Migration Issues

If database migrations fail:

1. Check the database connection:

   ```bash
   # Verify database settings in .env
   cat .env | grep DB_
   ```

2. Try running migrations manually with verbosity:

   ```bash
   python manage.py migrate --verbosity 2
   ```

3. Consider rolling back problematic migrations:
   ```bash
   python manage.py migrate <app_name> <previous_migration>
   ```

### PM2 Issues

If PM2 is not functioning correctly:

1. Check the PM2 process list:

   ```bash
   pm2 list
   ```

2. Try restarting PM2:

   ```bash
   pm2 kill
   pm2 resurrect
   ```

3. Verify the PM2 configuration:
   ```bash
   pm2 show <PM2_ID>
   ```

## Rollback Procedure

In case a deployment causes issues:

1. Identify the last stable commit:

   ```bash
   git log --oneline -10
   ```

2. Revert to the previous stable version:

   ```bash
   git checkout <commit_hash>
   ```

3. Restart the application:

   ```bash
   pm2 restart <PM2_ID>
   ```

4. If database migrations need to be reverted:
   ```bash
   python manage.py migrate <app_name> <previous_migration>
   ```

## Scheduled Maintenance

For scheduled maintenance:

1. Update the maintenance window in advance
2. Backup the database before major changes
3. Document all changes made during maintenance
4. Test thoroughly after maintenance is complete
