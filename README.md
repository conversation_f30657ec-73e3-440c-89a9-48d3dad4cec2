# FloFin Backend

## Installation and Setup

1. Create a virtual environment:

   ```bash
   python -m venv env
   ```

2. Activate the virtual environment:

   ```bash
   # On Linux/Mac
   source env/bin/activate

   # On Windows
   .\env\Scripts\activate
   ```

3. Install Django:

   ```bash
   python -m pip install Django
   ```

4. Upgrade pip:

   ```bash
   python -m pip install --upgrade pip
   ```

5. Install the required packages:

   ```bash
   pip install -r requirements.txt
   ```

## Environment Configuration

Add **.env** file in the root directory with the following variables:

```bash
# Authentication settings
TOKEN_TTL_MINUTES=int
REFRESH_TOKEN_TTL_MINUTES=int

# Database settings
DB_ENGINE=str          # e.g. mssql
DB_NAME=str            # Your database name
DB_HOST=str            # Database host
DB_PORT=int            # Database port (e.g. 1433 for SQL Server)
DB_USER=str            # Database username
DB_PASSWORD=str        # Database password
DB_DRIVER=str          # e.g. ODBC Driver 17 for SQL Server
DB_SCHEMA=str          # Database schema name

# Redis settings
REDIS_HOST=str         # Redis host
REDIS_PORT=int         # Redis port (default: 6379)
REDIS_DB=int           # Redis database number
REDIS_PASSWORD=str     # Redis password

# CORS and Host settings
CORS_ALLOWED_ORIGINS=str  # Comma-separated list of allowed origins
ALLOWED_HOSTS=str         # Comma-separated list of allowed hosts
```

### Environment Specific Configuration

#### Development Environment

- Set `DEBUG = True` in settings.py
- Use local database settings

#### Staging Environment

- Use staging database credentials
- Configure CORS settings for staging frontend
- Uncomment the CRONJOBS section in settings.py with appropriate schedule

#### Production Environment

- Set `DEBUG = False` in settings.py
- Set proper SECRET_KEY in production
- Use production database credentials
- Configure appropriate ALLOWED_HOSTS
- Enable Sentry for error tracking (uncomment the Sentry section in settings.py)

## Starting the Project

1. Make migrations:

   ```bash
   python manage.py makemigrations
   ```

2. Apply migrations:

   ```bash
   python manage.py migrate
   ```

3. Add crontab (if necessary):

   ```bash
   python manage.py crontab add
   ```

4. Run the server:

   ```bash
   python manage.py runserver 0.0.0.0:8000
   ```

## Running Backend Services

### Celery Worker (for background tasks)

The project uses Celery for background task processing, which requires Redis as a message broker.

1. Start the Redis server if it's not already running
2. Start the Celery worker:

   ```bash
   celery -A src worker -l info
   ```

### Django Server

Run the Django development server:

```bash
python manage.py runserver 0.0.0.0:8000
```

## Running Cronjobs

The project uses `all_cron.py` to manage various data processing cronjobs. Available tasks include:

- Updating Airtable data
- Processing card bin information
- Copying order import data
- Getting data from Konnektive
- Getting data from Sticky
- Refactoring duplicate records
- Updating fraud models

### How to Run Cronjobs

To run a specific cronjob task:

```bash
python manage.py all_cron [action] [start_date] [end_date]
```

Where:

- `action`: One of the following tasks:

  - `update_airtable`: Update data in Airtable
  - `card_bin`: Process card bin information
  - `copy_order_import`: Copy order import data
  - `get_knk`: Get data from Konnektive CRM
  - `get_sticky`: Get data from Sticky CRM
  - `refactor`: Refactor duplicate records
  - `update_fraud`: Update fraud models

- `start_date`: Optional start date in YYYY-MM-DD format (defaults to yesterday)
- `end_date`: Optional end date in YYYY-MM-DD format (defaults to today)

Example:

```bash
python manage.py all_cron get_knk 2023-01-01 2023-01-31
```

### Scheduling Cronjobs

To schedule recurring cronjobs with Django Crontab:

1. Configure your cronjobs in `settings.py`:

   ```python
   CRONJOBS = [
       ('20 * * * *', 'data.cron.Airtable.updateAirTable', '>> /path/to/logs/Airtable.log'),
       ('0 1 * * *', 'django.core.management.call_command', ['all_cron', 'get_knk']),
   ]
   ```

2. Add the crontab to your system:

   ```bash
   python manage.py crontab add
   ```

3. To display current cronjobs:

   ```bash
   python manage.py crontab show
   ```

4. To remove cronjobs:
   ```bash
   python manage.py crontab remove
   ```
