TOKEN_TTL_MINUTES=900
REFRESH_TOKEN_TTL_MINUTES=3600

# Database Configuration
DB_ENGINE=mssql
DB_NAME=flofin_db
DB_HOST=localhost
DB_PORT=1433
DB_USER=db_user
DB_PASSWORD=your_password
DB_DRIVER=ODBC Driver 17 for SQL Server

# Email Configuration
SMTP_SERVER=smtp.example.com
SMTP_PORT=465
SMTP_PASSWORD=your_smtp_password
SMTP_SENDERMAIL=<EMAIL>
EMAIL_RECIPIENT=<EMAIL>

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_redis_password

# CORS and Hosts Configuration
CORS_ALLOWED_ORIGINS="http://127.0.0.1:3000,http://127.0.0.1:8000,https://your-frontend-domain.com"
ALLOWED_HOSTS="127.0.0.1,localhost,your-domain.com"

# External Services
SENTRY_DSN="your_sentry_dsn"
AIRTABLE_PERSONAL_ACCESS_TOKEN="your_airtable_access_token"
AIRTABLE_DATABASE_ID="your_airtable_database_id"

# Environment
ENVIRONMENT="DEVELOPMENT"  # Options: DEVELOPMENT, STAGING, PRODUCTION
